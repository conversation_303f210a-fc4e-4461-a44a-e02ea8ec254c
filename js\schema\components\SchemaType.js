import { Dropdown } from "@wordpress/components";
import { useEffect, useState } from "@wordpress/element";
import { __ } from "@wordpress/i18n";
import dotProp from "dot-prop";
import { request } from '../functions';
import Hidden from './Fields/Hidden';
import Inserter from './Inserter';
import Property from './Property';

const SchemaType = ( { schemaID, schema } ) => {
	const [ properties, setProperties ] = useState( [] );
	const [ hiddenProperties, setHiddenProperties ] = useState( [] );
	const [ visibleProperties, setVisibleProperties ] = useState( [] );
	const [ removedProperties, setRemovedProperties ] = useState( [] );
	const { title, type, location } = schema;
	const fieldsStd = schema.fields || {};
	const base = `schemas[${ schemaID }]`;
	const removed = schema._removed ? schema._removed.split( ',' ) : [];

	useEffect( () => {
		request( 'types', { type } ).then( data => {
			//data is list fields type from php file
			let formattedData = data.map( property => {
				const _id = property.id;
				const visible = hasValue( property ) || property.required || ( property.show && !removed.includes( _id ) );
				let parentID = `${ base }[fields]`;
				parentID += property.name ? `[${ property.name }]` : '';
				const id = `${ parentID }[${ property.id }]`;
				const std = getDefaultValue( property );

				return { ...property, id, _id, std, parentID, visible, hidden: false };
			} );
			setProperties( formattedData );
		} );
	}, [ type ] );

	useEffect( () => {
		setVisibleProperties( () => properties.filter( property => property.visible && !property.hidden ) );
		setHiddenProperties( () => properties.filter( property => !property.visible && !property.hidden ) );
		setRemovedProperties( () => properties.filter( property => removed.includes( property._id ) ) );
	}, [ properties ] );

	const hasValue = property => {
		// Property without 'name' attribute.
		if ( !property.name ) {
			return fieldsStd.hasOwnProperty( property.id );
		}
		// Property has 'name' attribute.
		return fieldsStd[ property.name ] ? fieldsStd[ property.name ].hasOwnProperty( property.id ) : false;
	};

	const getDefaultValue = property => {
		const defaultValue = dotProp.get( property, 'std', '' );
		// Property without 'name' attribute.
		if ( !property.name ) {
			return dotProp.get( fieldsStd, property.id, defaultValue );
		}
		// Property has 'name' attribute.
		return dotProp.get( fieldsStd, `${ property.name }.${ property.id }`, defaultValue );
	};

	const handleSelect = ( e, onToggle ) => {
		onToggle();
		const id = e.target.dataset.id,
			selected = hiddenProperties.filter( item => item.id === id );
		setVisibleProperties( prev => [ ...prev, ...selected ] );
		setHiddenProperties( prev => prev.filter( item => item.id !== id ) );
	};

	const handleDelete = id => {
		const selected = visibleProperties.filter( item => item.id === id );
		setVisibleProperties( prev => prev.filter( item => item.id !== id ) );
		setHiddenProperties( prev => [ ...prev, ...selected ] );
		setRemovedProperties( prev => [ ...prev, ...selected ] );
	};

	const removedIds = removedProperties.map( p => p._id ).join( ',' );

	return (
		<>
			{ removedIds && <input type="hidden" name={ `${ base }[_removed]` } value={ removedIds } /> }
			<Hidden property={ { id: `${ base }[type]`, std: type } } />
			{
				visibleProperties.length > 0 && visibleProperties.map( item => <Property
					key={ item.id }
					deleteProp={ handleDelete }
					property={ item }
					setProperties={ setProperties }
				/> )
			}
			{
				![ 'CustomJsonLd', 'SearchAction', 'BreadcrumbList' ].includes( type ) && hiddenProperties.length > 0 && <Dropdown
					className="sss-dropdown"
					position="bottom right"
					renderToggle={ ( { onToggle } ) => (
						<a href="#" onClick={ e => { e.preventDefault(); onToggle(); } }>{ __( '+ Add Property', 'slim-seo-schema' ) }</a>
					) }
					renderContent={ ( { onToggle } ) => <Inserter items={ hiddenProperties } onSelect={ e => handleSelect( e, onToggle ) } /> }
				/>
			}
			{
				[ 'SearchAction', 'BreadcrumbList' ].includes( type ) && <p>{ __( 'This schema does not have any editable properties.', 'slim-seo-schema' ) }</p>
			}
		</>
	);
};

export default SchemaType;
