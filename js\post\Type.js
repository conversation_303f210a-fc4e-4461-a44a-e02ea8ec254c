import { Select } from "@elightup/form";
import { useEffect, useState } from "@wordpress/element";
import { __ } from "@wordpress/i18n";
import { request } from "../schema/functions";

export default function( { value, onChange } ) {
	const [ options, setOptions ] = useState( [] );

	useEffect( () => {
		request( 'data', { type: 'schemas' } ).then( setOptions );
	}, [] );

	return options.length > 0 && <Select
		id="schema-type"
		label={ __( 'Schema type', 'slim-seo-schema' ) }
		placeholder={ __( 'Inherit from global settings', 'slim-seo-schema' ) }
		options={ options }
		value={ value }
		onChange={ onChange }
	/>;
}
