import { Control } from "@elightup/form";
import { Dropdown } from "@wordpress/components";
import { memo, useEffect, useState } from "@wordpress/element";
import { __ } from "@wordpress/i18n";
import DeleteButton from '../Button/DeleteButton';
import Inserter from '../Inserter';
import { Panel } from '../Panel';
import Property from '../Property';

const Group = ( { property, deleteProp } ) => {
	const [ properties, setProperties ] = useState( [] );
	const [ visibleProperties, setVisibleProperties ] = useState( [] );
	const [ hiddenProperties, setHiddenProperties ] = useState( [] );

	const fieldsStd = property.std || {};
	const { id: groupID } = property;
	useEffect( () => {
		setProperties( () => property.fields.map( subProperty => {
			// Property is visible if it has default value or required.
			const visible = hasValue( subProperty ) || subProperty.required || subProperty.show;
			const parentID = subProperty.name ? `${ groupID }[${ subProperty.name }]` : `${ groupID }`;
			const id = `${ parentID }[${ subProperty.id }]`;
			const std = getDefaultValue( subProperty );
			return { ...subProperty, id, std, parentID, visible, hidden: false };
		} ) );
	}, [] );

	useEffect( () => {
		setVisibleProperties( () => properties.filter( p => p.visible && !p.hidden ) );
		setHiddenProperties( () => properties.filter( p => !p.visible && !p.hidden ) );
	}, [ properties ] );

	const hasValue = subProperty => {
		// Property without 'name' attribute.
		if ( !subProperty.name ) {
			return fieldsStd.hasOwnProperty( subProperty.id );
		}
		// Property has 'name' attribute.
		return fieldsStd[ subProperty.name ] ? fieldsStd[ subProperty.name ].hasOwnProperty( subProperty.id ) : false;
	};

	const getDefaultValue = subProperty => {
		const defaultValue = subProperty.std || '';
		// Property without 'name' attribute.
		if ( !subProperty.name ) {
			return fieldsStd[ subProperty.id ] || defaultValue;
		}
		// Property has 'name' attribute.
		if ( fieldsStd[ subProperty.name ] ) {
			return fieldsStd[ subProperty.name ][ subProperty.id ] || defaultValue;
		}
		return defaultValue;
	};

	const handleSelect = ( e, onToggle ) => {
		onToggle();
		const id = e.target.dataset.id;
		let selected = hiddenProperties.filter( field => field.id === id );
		selected = selected.map( item => ( { ...item, std: '' } ) );
		setVisibleProperties( prev => [ ...prev, ...selected ] );
		setHiddenProperties( prev => prev.filter( item => item.id !== id ) );
	};

	const handleDelete = ( id ) => {
		const selected = visibleProperties.filter( field => field.id === id );
		setVisibleProperties( prev => prev.filter( item => item.id !== id ) );
		setHiddenProperties( prev => [ ...prev, ...selected ] );
	};

	const { cloneable, cloneItemHeading, label, tooltip, panelNoBorder } = property;

	const groupHeading  = cloneItemHeading || '';
	const required      = property.required && !cloneable;
	const propertyLabel = !cloneable && label;
	const panelClass    = !cloneable || panelNoBorder ? 'sss-panel--no-border' : '';

	return (
		<Control label={ propertyLabel } id={ property.id } required={ required } tooltip={ tooltip }>
			<Panel title={ groupHeading } className={ panelClass } required={ required } defaultExpanded={ true } deleteProp={ deleteProp } id={ property.id }>
				{
					visibleProperties.map( field => <Property key={ field.id } property={ field } setProperties={ setProperties } properties={ properties } deleteProp={ handleDelete } /> )
				}
				{
					Object.keys( hiddenProperties ).length > 0 && <Dropdown
						className="sss-dropdown"
						position="bottom right"
						renderToggle={ ( { onToggle } ) => (
							<a href="#" onClick={ e => { e.preventDefault(); onToggle(); } }>{ __( '+ Add Property', 'slim-seo-schema' ) }</a>
						) }
						renderContent={ ( { onToggle } ) => <Inserter items={ hiddenProperties } onSelect={ e => handleSelect( e, onToggle ) } /> }
					/>
				}
				{ required || <DeleteButton id={ groupID } deleteProp={ deleteProp } /> }
			</Panel>
		</Control>
	);
};

export default memo( Group );
