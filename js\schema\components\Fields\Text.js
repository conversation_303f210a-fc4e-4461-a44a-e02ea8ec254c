import { Control } from "@elightup/form";
import { memo, useRef } from "@wordpress/element";
import DeleteButton from '../Button/DeleteButton';
import PropInserter from "./PropInserter";

const Text = ( { className, property, deleteProp, onChange } ) => {
	const inputRef = useRef();
	const { id, label, required, std, tooltip } = property;

	return (
		<Control className={ className } { ...property }>
			<div className="sss-input-wrapper">
				<input type="text" id={ id } name={ id } defaultValue={ std } ref={ inputRef } onChange={ onChange } />
				<PropInserter inputRef={ inputRef } />
			</div>
			{ required || <DeleteButton id={ id } deleteProp={ deleteProp } /> }
		</Control>
	);
};

export default memo( Text, ( prevProps, nextProps ) => prevProps.property.id === nextProps.property.id );
