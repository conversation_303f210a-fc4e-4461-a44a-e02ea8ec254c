<?php
namespace SlimSEOPro\Schema\SchemaTypes;

$aggregate_ratings                     = Helper::get_property( 'aggregateRating', [ 'show' => true ] );
$aggregate_ratings['fields'][0]['std'] = '{{ product.rating }}';
$aggregate_ratings['fields'][2]['std'] = '{{ product.review_count }}';

return [
	[
		'id'   => 'googleDocs',
		'type' => 'GoogleDocs',
		'url'  => 'https://developers.google.com/search/docs/appearance/structured-data/product-variants',
		'show' => true,
	],
	Helper::get_property( 'name', [
		'required' => true,
		'tooltip'  => __( 'The name of the Product group', 'slim-seo-schema' ),
	] ),
	[
		'label'   => __( 'Variants', 'slim-seo-schema' ),
		'id'      => 'hasVariant',
		'show'    => true,
		'tooltip' => __( 'Indicates a product that is a member of the product group', 'slim-seo-schema' ),
		'type'    => 'Group',
		'fields'  => [
			[
				'id'       => '@type',
				'std'      => 'Product',
				'type'     => 'Hidden',
				'required' => true,
			],
			[
				'label'   => __( 'SKU', 'slim-seo-schema' ),
				'id'      => 'sku',
				'show'    => true,
				'std'     => '{{ product.variants.sku }}',
				'tooltip' => __( 'The Stock Keeping Unit (SKU), i.e. a merchant-specific identifier for a product or service, or the product to which the offer refers.', 'slim-seo-schema' ),
			],
			Helper::get_property( 'name', [
				'required' => true,
				'std'      => '{{ product.variants.name }}',
				'tooltip'  => __( 'The name of the product', 'slim-seo-schema' ),
			] ),
			Helper::get_property( 'description', [
				'show'    => true,
				'std'     => '{{ product.variants.description }}',
				'tooltip' => __( 'The product description', 'slim-seo-schema' ),
			] ),
			[
				'label'   => __( 'Image', 'slim-seo-schema' ),
				'id'      => 'image',
				'show'    => true,
				'std'     => '[{{ product.variants.image }}]',
				'tooltip' => __( 'The URL of a product photo.', 'slim-seo-schema' ),
			],
			[
				'label'   => __( 'Size', 'slim-seo-schema' ),
				'id'      => 'size',
				'show'    => false,
				'std'     => '{{ product.variants.size }}',
				'tooltip' => __( 'A standardized size of a product or creative work, specified either through a simple textual string (for exp. "XL", "32Wx34L").', 'slim-seo-schema' ),
			],
			[
				'label'   => __( 'Color', 'slim-seo-schema' ),
				'id'      => 'color',
				'std'     => '{{ product.variants.color }}',
				'tooltip' => __( 'The color of the product.', 'slim-seo-schema' ),
			],
			[
				'label'   => __( 'GTIN', 'slim-seo-schema' ),
				'id'      => 'gtin',
				'tooltip' => __( 'A Global Trade Item Number (GTIN). GTINs identify trade items, including products and services, using numeric identification codes.', 'slim-seo-schema' ),
			],
			[
				'label'   => __( 'GTIN-8', 'slim-seo-schema' ),
				'id'      => 'gtin8',
				'tooltip' => __( 'The GTIN-8 code of the product, or the product to which the offer refers. This code is also known as EAN/UCC-8 or 8-digit EAN.', 'slim-seo-schema' ),
			],
			[
				'label'   => __( 'GTIN-12', 'slim-seo-schema' ),
				'id'      => 'gtin12',
				'tooltip' => __( 'The GTIN-12 code of the product, or the product to which the offer refers. The GTIN-12 is the 12-digit GS1 Identification Key composed of a U.P.C. Company Prefix, Item Reference, and Check Digit used to identify trade items.', 'slim-seo-schema' ),
			],
			[
				'label'   => __( 'GTIN-13', 'slim-seo-schema' ),
				'id'      => 'gtin13',
				'tooltip' => __( 'The GTIN-13 code of the product, or the product to which the offer refers. This is equivalent to 13-digit ISBN codes and EAN UCC-13. Former 12-digit UPC codes can be converted into a GTIN-13 code by simply adding a preceding zero.', 'slim-seo-schema' ),
			],
			[
				'label'   => __( 'GTIN-14', 'slim-seo-schema' ),
				'id'      => 'gtin14',
				'tooltip' => __( 'The GTIN-14 code of the product, or the product to which the offer refers.', 'slim-seo-schema' ),
			],
			[
				'id'     => 'offers',
				'type'   => 'Group',
				'label'  => __( 'Offers', 'slim-seo-schema' ),
				'show'   => true,
				'fields' => [
					[
						'id'          => '@type',
						'label'       => __( 'Type', 'slim-seo-schema' ),
						'type'        => 'DataList',
						'required'    => true,
						'std'         => 'Offer',
						'dependant'   => true,
						'placeholder' => __( 'None', 'slim-seo-schema' ),
						'options'     => [
							'Offer'          => __( 'Offer', 'slim-seo-schema' ),
							'AggregateOffer' => __( 'Aggregate Offer', 'slim-seo-schema' ),
						],
					],
					[
						'id'         => 'price',
						'label'      => __( 'Price', 'slim-seo-schema' ),
						'required'   => true,
						'tooltip'    => __( 'The offer price of a product', 'slim-seo-schema' ),
						'dependency' => '[@type]:Offer',
						'std'        => '{{ product.variants.price }}',
					],
					[
						'id'       => 'priceCurrency',
						'label'    => __( 'Price currency', 'slim-seo-schema' ),
						'required' => true,
						'std'      => '{{ product.currency }}',
					],
					[
						'id'      => 'priceValidUntil',
						'label'   => __( 'Price valid until', 'slim-seo-schema' ),
						'type'    => 'Date',
						'tooltip' => __( 'The date (in ISO 8601 date format) after which the price will no longer be available', 'slim-seo-schema' ),
						'std'     => '{{ product.sale_to }}',
					],
					[
						'id'      => 'availability',
						'label'   => __( 'Availability', 'slim-seo-schema' ),
						'show'    => true,
						'std'     => '{{ product.variants.stock }}',
						'tooltip' => __( 'The possible product availability options', 'slim-seo-schema' ),
					],
					Helper::get_property( 'url', [
						'show'    => true,
						'tooltip' => __( 'The URL of the product', 'slim-seo-schema' ),
						'std'     => '{{ product.variants.url }}',
					] ),
					[
						'id'      => 'shippingDetails',
						'label'   => __( 'Shipping details', 'slim-seo-schema' ),
						'type'    => 'Group',
						'tooltip' => __( 'Nested information about the shipping policies and options associated with an Offer', 'slim-seo-schema' ),
						'fields'  => [
							[
								'id'       => '@type',
								'std'      => 'OfferShippingDetails',
								'type'     => 'Hidden',
								'required' => true,
							],
							[
								'id'       => 'shippingDestination',
								'label'    => __( 'Destination', 'slim-seo-schema' ),
								'type'     => 'Group',
								'required' => true,
								'fields'   => [
									[
										'id'       => '@type',
										'std'      => 'DefinedRegion',
										'type'     => 'Hidden',
										'required' => true,
									],
									[
										'id'       => 'addressCountry',
										'label'    => __( 'Country code', 'slim-seo-schema' ),
										'tooltip'  => __( 'The 2-digit country code, in ISO 3166-1 format', 'slim-seo-schema' ),
										'required' => true,
									],
									[
										'id'    => 'addressRegion',
										'label' => __( 'Region', 'slim-seo-schema' ),
										'show'  => true,
									],
								],
							],
							[
								'id'      => 'deliveryTime',
								'label'   => __( 'Delivery time', 'slim-seo-schema' ),
								'type'    => 'Group',
								'tooltip' => __( 'The total delay between the receipt of the order and the goods reaching the final customer', 'slim-seo-schema' ),
								'fields'  => [
									[
										'id'       => '@type',
										'std'      => 'ShippingDeliveryTime',
										'type'     => 'Hidden',
										'required' => true,
									],
									[
										'id'      => 'handlingTime',
										'label'   => __( 'Handling time', 'slim-seo-schema' ),
										'type'    => 'Group',
										'tooltip' => __( 'The typical delay between the receipt of the order and the goods either leaving the warehouse or being prepared for pickup, in case the delivery method is on site pickup', 'slim-seo-schema' ),
										'fields'  => [
											[
												'id'       => '@type',
												'std'      => 'QuantitativeValue',
												'type'     => 'Hidden',
												'required' => true,
											],
											[
												'id'    => 'minValue',
												'label' => __( 'Min value', 'slim-seo-schema' ),
												'show'  => true,
											],
											[
												'id'    => 'maxValue',
												'label' => __( 'Max value', 'slim-seo-schema' ),
												'show'  => true,
											],
										],
									],
									[
										'id'      => 'transitTime',
										'label'   => __( 'Transit time', 'slim-seo-schema' ),
										'type'    => 'Group',
										'tooltip' => __( 'The typical delay the order has been sent for delivery and the goods reach the final customer.', 'slim-seo-schema' ),
										'fields'  => [
											[
												'id'       => '@type',
												'std'      => 'QuantitativeValue',
												'type'     => 'Hidden',
												'required' => true,
											],
											[
												'id'    => 'minValue',
												'label' => __( 'Min value', 'slim-seo-schema' ),
												'show'  => true,
											],
											[
												'id'    => 'maxValue',
												'label' => __( 'Max value', 'slim-seo-schema' ),
												'show'  => true,
											],
										],
									],
									[
										'id'      => 'cutOffTime',
										'label'   => __( 'Cutoff time', 'slim-seo-schema' ),
										'show'    => true,
										'tooltip' => __( 'The time after which new orders are no longer processed on that same day, in ISO 8601 format. One day gets added to the handling time.', 'slim-seo-schema' ),
									],
									Helper::get_property( 'OpeningHoursSpecification', [
										'id'               => 'businessDays',
										'label'            => __( 'Business days', 'slim-seo-schema' ),
										'cloneItemHeading' => __( 'Business days', 'slim-seo-schema' ),
									] ),
								],
							],
							[
								'id'      => 'doesNotShip',
								'label'   => __( 'Does Not Ship?', 'slim-seo-schema' ),
								'type'    => 'DataList',
								'options' => [
									'true'  => __( 'Yes', 'slim-seo-schema' ),
									'false' => __( 'No', 'slim-seo-schema' ),
								],
							],
							[
								'id'      => 'shippingRate',
								'label'   => __( 'Shipping rate', 'slim-seo-schema' ),
								'type'    => 'Group',
								'tooltip' => __( 'Information about the cost of shipping to the specified destination', 'slim-seo-schema' ),
								'fields'  => [
									[
										'id'       => '@type',
										'std'      => 'MonetaryAmount',
										'type'     => 'Hidden',
										'required' => true,
									],
									[
										'id'    => 'currency',
										'label' => __( 'Currency', 'slim-seo-schema' ),
										'show'  => true,
									],
									[
										'id'    => 'value',
										'label' => __( 'Value', 'slim-seo-schema' ),
										'show'  => true,
									],
								],
							],
						],
					],
					[
						'id'      => 'acceptedPaymentMethod',
						'label'   => __( 'Accepted payment method', 'slim-seo-schema' ),
						'tooltip' => __( 'The payment method(s) accepted by seller for this offer.', 'slim-seo-schema' ),
						'type'    => 'DataList',
						'std'     => 'http://purl.org/goodrelations/v1#Cash',
						'options' => [
							'http://purl.org/goodrelations/v1#ByBankTransferInAdvance' => __( 'By bank transfer in advance', 'slim-seo-schema' ),
							'http://purl.org/goodrelations/v1#ByInvoice'               => __( 'By invoice', 'slim-seo-schema' ),
							'http://purl.org/goodrelations/v1#Cash'                    => __( 'Cash', 'slim-seo-schema' ),
							'http://purl.org/goodrelations/v1#CheckInAdvance'          => __( 'Check in advance', 'slim-seo-schema' ),
							'http://purl.org/goodrelations/v1#COD'                     => __( 'COD', 'slim-seo-schema' ),
							'http://purl.org/goodrelations/v1#DirectDebit'             => __( 'Direct debit', 'slim-seo-schema' ),
							'http://purl.org/goodrelations/v1#GoogleCheckout'          => __( 'Google checkout', 'slim-seo-schema' ),
							'http://purl.org/goodrelations/v1#PayPal'                  => __( 'PayPal', 'slim-seo-schema' ),
							'http://purl.org/goodrelations/v1#PaySwarm'                => __( 'PaySwarm', 'slim-seo-schema' ),
						],
					],
					[
						'id'          => 'addOn',
						'label'       => __( 'Add on', 'slim-seo-schema' ),
						'tooltip'     => __( 'An additional offer that can only be obtained in combination with the first base offer (e.g. supplements and extensions that are available for a surcharge).', 'slim-seo-schema' ),
						'description' => __( 'Please create another Offer schema and link to this property via a dynamic variable', 'slim-seo-schema' ),
					],
					Helper::get_property( 'duration', [
						'id'      => 'advanceBookingRequirement',
						'label'   => __( 'Advance booking requirement', 'slim-seo-schema' ),
						'type'    => 'Group',
						'tooltip' => __( 'The amount of time that is required between accepting the offer and the actual usage of the resource or service.', 'slim-seo-schema' ),
					] ),
					Helper::get_property( 'aggregateRating', [
						'tooltip' => __( 'The overall rating, based on a collection of reviews or ratings, of the offer.', 'slim-seo-schema' ),
					] ),
					[
						'id'      => 'areaServed',
						'label'   => __( 'Area served', 'slim-seo-schema' ),
						'tooltip' => __( 'The geographic area where a service or offered item is provided.', 'slim-seo-schema' ),
					],
					[
						'id'      => 'asin',
						'label'   => __( 'Asin', 'slim-seo-schema' ),
						'tooltip' => __( 'An Amazon Standard Identification Number (ASIN) is a 10-character alphanumeric unique identifier assigned by Amazon.com and its partners for product identification within the Amazon organization. See documentation from Amazon for authoritative details', 'slim-seo-schema' ),
					],
					[
						'id'      => 'availabilityStarts',
						'label'   => __( 'Availability starts', 'slim-seo-schema' ),
						'tooltip' => __( 'The beginning of the availability of the product or service included in the offer.', 'slim-seo-schema' ),
						'type'    => 'Date',
					],
					[
						'id'      => 'availabilityEnds',
						'label'   => __( 'Availability ends', 'slim-seo-schema' ),
						'tooltip' => __( 'The end of the availability of the product or service included in the offer.', 'slim-seo-schema' ),
						'type'    => 'Date',
					],
					[
						'id'      => 'availableDeliveryMethod',
						'label'   => __( 'Available delivery method', 'slim-seo-schema' ),
						'type'    => 'DataList',
						'options' => [
							'http://purl.org/goodrelations/v1#DeliveryModeDirectDownload' => __( 'Direct download', 'slim-seo-schema' ),
							'http://purl.org/goodrelations/v1#DeliveryModeFreight'        => __( 'Freight', 'slim-seo-schema' ),
							'http://purl.org/goodrelations/v1#DeliveryModeMail'           => __( 'Mail', 'slim-seo-schema' ),
							'http://purl.org/goodrelations/v1#DeliveryModeOwnFleet'       => __( 'Own fleet', 'slim-seo-schema' ),
							'http://purl.org/goodrelations/v1#DeliveryModePickUp'         => __( 'Pick up', 'slim-seo-schema' ),
							'http://purl.org/goodrelations/v1#DHL'                        => __( 'DHL', 'slim-seo-schema' ),
							'http://purl.org/goodrelations/v1#FederalExpress'             => __( 'Federal express', 'slim-seo-schema' ),
							'http://purl.org/goodrelations/v1#UPS'                        => __( 'UPS', 'slim-seo-schema' ),
						],
					],
					[
						'label'   => __( 'Category', 'slim-seo-schema' ),
						'id'      => 'category',
						'tooltip' => __( 'A category for the item. Greater signs or slashes can be used to informally indicate a category hierarchy.', 'slim-seo-schema' ),
					],
					[
						'label'   => __( 'Checkout page URL template', 'slim-seo-schema' ),
						'id'      => 'checkoutPageURLTemplate',
						'tooltip' => __( 'A URL template (RFC 6570) for a checkout page for an offer.', 'slim-seo-schema' ),
					],
					Helper::get_property( 'description', [
						'std'     => '',
						'show'    => false,
						'tooltip' => __( 'A description of the offer.', 'slim-seo-schema' ),
					] ),
					[
						'label'   => __( 'Disambiguating description', 'slim-seo-schema' ),
						'id'      => 'disambiguatingDescription',
						'tooltip' => __( 'A sub property of description. A short description of the item used to disambiguate from other.', 'slim-seo-schema' ),
					],
					[
						'id'      => 'eligibleCustomerType',
						'label'   => __( 'Eligible customer type', 'slim-seo-schema' ),
						'tooltip' => __( 'The type(s) of customers for which the given offer is valid.', 'slim-seo-schema' ),
						'type'    => 'DataList',
						'std'     => 'http://purl.org/goodrelations/v1#Business',
						'options' => [
							'http://purl.org/goodrelations/v1#Business'          => __( 'Business', 'slim-seo-schema' ),
							'http://purl.org/goodrelations/v1#Enduser'           => __( 'End-user', 'slim-seo-schema' ),
							'http://purl.org/goodrelations/v1#PublicInstitution' => __( 'Public institution', 'slim-seo-schema' ),
							'http://purl.org/goodrelations/v1#Reseller'          => __( 'Reseller', 'slim-seo-schema' ),
						],
					],
					Helper::get_property( 'duration', [
						'id'      => 'eligibleDuration',
						'label'   => __( 'Eligible duration', 'slim-seo-schema' ),
						'type'    => 'Group',
						'tooltip' => __( 'The duration for which the given offer is valid.', 'slim-seo-schema' ),
					] ),
					Helper::get_property( 'QuantitativeValue', [
						'id'      => 'eligibleQuantity',
						'label'   => __( 'Eligible quantity', 'slim-seo-schema' ),
						'type'    => 'Group',
						'tooltip' => __( 'The interval and unit of measurement of ordering quantities for which the offer or price specification is valid. This allows e.g. specifying that a certain freight charge is valid only for a certain quantity.', 'slim-seo-schema' ),
					] ),
					[
						'id'        => 'eligibleRegion',
						'label'     => __( 'Eligible region', 'slim-seo-schema' ),
						'tooltip'   => __( 'The ISO 3166-1 (ISO 3166-1 alpha-2) or ISO 3166-2 code, the place, or the GeoShape for the geo-political region(s) for which the offer or delivery charge specification is valid.', 'slim-seo-schema' ),
						'cloneable' => true,
					],
					[
						'id'      => 'eligibleTransactionVolume',
						'label'   => __( 'Eligible transaction volume', 'slim-seo-schema' ),
						'tooltip' => __( 'The transaction volume, in a monetary unit, for which the offer or price specification is valid.', 'slim-seo-schema' ),
						'type'    => 'Group',
						'fields'  => [
							[
								'id'       => '@type',
								'std'      => 'PriceSpecification',
								'type'     => 'Hidden',
								'required' => true,
							],
							[
								'id'       => 'price',
								'label'    => __( 'Price', 'slim-seo-schema' ),
								'tooltip'  => __( 'The price amount for the specified offer.', 'slim-seo-schema' ),
								'required' => true,
							],
							[
								'id'       => 'priceCurrency',
								'label'    => __( 'Price currency', 'slim-seo-schema' ),
								'tooltip'  => __( 'The currency of the price for the specified offer.', 'slim-seo-schema' ),
								'required' => true,
							],
						],
					],
					[
						'id'      => 'hasAdultConsideration',
						'label'   => __( 'Has adult consideration', 'slim-seo-schema' ),
						'tooltip' => __( 'Used to tag an item to be intended or suitable for consumption or use by adults only.', 'slim-seo-schema' ),
						'type'    => 'DataList',
						'options' => [
							'https://schema/org/AlcoholConsideration'                     => 'Alcohol',
							'https://schema/org/DangerousGoodConsideration'               => 'Dangerous good',
							'https://schema/org/HealthcareConsideration'                  => 'Healthcare',
							'https://schema/org/NarcoticConsideration'                    => 'Narcotic',
							'https://schema/org/ReducedRelevanceForChildrenConsideration' => 'Reduced relevance for children',
							'https://schema/org/SexualContentConsideration'               => 'Sexual content',
							'https://schema/org/TobaccoNicotineConsideration'             => 'Tobacco nicotine',
							'https://schema/org/UnclassifiedAdultConsideration'           => 'Unclassified adult',
							'https://schema/org/ViolenceConsideration'                    => 'Violence',
							'https://schema/org/WeaponConsideration'                      => 'Weapon',

						],
					],
					[
						'id'      => 'isFamilyFriendly',
						'label'   => __( 'Is family friendly', 'slim-seo-schema' ),
						'tooltip' => __( 'Indicates whether this content is family friendly.', 'slim-seo-schema' ),
						'type'    => 'DataList',
						'std'     => 'True',
						'options' => [
							'True'  => __( 'True', 'slim-seo-schema' ),
							'False' => __( 'False', 'slim-seo-schema' ),
						],
					],
					Helper::get_property( 'duration', [
						'id'      => 'leaseLength',
						'label'   => __( 'Lease length', 'slim-seo-schema' ),
						'type'    => 'Group',
						'tooltip' => __( 'Length of the lease for some Accommodation, either particular to some Offer or in some cases intrinsic to the property.', 'slim-seo-schema' ),
					] ),
					[
						'label'   => __( 'Mobile URL', 'slim-seo-schema' ),
						'id'      => 'mobileUrl',
						'tooltip' => __( 'Provided for specific situations in which data consumers need to determine whether one of several provided URLs is a dedicated \'mobile site\'. The property is expected only on Product and Offer, rather than Thing.', 'slim-seo-schema' ),
					],
					[
						'label'   => __( 'MPN', 'slim-seo-schema' ),
						'id'      => 'mpn',
						'tooltip' => __( 'The Manufacturer Part Number (MPN) of the product, or the product to which the offer refers', 'slim-seo-schema' ),
					],
					[
						'id'          => 'offeredBy',
						'label'       => __( 'Offered by', 'slim-seo-schema' ),
						'tooltip'     => __( 'A pointer to the organization or person making the offer.', 'slim-seo-schema' ),
						'description' => __( 'Please create a Person or an Organization schema and link to this property via a dynamic variable', 'slim-seo-schema' ),
					],
					[
						'id'          => 'seller',
						'label'       => __( 'Seller', 'slim-seo-schema' ),
						'tooltip'     => __( 'An entity which offers (sells / leases / lends / loans) the services / goods. A seller may also be a provider.', 'slim-seo-schema' ),
						'description' => __( 'Please create a Person or an Organization schema and link to this property via a dynamic variable', 'slim-seo-schema' ),
					],
					[
						'label'   => __( 'Serial number', 'slim-seo-schema' ),
						'id'      => 'serialNumber',
						'tooltip' => __( 'The serial number or any alphanumeric identifier of a particular product. When attached to an offer, it is a shortcut for the serial number of the product included in the offer.', 'slim-seo-schema' ),
					],
					[
						'label'   => __( 'SKU', 'slim-seo-schema' ),
						'id'      => 'sku',
						'tooltip' => __( 'The Stock Keeping Unit (SKU), i.e. a merchant-specific identifier for a product or service, or the product to which the offer refers.', 'slim-seo-schema' ),
					],
					[
						'id'      => 'validFrom',
						'label'   => __( 'Valid from', 'slim-seo-schema' ),
						'type'    => 'Date',
						'tooltip' => __( 'The date when the item becomes valid in ISO-8601 format', 'slim-seo-schema' ),
					],
					[
						'id'      => 'validThrough',
						'label'   => __( 'Valid through', 'slim-seo-schema' ),
						'tooltip' => __( 'The date after when the item is not valid. For example the end of an offer, salary period, or a period of opening hours. ', 'slim-seo-schema' ),
					],
					[
						'id'         => 'lowPrice',
						'label'      => __( 'Low price', 'slim-seo-schema' ),
						'required'   => true,
						'tooltip'    => __( 'The lowest price of all offers available. Use a floating point number.', 'slim-seo-schema' ),
						'dependency' => '[@type]:AggregateOffer',
						'std'        => '{{ product.low_price }}',
					],
					[
						'id'         => 'highPrice',
						'label'      => __( 'High price', 'slim-seo-schema' ),
						'dependency' => '[@type]:AggregateOffer',
						'std'        => '{{ product.high_price }}',
						'show'       => true,
					],
					[
						'id'         => 'offerCount',
						'label'      => __( 'Offer count', 'slim-seo-schema' ),
						'tooltip'    => __( 'The number of offers for the product.', 'slim-seo-schema' ),
						'dependency' => '[@type]:AggregateOffer',
						'std'        => '{{ product.offer_count }}',
						'show'       => true,
					],
				],
			],
		],
	],
	[
		'label'    => __( 'Product group ID', 'slim-seo-schema' ),
		'id'       => 'productGroupID',
		'std'      => '{{ product.sku }}',
		'required' => true,
		'tooltip'  => __( 'Indicates a textual identifier for a ProductGroup.', 'slim-seo-schema' ),
	],
	[
		'id'       => 'variesBy',
		'label'    => __( 'Varies by', 'slim-seo-schema' ),
		'tooltip'  => __( 'Aspects by which the variants in the Product groupGroup vary, (for exp. size or color), if applicable. Reference these variant-identifying properties through their full Schema.org URL (for exp. https://schema.org/color)', 'slim-seo-schema' ),
		'type'     => 'ReactSelect',
		'required' => true,
		'std'      => 'WebApplication',
		'options'  => [
			'https://schema.org/color'           => __( 'https://schema.org/color', 'slim-seo-schema' ),
			'https://schema.org/size'            => __( 'https://schema.org/size', 'slim-seo-schema' ),
			'https://schema.org/suggestedAge'    => __( 'https://schema.org/suggestedAge', 'slim-seo-schema' ),
			'https://schema.org/suggestedGender' => __( 'https://schema.org/suggestedGender', 'slim-seo-schema' ),
			'https://schema.org/material'        => __( 'https://schema.org/material', 'slim-seo-schema' ),
			'https://schema.org/pattern'         => __( 'https://schema.org/pattern', 'slim-seo-schema' ),
		],
	],

	Helper::get_property( 'description', [
		'show'    => true,
		'tooltip' => __( 'the Product group description', 'slim-seo-schema' ),
	] ),
	[
		'label'   => __( 'Brand name', 'slim-seo-schema' ),
		'id'      => 'brand',
		'show'    => true,
		'tooltip' => __( 'The brand of the Product group', 'slim-seo-schema' ),
		'type'    => 'Group',
		'fields'  => [
			[
				'id'       => '@type',
				'std'      => 'Brand',
				'type'     => 'Hidden',
				'required' => true,
			],
			[
				'id'   => 'name',
				'std'  => '{{ site.title }}',
				'show' => true,
			],
		],
	],
	Helper::get_property( 'url', [
		'show'    => true,
		'tooltip' => __( 'The URL of the Product group', 'slim-seo-schema' ),
		'std'     => '{{ post.url }}',
	] ),
	[
		'label'   => __( 'SKU', 'slim-seo-schema' ),
		'id'      => 'sku',
		'show'    => true,
		'std'     => '{{ product.sku }}',
		'tooltip' => __( 'The Stock Keeping Unit (SKU), i.e. a merchant-specific identifier for a product or service, or the Product group to which the offer refers.', 'slim-seo-schema' ),
	],
	Helper::get_property( 'image', [
		'show'    => true,
		'tooltip' => __( 'The URL of a product photo', 'slim-seo-schema' ),
		'std'     => [
			'{{ post.thumbnail }}',
		],
	] ),
	Helper::get_property( 'Review', [
		'fields' => [
			[
				'id' => '@type',
			],
			[
				'id' => 'author',
			],
			[
				'id' => 'reviewRating',
			],
			[
				'id' => 'datePublished',
			],
			[
				'id'      => 'positiveNotes',
				'type'    => 'Group',
				'label'   => __( 'Positive notes (Pros)', 'slim-seo-schema' ),
				'tooltip' => __( 'A list of positive statements about the Product group, listed in a specific order', 'slim-seo-schema' ),
				'show'    => true,
				'fields'  => [
					[
						'id'       => '@type',
						'std'      => 'ItemList',
						'type'     => 'Hidden',
						'required' => true,
					],
					[
						'id'        => 'itemListElement',
						'type'      => 'Group',
						'cloneable' => true,
						'show'      => true,
						'fields'    => [
							[
								'id'       => '@type',
								'std'      => 'ListItem',
								'type'     => 'Hidden',
								'required' => true,
							],
							Helper::get_property( 'name', [
								'std'      => '',
								'tooltip'  => __( 'The key statement of the review.', 'slim-seo-schema' ),
								'required' => true,
							] ),
							[
								'id'      => 'position',
								'label'   => __( 'Position', 'slim-seo-schema' ),
								'show'    => true,
								'tooltip' => __( 'The position (order) of the statement in the list.', 'slim-seo-schema' ),
							],
						],
					],
				],
			],
			[
				'id'      => 'negativeNotes',
				'type'    => 'Group',
				'label'   => __( 'Negative notes (Cons)', 'slim-seo-schema' ),
				'tooltip' => __( 'A list of negative statements about the Product group, listed in a specific order', 'slim-seo-schema' ),
				'show'    => true,
				'fields'  => [
					[
						'id'       => '@type',
						'std'      => 'ItemList',
						'type'     => 'Hidden',
						'required' => true,
					],
					[
						'id'        => 'itemListElement',
						'type'      => 'Group',
						'cloneable' => true,
						'show'      => true,
						'fields'    => [
							[
								'id'       => '@type',
								'std'      => 'ListItem',
								'type'     => 'Hidden',
								'required' => true,
							],
							Helper::get_property( 'name', [
								'std'      => '',
								'tooltip'  => __( 'The key statement of the review.', 'slim-seo-schema' ),
								'required' => true,
							] ),
							[
								'id'      => 'position',
								'label'   => __( 'Position', 'slim-seo-schema' ),
								'show'    => true,
								'tooltip' => __( 'The position (order) of the statement in the list.', 'slim-seo-schema' ),
							],
						],
					],
				],
			],
		],
	] ),
	$aggregate_ratings,
	[
		'id'     => 'offers',
		'type'   => 'Group',
		'label'  => __( 'Offers', 'slim-seo-schema' ),
		'show'   => true,
		'fields' => [
			[
				'id'          => '@type',
				'label'       => __( 'Type', 'slim-seo-schema' ),
				'type'        => 'DataList',
				'required'    => true,
				'std'         => 'Offer',
				'dependant'   => true,
				'placeholder' => __( 'None', 'slim-seo-schema' ),
				'options'     => [
					'Offer'          => __( 'Offer', 'slim-seo-schema' ),
					'AggregateOffer' => __( 'Aggregate Offer', 'slim-seo-schema' ),
				],
			],
			[
				'id'         => 'price',
				'label'      => __( 'Price', 'slim-seo-schema' ),
				'required'   => true,
				'tooltip'    => __( 'The offer price of a product', 'slim-seo-schema' ),
				'dependency' => '[@type]:Offer',
				'std'        => '{{ product.price }}',
			],
			[
				'id'       => 'priceCurrency',
				'label'    => __( 'Price currency', 'slim-seo-schema' ),
				'required' => true,
				'std'      => '{{ product.currency }}',
			],
			[
				'id'      => 'priceValidUntil',
				'label'   => __( 'Price valid until', 'slim-seo-schema' ),
				'type'    => 'Date',
				'tooltip' => __( 'The date (in ISO 8601 date format) after which the price will no longer be available', 'slim-seo-schema' ),
				'std'     => '{{ product.sale_to }}',
			],
			[
				'id'      => 'availability',
				'label'   => __( 'Availability', 'slim-seo-schema' ),
				'type'    => 'DataList',
				'show'    => true,
				'tooltip' => __( 'The possible product availability options', 'slim-seo-schema' ),
				'std'     => '{{ product.stock }}',
				'options' => [
					'https://schema.org/Discontinued' => __( 'Discontinued', 'slim-seo-schema' ),
					'https://schema.org/InStock'      => __( 'In stock', 'slim-seo-schema' ),
					'https://schema.org/InStoreOnly'  => __( 'In store only', 'slim-seo-schema' ),
					'https://schema.org/LimitedAvailability' => __( 'Limited availability', 'slim-seo-schema' ),
					'https://schema.org/OnlineOnly'   => __( 'Online only', 'slim-seo-schema' ),
					'https://schema.org/OutOfStock'   => __( 'Out of stock', 'slim-seo-schema' ),
					'https://schema.org/PreOrder'     => __( 'Pre order', 'slim-seo-schema' ),
					'https://schema.org/PreSale'      => __( 'Pre sale', 'slim-seo-schema' ),
					'https://schema.org/SoldOut'      => __( 'Sold out', 'slim-seo-schema' ),
				],
			],
			[
				'id'      => 'itemOffered',
				'label'   => __( 'Item offered', 'slim-seo-schema' ),
				'tooltip' => __( 'The item being sold', 'slim-seo-schema' ),
				'std'     => '{{ post.title }}',
			],
			[
				'id'      => 'url',
				'label'   => __( 'URL', 'slim-seo-schema' ),
				'tooltip' => __( 'The URL of the Product group', 'slim-seo-schema' ),
				'std'     => '{{ post.url }}',
			],
			[
				'id'      => 'shippingDetails',
				'label'   => __( 'Shipping details', 'slim-seo-schema' ),
				'type'    => 'Group',
				'tooltip' => __( 'Nested information about the shipping policies and options associated with an Offer', 'slim-seo-schema' ),
				'fields'  => [
					[
						'id'       => '@type',
						'std'      => 'OfferShippingDetails',
						'type'     => 'Hidden',
						'required' => true,
					],
					[
						'id'       => 'shippingDestination',
						'label'    => __( 'Destination', 'slim-seo-schema' ),
						'type'     => 'Group',
						'required' => true,
						'fields'   => [
							[
								'id'       => '@type',
								'std'      => 'DefinedRegion',
								'type'     => 'Hidden',
								'required' => true,
							],
							[
								'id'       => 'addressCountry',
								'label'    => __( 'Country code', 'slim-seo-schema' ),
								'tooltip'  => __( 'The 2-digit country code, in ISO 3166-1 format', 'slim-seo-schema' ),
								'required' => true,
							],
							[
								'id'    => 'addressRegion',
								'label' => __( 'Region', 'slim-seo-schema' ),
								'show'  => true,
							],
						],
					],
					[
						'id'      => 'deliveryTime',
						'label'   => __( 'Delivery time', 'slim-seo-schema' ),
						'type'    => 'Group',
						'tooltip' => __( 'The total delay between the receipt of the order and the goods reaching the final customer', 'slim-seo-schema' ),
						'fields'  => [
							[
								'id'       => '@type',
								'std'      => 'ShippingDeliveryTime',
								'type'     => 'Hidden',
								'required' => true,
							],
							[
								'id'      => 'handlingTime',
								'label'   => __( 'Handling time', 'slim-seo-schema' ),
								'type'    => 'Group',
								'tooltip' => __( 'The typical delay between the receipt of the order and the goods either leaving the warehouse or being prepared for pickup, in case the delivery method is on site pickup', 'slim-seo-schema' ),
								'fields'  => [
									[
										'id'       => '@type',
										'std'      => 'QuantitativeValue',
										'type'     => 'Hidden',
										'required' => true,
									],
									[
										'id'    => 'minValue',
										'label' => __( 'Min value', 'slim-seo-schema' ),
										'show'  => true,
									],
									[
										'id'    => 'maxValue',
										'label' => __( 'Max value', 'slim-seo-schema' ),
										'show'  => true,
									],
								],
							],
							[
								'id'      => 'transitTime',
								'label'   => __( 'Transit time', 'slim-seo-schema' ),
								'type'    => 'Group',
								'tooltip' => __( 'The typical delay the order has been sent for delivery and the goods reach the final customer.', 'slim-seo-schema' ),
								'fields'  => [
									[
										'id'       => '@type',
										'std'      => 'QuantitativeValue',
										'type'     => 'Hidden',
										'required' => true,
									],
									[
										'id'    => 'minValue',
										'label' => __( 'Min value', 'slim-seo-schema' ),
										'show'  => true,
									],
									[
										'id'    => 'maxValue',
										'label' => __( 'Max value', 'slim-seo-schema' ),
										'show'  => true,
									],
								],
							],
							[
								'id'      => 'cutOffTime',
								'label'   => __( 'Cutoff time', 'slim-seo-schema' ),
								'show'    => true,
								'tooltip' => __( 'The time after which new orders are no longer processed on that same day, in ISO 8601 format. One day gets added to the handling time.', 'slim-seo-schema' ),
							],
							Helper::get_property( 'OpeningHoursSpecification', [
								'id'               => 'businessDays',
								'label'            => __( 'Business days', 'slim-seo-schema' ),
								'cloneItemHeading' => __( 'Business days', 'slim-seo-schema' ),
							] ),
						],
					],
					[
						'id'      => 'doesNotShip',
						'label'   => __( 'Does Not Ship?', 'slim-seo-schema' ),
						'type'    => 'DataList',
						'options' => [
							'true'  => __( 'Yes', 'slim-seo-schema' ),
							'false' => __( 'No', 'slim-seo-schema' ),
						],
					],
					[
						'id'      => 'shippingRate',
						'label'   => __( 'Shipping rate', 'slim-seo-schema' ),
						'type'    => 'Group',
						'tooltip' => __( 'Information about the cost of shipping to the specified destination', 'slim-seo-schema' ),
						'fields'  => [
							[
								'id'       => '@type',
								'std'      => 'MonetaryAmount',
								'type'     => 'Hidden',
								'required' => true,
							],
							[
								'id'    => 'currency',
								'label' => __( 'Currency', 'slim-seo-schema' ),
								'show'  => true,
							],
							[
								'id'    => 'value',
								'label' => __( 'Value', 'slim-seo-schema' ),
								'show'  => true,
							],
						],
					],
				],
			],
			[
				'id'      => 'acceptedPaymentMethod',
				'label'   => __( 'Accepted payment method', 'slim-seo-schema' ),
				'tooltip' => __( 'The payment method(s) accepted by seller for this offer.', 'slim-seo-schema' ),
				'type'    => 'DataList',
				'std'     => 'http://purl.org/goodrelations/v1#Cash',
				'options' => [
					'http://purl.org/goodrelations/v1#ByBankTransferInAdvance' => __( 'By bank transfer in advance', 'slim-seo-schema' ),
					'http://purl.org/goodrelations/v1#ByInvoice' => __( 'By invoice', 'slim-seo-schema' ),
					'http://purl.org/goodrelations/v1#Cash' => __( 'Cash', 'slim-seo-schema' ),
					'http://purl.org/goodrelations/v1#CheckInAdvance' => __( 'Check in advance', 'slim-seo-schema' ),
					'http://purl.org/goodrelations/v1#COD' => __( 'COD', 'slim-seo-schema' ),
					'http://purl.org/goodrelations/v1#DirectDebit' => __( 'Direct debit', 'slim-seo-schema' ),
					'http://purl.org/goodrelations/v1#GoogleCheckout' => __( 'Google checkout', 'slim-seo-schema' ),
					'http://purl.org/goodrelations/v1#PayPal' => __( 'PayPal', 'slim-seo-schema' ),
					'http://purl.org/goodrelations/v1#PaySwarm' => __( 'PaySwarm', 'slim-seo-schema' ),
				],
			],
			[
				'id'          => 'addOn',
				'label'       => __( 'Add on', 'slim-seo-schema' ),
				'tooltip'     => __( 'An additional offer that can only be obtained in combination with the first base offer (e.g. supplements and extensions that are available for a surcharge).', 'slim-seo-schema' ),
				'description' => __( 'Please create another Offer schema and link to this property via a dynamic variable', 'slim-seo-schema' ),
			],
			Helper::get_property( 'duration', [
				'id'      => 'advanceBookingRequirement',
				'label'   => __( 'Advance booking requirement', 'slim-seo-schema' ),
				'type'    => 'Group',
				'tooltip' => __( 'The amount of time that is required between accepting the offer and the actual usage of the resource or service.', 'slim-seo-schema' ),
			] ),
			Helper::get_property( 'aggregateRating', [
				'tooltip' => __( 'The overall rating, based on a collection of reviews or ratings, of the offer.', 'slim-seo-schema' ),
			] ),
			[
				'id'      => 'areaServed',
				'label'   => __( 'Area served', 'slim-seo-schema' ),
				'tooltip' => __( 'The geographic area where a service or offered item is provided.', 'slim-seo-schema' ),
			],
			[
				'id'      => 'asin',
				'label'   => __( 'Asin', 'slim-seo-schema' ),
				'tooltip' => __( 'An Amazon Standard Identification Number (ASIN) is a 10-character alphanumeric unique identifier assigned by Amazon.com and its partners for product identification within the Amazon organization. See documentation from Amazon for authoritative details', 'slim-seo-schema' ),
			],
			[
				'id'      => 'availabilityStarts',
				'label'   => __( 'Availability starts', 'slim-seo-schema' ),
				'tooltip' => __( 'The beginning of the availability of the Product group or service included in the offer.', 'slim-seo-schema' ),
				'type'    => 'Date',
			],
			[
				'id'      => 'availabilityEnds',
				'label'   => __( 'Availability ends', 'slim-seo-schema' ),
				'tooltip' => __( 'The end of the availability of the Product group or service included in the offer.', 'slim-seo-schema' ),
				'type'    => 'Date',
			],
			[
				'id'      => 'availableDeliveryMethod',
				'label'   => __( 'Available delivery method', 'slim-seo-schema' ),
				'type'    => 'DataList',
				'options' => [
					'http://purl.org/goodrelations/v1#DeliveryModeDirectDownload' => __( 'Direct download', 'slim-seo-schema' ),
					'http://purl.org/goodrelations/v1#DeliveryModeFreight' => __( 'Freight', 'slim-seo-schema' ),
					'http://purl.org/goodrelations/v1#DeliveryModeMail' => __( 'Mail', 'slim-seo-schema' ),
					'http://purl.org/goodrelations/v1#DeliveryModeOwnFleet' => __( 'Own fleet', 'slim-seo-schema' ),
					'http://purl.org/goodrelations/v1#DeliveryModePickUp' => __( 'Pick up', 'slim-seo-schema' ),
					'http://purl.org/goodrelations/v1#DHL' => __( 'DHL', 'slim-seo-schema' ),
					'http://purl.org/goodrelations/v1#FederalExpress' => __( 'Federal express', 'slim-seo-schema' ),
					'http://purl.org/goodrelations/v1#UPS' => __( 'UPS', 'slim-seo-schema' ),
				],
			],
			[
				'label'   => __( 'Category', 'slim-seo-schema' ),
				'id'      => 'category',
				'tooltip' => __( 'A category for the item. Greater signs or slashes can be used to informally indicate a category hierarchy.', 'slim-seo-schema' ),
			],
			[
				'label'   => __( 'Checkout page URL template', 'slim-seo-schema' ),
				'id'      => 'checkoutPageURLTemplate',
				'tooltip' => __( 'A URL template (RFC 6570) for a checkout page for an offer.', 'slim-seo-schema' ),
			],
			Helper::get_property( 'description', [
				'std'     => '',
				'show'    => false,
				'tooltip' => __( 'A description of the offer.', 'slim-seo-schema' ),
			] ),
			[
				'label'   => __( 'Disambiguating description', 'slim-seo-schema' ),
				'id'      => 'disambiguatingDescription',
				'tooltip' => __( 'A sub property of description. A short description of the item used to disambiguate from other.', 'slim-seo-schema' ),
			],
			[
				'id'      => 'eligibleCustomerType',
				'label'   => __( 'Eligible customer type', 'slim-seo-schema' ),
				'tooltip' => __( 'The type(s) of customers for which the given offer is valid.', 'slim-seo-schema' ),
				'type'    => 'DataList',
				'std'     => 'http://purl.org/goodrelations/v1#Business',
				'options' => [
					'http://purl.org/goodrelations/v1#Business'          => __( 'Business', 'slim-seo-schema' ),
					'http://purl.org/goodrelations/v1#Enduser'           => __( 'End-user', 'slim-seo-schema' ),
					'http://purl.org/goodrelations/v1#PublicInstitution' => __( 'Public institution', 'slim-seo-schema' ),
					'http://purl.org/goodrelations/v1#Reseller'          => __( 'Reseller', 'slim-seo-schema' ),
				],
			],
			Helper::get_property( 'duration', [
				'id'      => 'eligibleDuration',
				'label'   => __( 'Eligible duration', 'slim-seo-schema' ),
				'type'    => 'Group',
				'tooltip' => __( 'The duration for which the given offer is valid.', 'slim-seo-schema' ),
			] ),
			Helper::get_property( 'QuantitativeValue', [
				'id'      => 'eligibleQuantity',
				'label'   => __( 'Eligible quantity', 'slim-seo-schema' ),
				'type'    => 'Group',
				'tooltip' => __( 'The interval and unit of measurement of ordering quantities for which the offer or price specification is valid. This allows e.g. specifying that a certain freight charge is valid only for a certain quantity.', 'slim-seo-schema' ),
			] ),
			[
				'id'        => 'eligibleRegion',
				'label'     => __( 'Eligible region', 'slim-seo-schema' ),
				'tooltip'   => __( 'The ISO 3166-1 (ISO 3166-1 alpha-2) or ISO 3166-2 code, the place, or the GeoShape for the geo-political region(s) for which the offer or delivery charge specification is valid.', 'slim-seo-schema' ),
				'cloneable' => true,
			],
			[
				'id'      => 'eligibleTransactionVolume',
				'label'   => __( 'Eligible transaction volume', 'slim-seo-schema' ),
				'tooltip' => __( 'The transaction volume, in a monetary unit, for which the offer or price specification is valid.', 'slim-seo-schema' ),
				'type'    => 'Group',
				'fields'  => [
					[
						'id'       => '@type',
						'std'      => 'PriceSpecification',
						'type'     => 'Hidden',
						'required' => true,
					],
					[
						'id'       => 'price',
						'label'    => __( 'Price', 'slim-seo-schema' ),
						'tooltip'  => __( 'The price amount for the specified offer.', 'slim-seo-schema' ),
						'required' => true,
					],
					[
						'id'       => 'priceCurrency',
						'label'    => __( 'Price currency', 'slim-seo-schema' ),
						'tooltip'  => __( 'The currency of the price for the specified offer.', 'slim-seo-schema' ),
						'required' => true,
					],
				],
			],
			[
				'id'      => 'hasAdultConsideration',
				'label'   => __( 'Has adult consideration', 'slim-seo-schema' ),
				'tooltip' => __( 'Used to tag an item to be intended or suitable for consumption or use by adults only.', 'slim-seo-schema' ),
				'type'    => 'DataList',
				'options' => [
					'https://schema/org/AlcoholConsideration'                     => 'Alcohol',
					'https://schema/org/DangerousGoodConsideration'               => 'Dangerous good',
					'https://schema/org/HealthcareConsideration'                  => 'Healthcare',
					'https://schema/org/NarcoticConsideration'                    => 'Narcotic',
					'https://schema/org/ReducedRelevanceForChildrenConsideration' => 'Reduced relevance for children',
					'https://schema/org/SexualContentConsideration'               => 'Sexual content',
					'https://schema/org/TobaccoNicotineConsideration'             => 'Tobacco nicotine',
					'https://schema/org/UnclassifiedAdultConsideration'           => 'Unclassified adult',
					'https://schema/org/ViolenceConsideration'                    => 'Violence',
					'https://schema/org/WeaponConsideration'                      => 'Weapon',

				],
			],
			[
				'id'      => 'isFamilyFriendly',
				'label'   => __( 'Is family friendly', 'slim-seo-schema' ),
				'tooltip' => __( 'Indicates whether this content is family friendly.', 'slim-seo-schema' ),
				'type'    => 'DataList',
				'std'     => 'True',
				'options' => [
					'True'  => __( 'True', 'slim-seo-schema' ),
					'False' => __( 'False', 'slim-seo-schema' ),
				],
			],
			Helper::get_property( 'duration', [
				'id'      => 'leaseLength',
				'label'   => __( 'Lease length', 'slim-seo-schema' ),
				'type'    => 'Group',
				'tooltip' => __( 'Length of the lease for some Accommodation, either particular to some Offer or in some cases intrinsic to the property.', 'slim-seo-schema' ),
			] ),
			[
				'label'   => __( 'Mobile URL', 'slim-seo-schema' ),
				'id'      => 'mobileUrl',
				'tooltip' => __( 'Provided for specific situations in which data consumers need to determine whether one of several provided URLs is a dedicated \'mobile site\'. The property is expected only on Product and Offer, rather than Thing.', 'slim-seo-schema' ),
			],
			[
				'label'   => __( 'MPN', 'slim-seo-schema' ),
				'id'      => 'mpn',
				'tooltip' => __( 'The Manufacturer Part Number (MPN) of the Product group, or the Product group to which the offer refers', 'slim-seo-schema' ),
			],
			[
				'id'          => 'offeredBy',
				'label'       => __( 'Offered by', 'slim-seo-schema' ),
				'tooltip'     => __( 'A pointer to the organization or person making the offer.', 'slim-seo-schema' ),
				'description' => __( 'Please create a Person or an Organization schema and link to this property via a dynamic variable', 'slim-seo-schema' ),
			],
			[
				'id'          => 'seller',
				'label'       => __( 'Seller', 'slim-seo-schema' ),
				'tooltip'     => __( 'An entity which offers (sells / leases / lends / loans) the services / goods. A seller may also be a provider.', 'slim-seo-schema' ),
				'description' => __( 'Please create a Person or an Organization schema and link to this property via a dynamic variable', 'slim-seo-schema' ),
			],
			[
				'label'   => __( 'Serial number', 'slim-seo-schema' ),
				'id'      => 'serialNumber',
				'tooltip' => __( 'The serial number or any alphanumeric identifier of a particular product. When attached to an offer, it is a shortcut for the serial number of the Product group included in the offer.', 'slim-seo-schema' ),
			],
			[
				'label'   => __( 'SKU', 'slim-seo-schema' ),
				'id'      => 'sku',
				'tooltip' => __( 'The Stock Keeping Unit (SKU), i.e. a merchant-specific identifier for a product or service, or the Product group to which the offer refers.', 'slim-seo-schema' ),
			],
			[
				'id'      => 'validFrom',
				'label'   => __( 'Valid from', 'slim-seo-schema' ),
				'type'    => 'Date',
				'tooltip' => __( 'The date when the item becomes valid in ISO-8601 format', 'slim-seo-schema' ),
			],
			[
				'id'      => 'validThrough',
				'label'   => __( 'Valid through', 'slim-seo-schema' ),
				'tooltip' => __( 'The date after when the item is not valid. For exp. the end of an offer, salary period, or a period of opening hours. ', 'slim-seo-schema' ),
			],
			[
				'id'         => 'lowPrice',
				'label'      => __( 'Low price', 'slim-seo-schema' ),
				'required'   => true,
				'tooltip'    => __( 'The lowest price of all offers available. Use a floating point number.', 'slim-seo-schema' ),
				'dependency' => '[@type]:AggregateOffer',
				'std'        => '{{ product.low_price }}',
			],
			[
				'id'         => 'highPrice',
				'label'      => __( 'High price', 'slim-seo-schema' ),
				'dependency' => '[@type]:AggregateOffer',
				'std'        => '{{ product.high_price }}',
				'show'       => true,
			],
			[
				'id'         => 'offerCount',
				'label'      => __( 'Offer count', 'slim-seo-schema' ),
				'tooltip'    => __( 'The number of offers for the Product group.', 'slim-seo-schema' ),
				'dependency' => '[@type]:AggregateOffer',
				'std'        => '{{ product.offer_count }}',
				'show'       => true,
			],
		],
	],

	// Optional properties.
	[
		'label'   => __( 'Audience', 'slim-seo-schema' ),
		'id'      => 'audience',
		'tooltip' => __( 'An intended audience, i.e. a group for whom something was created.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Award', 'slim-seo-schema' ),
		'id'      => 'award',
		'tooltip' => __( 'An award won by or for this item.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Category', 'slim-seo-schema' ),
		'id'      => 'category',
		'tooltip' => __( 'A category for the item. Greater signs or slashes can be used to informally indicate a category hierarchy.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Color', 'slim-seo-schema' ),
		'id'      => 'color',
		'show'    => true,
		'tooltip' => __( 'The color of the Product group.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Country of assembly', 'slim-seo-schema' ),
		'id'      => 'countryOfAssembly',
		'tooltip' => __( 'The place where the Product group was assembled.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Country of last processing', 'slim-seo-schema' ),
		'id'      => 'countryOfLastProcessing',
		'tooltip' => __( 'The place where the Product group was last processed and tested before importation.', 'slim-seo-schema' ),
	],
	Helper::get_property( 'countryOfOrigin' ),
	[
		'label'   => __( 'Depth', 'slim-seo-schema' ),
		'id'      => 'depth',
		'std'     => '{{ post.custom_field._length }} cm',
		'tooltip' => __( 'The depth of the Product group.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Disambiguating description', 'slim-seo-schema' ),
		'id'      => 'disambiguatingDescription',
		'tooltip' => __( 'A sub property of description. A short description of the item used to disambiguate from other.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'GTIN', 'slim-seo-schema' ),
		'id'      => 'gtin',
		'tooltip' => __( 'A Global Trade Item Number (GTIN). GTINs identify trade items, including products and services, using numeric identification codes.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'GTIN-8', 'slim-seo-schema' ),
		'id'      => 'gtin8',
		'tooltip' => __( 'The GTIN-8 code of the Product group, or the Product group to which the offer refers. This code is also known as EAN/UCC-8 or 8-digit EAN.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'GTIN-12', 'slim-seo-schema' ),
		'id'      => 'gtin12',
		'tooltip' => __( 'The GTIN-12 code of the Product group, or the Product group to which the offer refers. The GTIN-12 is the 12-digit GS1 Identification Key composed of a U.P.C. Company Prefix, Item Reference, and Check Digit used to identify trade items.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'GTIN-13', 'slim-seo-schema' ),
		'id'      => 'gtin13',
		'tooltip' => __( 'The GTIN-13 code of the Product group, or the Product group to which the offer refers. This is equivalent to 13-digit ISBN codes and EAN UCC-13. Former 12-digit UPC codes can be converted into a GTIN-13 code by simply adding a preceding zero.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'GTIN-14', 'slim-seo-schema' ),
		'id'      => 'gtin14',
		'tooltip' => __( 'The GTIN-14 code of the Product group, or the Product group to which the offer refers.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Height', 'slim-seo-schema' ),
		'id'      => 'height',
		'std'     => '{{ post.custom_field._height }} cm',
		'tooltip' => __( 'The height of the Product group.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Logo', 'slim-seo-schema' ),
		'id'      => 'logo',
		'tooltip' => __( 'Link to logo associated with the Product group.', 'slim-seo-schema' ),
	],
	[
		'id'      => 'manufacturer',
		'label'   => __( 'Manufacturer', 'slim-seo-schema' ),
		'tooltip' => __( 'The manufacturer of the Product group.', 'slim-seo-schema' ),
		'std'     => '{{ schemas.organization }}',
	],
	[
		'label'   => __( 'Material', 'slim-seo-schema' ),
		'id'      => 'material',
		'tooltip' => __( 'A material that product is made from, e.g. leather, wool, cotton, paper.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Model', 'slim-seo-schema' ),
		'id'      => 'model',
		'tooltip' => __( 'The model of the Product group.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'MPN', 'slim-seo-schema' ),
		'id'      => 'mpn',
		'tooltip' => __( 'The Manufacturer Part Number (MPN) of the Product group, or the Product group to which the offer refers', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'NSN', 'slim-seo-schema' ),
		'id'      => 'nsn',
		'tooltip' => __( 'Indicates the NATO stock number (nsn) of a Product.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Pattern', 'slim-seo-schema' ),
		'id'      => 'pattern',
		'tooltip' => __( 'A pattern that something has, for example "polka dot", "striped".', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Product ID', 'slim-seo-schema' ),
		'id'      => 'productID',
		'tooltip' => __( 'the Product group identifier, such as ISBN.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Product group ID', 'slim-seo-schema' ),
		'id'      => 'inProductGroupWithID',
		'tooltip' => __( 'The id of a product group that this product variant belongs to. See also Item Group Id in Google Merchant Center Help. At most one value should be specified.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Production date', 'slim-seo-schema' ),
		'id'      => 'productionDate',
		'std'     => '{{ product.date }}',
		'tooltip' => __( 'The date of production of product in ISO 8601 date format.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Purchase date', 'slim-seo-schema' ),
		'id'      => 'purchaseDate',
		'std'     => '{{ product.date }}',
		'tooltip' => __( 'The date the item e.g. vehicle was purchased by the current owner in ISO 8601 date format.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Release date', 'slim-seo-schema' ),
		'id'      => 'releaseDate',
		'std'     => '{{ product.date }}',
		'tooltip' => __( 'The release date of a product or product model in ISO 8601 date format. This can be used to distinguish the exact variant of a product.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Size', 'slim-seo-schema' ),
		'id'      => 'size',
		'show'    => true,
		'tooltip' => __( 'A standardized size of a product or creative work, specified either through a simple textual string (for exp. "XL", "32Wx34L").', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Slogan', 'slim-seo-schema' ),
		'id'      => 'slogan',
		'tooltip' => __( 'A slogan or motto associated with the Product group.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Weight', 'slim-seo-schema' ),
		'id'      => 'weight',
		'std'     => '{{ post.custom_field._weight }} kg',
		'tooltip' => __( 'The weight of the Product group.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Width', 'slim-seo-schema' ),
		'id'      => 'width',
		'std'     => '{{ post.custom_field._width }} cm',
		'tooltip' => __( 'The width of the Product group.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Item condition', 'slim-seo-schema' ),
		'id'      => 'itemCondition',
		'type'    => 'DataList',
		'tooltip' => __( 'The condition of the Product group or service. Also used for product return policies to specify the condition of products accepted for returns.', 'slim-seo-schema' ),
		'std'     => 'https://schema.org/NewCondition',
		'options' => [
			'https://schema.org/NewCondition'         => __( 'New condition', 'slim-seo-schema' ),
			'https://schema.org/UsedCondition'        => __( 'Used condition', 'slim-seo-schema' ),
			'https://schema.org/RefurbishedCondition' => __( 'Refurbished condition', 'slim-seo-schema' ),
			'https://schema.org/DamagedCondition'     => __( 'Damaged condition', 'slim-seo-schema' ),
		],
	],
];
