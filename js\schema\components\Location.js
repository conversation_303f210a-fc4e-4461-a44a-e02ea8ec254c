import { useEffect, useState } from "@wordpress/element";
import { __ } from "@wordpress/i18n";
import dotProp from 'dot-prop';
import Property from "./Property";

const fields = [
	{
		id: 'type',
		label: __( 'Type', 'slim-seo-schema' ),
		required: true,
		options: SSSchema.locationTypes,
		std: 'singular',
		type: 'Select',
		dependant: true,
	},
	{
		id: 'singular_locations',
		type: 'GroupLocation',
		locations: SSSchema.singularLocations,
		groups: SSSchema.singularRules,
		dependency: '[type]:singular',
	},
	{
		id: 'archive_locations',
		type: 'GroupLocation',
		locations: SSSchema.archiveLocations,
		groups: SSSchema.archiveRules,
		dependency: '[type]:archive',
	},
	{
		id: 'code',
		label: __( 'Code', 'slim-seo-schema' ),
		type: 'Textarea',
		dependency: '[type]:code',
		hasInsert: false,
		required: true
	}
];

export default function Location( { schemaID, location } ) {
	const [ properties, setProperties ] = useState( [] );
	useEffect( () => {
		setProperties( () => fields.map( property => {
			const id = `schemas[${ schemaID }][location][${ property.id }]`;
			const parentID = `${ schemaID }`;
			const std = dotProp.get( location, property.id, '' );
			return { ...property, id, std, parentID, hidden: false };
		} ) );
	}, [] );

	return properties.filter( field => !field.hidden ).map( field => (
		<Property key={ field.id } property={ field } setProperties={ setProperties } properties={ properties } />
	) );
}
