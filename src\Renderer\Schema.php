<?php
namespace SlimSEOPro\Schema\Renderer;

use eLightUp\Twig\Environment;
use eLightUp\Twig\Loader\ArrayLoader;
use SlimSEOPro\Schema\Support\Arr;
use SlimSEOPro\Schema\Support\Data;

class Schema {
	private $type;
	private $props;
	private $schema;
	private $data = [];

	public function __construct( string $type, array $props, array $data ) {
		unset( $props['_label'] );

		$this->type  = $type;
		$this->props = $props;
		$this->data  = $data;

		$this->parse_custom_json_ld();

		$this->schema = $this->type === 'CustomJsonLd' ? [] : [
			'fields' => Data::get_schema_specs( $this->type ),
		];
	}

	private function parse_custom_json_ld(): void {
		if ( $this->type !== 'CustomJsonLd' || empty( $this->props['code'] ) ) {
			return;
		}

		// Extract the JSON code incase users enter with <script type="application/ld+json">...</script>
		$code       = $this->props['code'];
		$json_start = strpos( $code, '{' );
		$json_end   = strrpos( $code, '}' );
		$json       = substr( $code, $json_start, $json_end - $json_start + 1 );
		$fields     = json_decode( $json, true );

		$this->props = empty( $fields ) || empty( $fields['@type'] ) ? [] : $fields;
	}

	public function render(): array {
		$this->render_props( $this->props );
		return $this->empty() ? [] : $this->props;
	}

	private function empty(): bool {
		$props = $this->props;
		unset( $props['@id'], $props['@type'], $props['name'] );
		return empty( $props );
	}

	/**
	 * Render array of props.
	 *
	 * @param array  $props  Array of schema props.
	 * @param string $prefix Key prefix, to access the prop settings from the specs.
	 */
	private function render_props( &$props, $prefix = '' ) {
		if ( empty( $props ) ) {
			return;
		}
		foreach ( $props as $key => &$value ) {
			$key = trim( "$prefix.$key", '.' );

			if ( is_array( $value ) ) {
				$this->render_array_prop( $value, $key );
			} else {
				$this->render_single_prop( $value );
			}
		}

		$props = array_filter( $props );
		if ( Arr::is_numeric_key( $props ) ) {
			$props = array_values( $props );
		}
	}

	private function render_array_prop( &$value, $key ) {
		$settings = Arr::find_sub_field( $this->schema, $key );

		// Non-cloneable props, e.g. normal group: render sub-props.
		if ( ! Arr::get( $settings, 'cloneable' ) ) {
			$this->render_props( $value, $key );
			return;
		}

		// Stack cloneable props.
		CloneableProps::increase();

		// Remove keys.
		$value = array_values( $value );

		// Non-group props, like image: render and merge all values.
		if ( Arr::get( $settings, 'type' ) !== 'Group' ) {
			array_walk( $value, [ $this, 'render_single_prop' ] );
			$value = Arr::flatten( $value );
			CloneableProps::decrease();
			return;
		}

		// Simply render each clone if it has sub cloneable groups.
		if ( $this->has_cloneable_group( $settings ) ) {
			foreach ( $value as &$clone ) {
				$this->render_props( $clone, $key );
			}
			CloneableProps::decrease();
			return;
		}

		// No sub cloneable group: re-parse value.
		$this->render_cloneable_group_without_sub_cloneable_group( $value );

		CloneableProps::decrease();
	}

	private function render_cloneable_group_without_sub_cloneable_group( array &$value ): void {
		$new_value = [];
		foreach ( $value as &$clone ) {
			$clone = Arr::dot( $clone );
			array_walk( $clone, [ $this, 'render_single_prop' ] );

			$count = $this->count_clones( $clone );
			for ( $i = 1; $i <= $count; $i++ ) {
				$new_clone   = $this->build_clone( $clone, $i );
				$new_value[] = Arr::undot( $new_clone );
			}
		}
		$value = $new_value;
	}

	/**
	 * Build new clone from a clone with multiple values in some props.
	 */
	private function build_clone( array $clone, int $index ): array {
		--$index;
		$return = [];
		foreach ( $clone as $key => $value ) {
			$return[ $key ] = is_array( $value ) ? ( isset( $value[ $index ] ) ? $value[ $index ] : null ) : $value;
		}
		return $return;
	}

	/**
	 * Count how many (max) values (Meta Box clone values) in every prop of a group.
	 */
	private function count_clones( array $value ): int {
		$count = 1;
		foreach ( $value as $sub_value ) {
			if ( is_array( $sub_value ) && count( $sub_value ) > $count ) {
				$count = count( $sub_value );
			}
		}
		return $count;
	}

	/**
	 * Check if a group has a sub cloneable group.
	 */
	private function has_cloneable_group( array $prop ): string {
		foreach ( $prop['fields'] as $sub_prop ) {
			if ( Arr::get( $sub_prop, 'type' ) !== 'Group' ) {
				continue;
			}

			// If current field (group) is cloneable or has a cloneable child.
			if ( ! empty( $sub_prop['cloneable'] ) || $this->has_cloneable_group( $sub_prop ) ) {
				return true;
			}
		}

		return false;
	}

	/**
	 * Render a single prop.
	 *
	 * A prop can be rendered as an array (multiple/cloneable fields from Meta Box) or a simple string.
	 * In this case, need to use json_encode() & json_decode() to parse a variable to an array.
	 *
	 * Because of that, we have to render each variable and combine later.
	 *
	 * @param string $value Prop value.
	 * @return string|array
	 */
	private function render_single_prop( &$value ) {
		// Don't render if no variables.
		if ( strpos( $value, '{{' ) === false ) {
			// Parse shortcodes, blocks, etc.
			$value = Normalizer::normalize( $value );
			return;
		}

		preg_match_all( '#{{\s*?([^}\s]+?)\s*?}}#', $value, $matches, PREG_PATTERN_ORDER );

		// One variable.
		if ( count( $matches[0] ) === 1 ) {
			$var       = $matches[0][0];
			$var_value = $this->render_variable( $var );
			// Allows to parse post.categories as array. In case of string, allow both dynamic and static texts.
			$value = is_array( $var_value ) ? $var_value : strtr( $value, [ $var => $var_value ] );

			// Parse shortcodes, blocks, etc.
			$value = Normalizer::normalize( $value );
			return;
		}

		// If many variables in the prop, render each one and replace later.
		$replacements = [];
		$count        = 1;
		foreach ( $matches[0] as $var ) {
			$var_value            = $this->render_variable( $var );
			$replacements[ $var ] = $var_value;
			if ( is_array( $var_value ) ) {
				$count = max( $count, count( $var_value ) );
			}
		}

		$return = [];
		for ( $i = 0; $i < $count; $i++ ) {
			$row_replacements = [];
			foreach ( $replacements as $var => $var_value ) {
				$replacement              = is_array( $var_value ) ? ( $var_value[ $i ] ?? '' ) : $var_value;
				$row_replacements[ $var ] = $replacement;
			}
			$return[] = strtr( $value, $row_replacements );
		}

		$value = $count === 1 ? reset( $return ) : $return;

		// Parse shortcodes, blocks, etc.
		$value = Normalizer::normalize( $value );
	}

	/**
	 * Render a single variable.
	 *
	 * A variable can be rendered as an array (multiple/cloneable fields from Meta Box) or a simple string.
	 * In this case, need to use json_encode() & json_decode() to parse a variable to an array.
	 *
	 * @link https://stackoverflow.com/q/64858073/371240
	 *
	 * @param string $variable Variable.
	 * @return string|array
	 */
	private function render_variable( $variable ) {
		ActiveVariable::set( trim( $variable, ' {}' ) );

		$raw = str_replace( '}}', '| json_encode() | raw }}', $variable );

		$loader = new ArrayLoader( compact( 'raw' ) );
		$twig   = new Environment( $loader, [ 'autoescape' => false ] );
		$value  = $twig->render( 'raw', $this->data );
		$value  = json_decode( $value, true );
		$value  = Normalizer::normalize( $value );

		return $value;
	}
}
