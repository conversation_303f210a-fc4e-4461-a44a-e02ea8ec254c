.interface-complementary-area .components-panel {
	z-index: 9;
}
.interface-interface-skeleton__sidebar {
	overflow: visible;
}
#side-sortables {
	.sss-panel:not(.sss-panel--no-border) {
		border: 1px solid var(--c-lighter);
	}
	.sss-docs-link {
		display: none;
	}
	.sss-panel__title {
		min-width: 0;
	}
}

@media (max-width: 782px) {
	.sss-panel:not(.sss-panel--no-border) {
		border: 1px solid var(--c-lighter);
	}
	.sss-docs-link {
		display: none;
	}
	.sss-panel__title {
		min-width: 0;
	}
}