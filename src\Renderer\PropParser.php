<?php
namespace SlimSEOPro\Schema\Renderer;

use SlimSEOPro\Schema\Support\Arr;

class PropParser {
	public static function parse( &$schema ) {
		$type   = $schema['type'];
		$fields = &$schema['fields'];

		$id            = self::get_id( $schema );
		$fields['@id'] = self::get_id_value( $id );

		$custom_fields = $fields['custom'] ?? [];
		$custom_fields = self::parse_custom_fields( $custom_fields );
		unset( $fields['custom'] );

		$fields = Arr::undot( $custom_fields, array_merge( [ '@type' => $type ], $fields ) );
	}

	public static function parse_custom_fields( $custom_fields ) {
		if ( empty( $custom_fields ) ) {
			return [];
		}
		$fields = [];
		foreach ( $custom_fields as $field ) {
			$fields[ $field['key'] ] = $field['value'];
		}
		return $fields;
	}

	public static function get_id( $schema ) {
		$label = Arr::get( $schema, 'fields._label', $schema['type'] );
		return str_replace( '-', '_', sanitize_title( $label ) );
	}

	public static function get_id_value( $id ) {
		return self::get_current_url() . "#$id";
	}

	private static function get_current_url() {
		global $wp;

		$url = add_query_arg( [], $wp->request );
		$url = home_url( $url );
		$url = strtok( $url, '#' );
		$url = strtok( $url, '?' );

		return $url;
	}
}
