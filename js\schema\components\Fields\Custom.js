import { Control } from "@elightup/form";
import { Dashicon } from "@wordpress/components";
import { useRef, useState } from "@wordpress/element";
import { __ } from "@wordpress/i18n";
import { uniqueID } from "../../functions";
import PropInserter from "./PropInserter";

const Custom = ( { property, className, deleteProp } ) => {
	const { id, label, required, std, tooltip } = property;

	const [ items, setItems ] = useState( Object.values( std || {} ) );
	const addItem = () => setItems( prev => [ ...prev, { key: '', value: '', id: uniqueID() } ] );
	const removeItem = id => setItems( prev => prev.filter( item => item.id !== id ) );

	return (
		<Control className={ className } label={ label } id={ id } required={ required } tooltip={ tooltip }>
			{
				items.map( item => (
					<Item
						key={ item.id }
						item={ item }
						removeItem={ removeItem }
						name={ `${ id }[${ item.id }]` }
					/>
				) )
			}
			<button type="button" className="button" onClick={ addItem }>{ __( '+ Add New', 'slim-seo-schema' ) }</button>
		</Control>
	);
};

const Item = ( { name, item, removeItem } ) => {
	const inputRef = useRef();

	return (
		<div className="sss-custom">
			<input type="hidden" name={ `${ name }[id]` } defaultValue={ item.id } />
			<input type="text" placeholder="Enter key" name={ `${ name }[key]` } defaultValue={ item.key } />
			<div className="sss-input-wrapper">
				<input type="text" placeholder="Enter value" name={ `${ name }[value]` } defaultValue={ item.value } ref={ inputRef } />
				<PropInserter inputRef={ inputRef } />
			</div>
			<button type="button" className="sss-custom__remove" title={ __( 'Remove', 'meta-box-builder' ) } onClick={ () => removeItem( item.id ) }><Dashicon icon="dismiss" /></button>
		</div>
	);
};

export default Custom;
