(()=>{var e={967:(e,t,n)=>{var r={"./Custom":[2388,179],"./Custom.js":[2388,179],"./DataList":[4343,179],"./DataList.js":[4343,179],"./Date":[9592,179],"./Date.js":[9592,179],"./DuplicateField":[1910],"./DuplicateField.js":[1910],"./Field":[4151],"./Field.js":[4151],"./GoogleDocs":[3502,179],"./GoogleDocs.js":[3502,179],"./Group":[9406,179],"./Group.js":[9406,179],"./GroupLocation":[2177,179],"./GroupLocation.js":[2177,179],"./Hidden":[7762],"./Hidden.js":[7762],"./Image":[5937,179],"./Image.js":[5937,179],"./PropInserter":[1457,179],"./PropInserter.js":[1457,179],"./SchemaDocs":[9304,179],"./SchemaDocs.js":[9304,179],"./Select":[6534,179],"./Select.js":[6534,179],"./Text":[4632,179],"./Text.js":[4632,179],"./Textarea":[8787,179],"./Textarea.js":[8787,179]};function o(e){if(!n.o(r,e))return Promise.resolve().then((()=>{var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}));var t=r[e],o=t[0];return Promise.all(t.slice(1).map(n.e)).then((()=>n(o)))}o.keys=()=>Object.keys(r),o.id=967,e.exports=o},4619:(e,t,n)=>{"use strict";n.d(t,{oT:()=>u,Ph:()=>s});var r=n(2610),o=n(6010),a=n(7537);const i=({content:e})=>React.createElement(a.Tooltip,{text:e},React.createElement("span",{className:"ef-control__tooltip"},React.createElement(a.Dashicon,{icon:"editor-help"}))),u=({label:e="",required:t=!1,tooltip:n="",description:a="",id:u="",className:c="",children:s})=>React.createElement("div",{className:(0,o.Z)("ef-control",c)},e&&React.createElement("label",{className:"ef-control__label",htmlFor:u},e.includes("<")?React.createElement(r.RawHTML,null,e):e,t&&React.createElement("span",{className:"ef-control__required"},"*"),n&&React.createElement(i,{content:n})),React.createElement("div",{className:"ef-control__input"},s,a&&React.createElement(r.RawHTML,{className:"ef-control__description"},a)));function c(){return c=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},c.apply(this,arguments)}const s=({id:e="",name:t="",value:n="",placeholder:r="-",options:o,onChange:a,...i})=>React.createElement(u,c({id:e},i),React.createElement("select",{id:e,name:t,defaultValue:n,onChange:a},React.createElement("option",{value:""},r),Array.isArray(o)?o.map((e=>e.options?React.createElement("optgroup",{key:e.label,label:e.label},Array.isArray(e.options)?e.options.map((e=>React.createElement("option",{key:e.value,value:e.value},e.label))):Object.entries(e.options).map((([e,t])=>React.createElement("option",{key:e,value:e},t)))):React.createElement("option",{key:e.value,value:e.value},e.label))):Object.entries(o).map((([e,t])=>React.createElement("option",{key:e,value:e},t)))))},6898:(e,t,n)=>{"use strict";n.d(t,{Z:()=>oe});var r=function(){function e(e){var t=this;this._insertTag=function(e){var n;n=0===t.tags.length?t.insertionPoint?t.insertionPoint.nextSibling:t.prepend?t.container.firstChild:t.before:t.tags[t.tags.length-1].nextSibling,t.container.insertBefore(e,n),t.tags.push(e)},this.isSpeedy=void 0===e.speedy||e.speedy,this.tags=[],this.ctr=0,this.nonce=e.nonce,this.key=e.key,this.container=e.container,this.prepend=e.prepend,this.insertionPoint=e.insertionPoint,this.before=null}var t=e.prototype;return t.hydrate=function(e){e.forEach(this._insertTag)},t.insert=function(e){this.ctr%(this.isSpeedy?65e3:1)==0&&this._insertTag(function(e){var t=document.createElement("style");return t.setAttribute("data-emotion",e.key),void 0!==e.nonce&&t.setAttribute("nonce",e.nonce),t.appendChild(document.createTextNode("")),t.setAttribute("data-s",""),t}(this));var t=this.tags[this.tags.length-1];if(this.isSpeedy){var n=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]}(t);try{n.insertRule(e,n.cssRules.length)}catch(e){0}}else t.appendChild(document.createTextNode(e));this.ctr++},t.flush=function(){this.tags.forEach((function(e){return e.parentNode&&e.parentNode.removeChild(e)})),this.tags=[],this.ctr=0},e}(),o=Math.abs,a=String.fromCharCode,i=Object.assign;function u(e){return e.trim()}function c(e,t,n){return e.replace(t,n)}function s(e,t){return e.indexOf(t)}function l(e,t){return 0|e.charCodeAt(t)}function f(e,t,n){return e.slice(t,n)}function p(e){return e.length}function d(e){return e.length}function m(e,t){return t.push(e),e}var h=1,v=1,y=0,b=0,g=0,O="";function w(e,t,n,r,o,a,i){return{value:e,root:t,parent:n,type:r,props:o,children:a,line:h,column:v,length:i,return:""}}function S(e,t){return i(w("",null,null,"",null,null,0),e,{length:-e.length},t)}function E(){return g=b>0?l(O,--b):0,v--,10===g&&(v=1,h--),g}function P(){return g=b<y?l(O,b++):0,v++,10===g&&(v=1,h++),g}function x(){return l(O,b)}function R(){return b}function C(e,t){return f(O,e,t)}function A(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function I(e){return h=v=1,y=p(O=e),b=0,[]}function j(e){return O="",e}function k(e){return u(C(b-1,T(91===e?e+2:40===e?e+1:e)))}function M(e){for(;(g=x())&&g<33;)P();return A(e)>2||A(g)>3?"":" "}function D(e,t){for(;--t&&P()&&!(g<48||g>102||g>57&&g<65||g>70&&g<97););return C(e,R()+(t<6&&32==x()&&32==P()))}function T(e){for(;P();)switch(g){case e:return b;case 34:case 39:34!==e&&39!==e&&T(g);break;case 40:41===e&&T(e);break;case 92:P()}return b}function Z(e,t){for(;P()&&e+g!==57&&(e+g!==84||47!==x()););return"/*"+C(t,b-1)+"*"+a(47===e?e:P())}function N(e){for(;!A(x());)P();return C(e,b)}var L="-ms-",V="-moz-",_="-webkit-",F="comm",U="rule",H="decl",$="@keyframes";function z(e,t){for(var n="",r=d(e),o=0;o<r;o++)n+=t(e[o],o,e,t)||"";return n}function B(e,t,n,r){switch(e.type){case"@import":case H:return e.return=e.return||e.value;case F:return"";case $:return e.return=e.value+"{"+z(e.children,r)+"}";case U:e.value=e.props.join(",")}return p(n=z(e.children,r))?e.return=e.value+"{"+n+"}":""}function G(e,t){switch(function(e,t){return(((t<<2^l(e,0))<<2^l(e,1))<<2^l(e,2))<<2^l(e,3)}(e,t)){case 5103:return _+"print-"+e+e;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return _+e+e;case 5349:case 4246:case 4810:case 6968:case 2756:return _+e+V+e+L+e+e;case 6828:case 4268:return _+e+L+e+e;case 6165:return _+e+L+"flex-"+e+e;case 5187:return _+e+c(e,/(\w+).+(:[^]+)/,_+"box-$1$2"+L+"flex-$1$2")+e;case 5443:return _+e+L+"flex-item-"+c(e,/flex-|-self/,"")+e;case 4675:return _+e+L+"flex-line-pack"+c(e,/align-content|flex-|-self/,"")+e;case 5548:return _+e+L+c(e,"shrink","negative")+e;case 5292:return _+e+L+c(e,"basis","preferred-size")+e;case 6060:return _+"box-"+c(e,"-grow","")+_+e+L+c(e,"grow","positive")+e;case 4554:return _+c(e,/([^-])(transform)/g,"$1"+_+"$2")+e;case 6187:return c(c(c(e,/(zoom-|grab)/,_+"$1"),/(image-set)/,_+"$1"),e,"")+e;case 5495:case 3959:return c(e,/(image-set\([^]*)/,_+"$1$`$1");case 4968:return c(c(e,/(.+:)(flex-)?(.*)/,_+"box-pack:$3"+L+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+_+e+e;case 4095:case 3583:case 4068:case 2532:return c(e,/(.+)-inline(.+)/,_+"$1$2")+e;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(p(e)-1-t>6)switch(l(e,t+1)){case 109:if(45!==l(e,t+4))break;case 102:return c(e,/(.+:)(.+)-([^]+)/,"$1"+_+"$2-$3$1"+V+(108==l(e,t+3)?"$3":"$2-$3"))+e;case 115:return~s(e,"stretch")?G(c(e,"stretch","fill-available"),t)+e:e}break;case 4949:if(115!==l(e,t+1))break;case 6444:switch(l(e,p(e)-3-(~s(e,"!important")&&10))){case 107:return c(e,":",":"+_)+e;case 101:return c(e,/(.+:)([^;!]+)(;|!.+)?/,"$1"+_+(45===l(e,14)?"inline-":"")+"box$3$1"+_+"$2$3$1"+L+"$2box$3")+e}break;case 5936:switch(l(e,t+11)){case 114:return _+e+L+c(e,/[svh]\w+-[tblr]{2}/,"tb")+e;case 108:return _+e+L+c(e,/[svh]\w+-[tblr]{2}/,"tb-rl")+e;case 45:return _+e+L+c(e,/[svh]\w+-[tblr]{2}/,"lr")+e}return _+e+L+e+e}return e}function Y(e){return j(W("",null,null,null,[""],e=I(e),0,[0],e))}function W(e,t,n,r,o,i,u,l,f){for(var d=0,h=0,v=u,y=0,b=0,g=0,O=1,w=1,S=1,C=0,A="",I=o,j=i,T=r,L=A;w;)switch(g=C,C=P()){case 40:if(108!=g&&58==L.charCodeAt(v-1)){-1!=s(L+=c(k(C),"&","&\f"),"&\f")&&(S=-1);break}case 34:case 39:case 91:L+=k(C);break;case 9:case 10:case 13:case 32:L+=M(g);break;case 92:L+=D(R()-1,7);continue;case 47:switch(x()){case 42:case 47:m(J(Z(P(),R()),t,n),f);break;default:L+="/"}break;case 123*O:l[d++]=p(L)*S;case 125*O:case 59:case 0:switch(C){case 0:case 125:w=0;case 59+h:b>0&&p(L)-v&&m(b>32?K(L+";",r,n,v-1):K(c(L," ","")+";",r,n,v-2),f);break;case 59:L+=";";default:if(m(T=q(L,t,n,d,h,o,l,A,I=[],j=[],v),i),123===C)if(0===h)W(L,t,T,T,I,i,v,l,j);else switch(y){case 100:case 109:case 115:W(e,T,T,r&&m(q(e,T,T,0,0,o,l,A,o,I=[],v),j),o,j,v,l,r?I:j);break;default:W(L,T,T,T,[""],j,0,l,j)}}d=h=b=0,O=S=1,A=L="",v=u;break;case 58:v=1+p(L),b=g;default:if(O<1)if(123==C)--O;else if(125==C&&0==O++&&125==E())continue;switch(L+=a(C),C*O){case 38:S=h>0?1:(L+="\f",-1);break;case 44:l[d++]=(p(L)-1)*S,S=1;break;case 64:45===x()&&(L+=k(P())),y=x(),h=v=p(A=L+=N(R())),C++;break;case 45:45===g&&2==p(L)&&(O=0)}}return i}function q(e,t,n,r,a,i,s,l,p,m,h){for(var v=a-1,y=0===a?i:[""],b=d(y),g=0,O=0,S=0;g<r;++g)for(var E=0,P=f(e,v+1,v=o(O=s[g])),x=e;E<b;++E)(x=u(O>0?y[E]+" "+P:c(P,/&\f/g,y[E])))&&(p[S++]=x);return w(e,t,n,0===a?U:l,p,m,h)}function J(e,t,n){return w(e,t,n,F,a(g),f(e,2,-2),0)}function K(e,t,n,r){return w(e,t,n,H,f(e,0,r),f(e,r+1,-1),r)}var X=function(e,t,n){for(var r=0,o=0;r=o,o=x(),38===r&&12===o&&(t[n]=1),!A(o);)P();return C(e,b)},Q=function(e,t){return j(function(e,t){var n=-1,r=44;do{switch(A(r)){case 0:38===r&&12===x()&&(t[n]=1),e[n]+=X(b-1,t,n);break;case 2:e[n]+=k(r);break;case 4:if(44===r){e[++n]=58===x()?"&\f":"",t[n]=e[n].length;break}default:e[n]+=a(r)}}while(r=P());return e}(I(e),t))},ee=new WeakMap,te=function(e){if("rule"===e.type&&e.parent&&!(e.length<1)){for(var t=e.value,n=e.parent,r=e.column===n.column&&e.line===n.line;"rule"!==n.type;)if(!(n=n.parent))return;if((1!==e.props.length||58===t.charCodeAt(0)||ee.get(n))&&!r){ee.set(e,!0);for(var o=[],a=Q(t,o),i=n.props,u=0,c=0;u<a.length;u++)for(var s=0;s<i.length;s++,c++)e.props[c]=o[u]?a[u].replace(/&\f/g,i[s]):i[s]+" "+a[u]}}},ne=function(e){if("decl"===e.type){var t=e.value;108===t.charCodeAt(0)&&98===t.charCodeAt(2)&&(e.return="",e.value="")}},re=[function(e,t,n,r){if(e.length>-1&&!e.return)switch(e.type){case H:e.return=G(e.value,e.length);break;case $:return z([S(e,{value:c(e.value,"@","@"+_)})],r);case U:if(e.length)return function(e,t){return e.map(t).join("")}(e.props,(function(t){switch(function(e,t){return(e=t.exec(e))?e[0]:e}(t,/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":return z([S(e,{props:[c(t,/:(read-\w+)/,":"+V+"$1")]})],r);case"::placeholder":return z([S(e,{props:[c(t,/:(plac\w+)/,":"+_+"input-$1")]}),S(e,{props:[c(t,/:(plac\w+)/,":"+V+"$1")]}),S(e,{props:[c(t,/:(plac\w+)/,L+"input-$1")]})],r)}return""}))}}];const oe=function(e){var t=e.key;if("css"===t){var n=document.querySelectorAll("style[data-emotion]:not([data-s])");Array.prototype.forEach.call(n,(function(e){-1!==e.getAttribute("data-emotion").indexOf(" ")&&(document.head.appendChild(e),e.setAttribute("data-s",""))}))}var o=e.stylisPlugins||re;var a,i,u={},c=[];a=e.container||document.head,Array.prototype.forEach.call(document.querySelectorAll('style[data-emotion^="'+t+' "]'),(function(e){for(var t=e.getAttribute("data-emotion").split(" "),n=1;n<t.length;n++)u[t[n]]=!0;c.push(e)}));var s,l,f,p,m=[B,(p=function(e){s.insert(e)},function(e){e.root||(e=e.return)&&p(e)})],h=(l=[te,ne].concat(o,m),f=d(l),function(e,t,n,r){for(var o="",a=0;a<f;a++)o+=l[a](e,t,n,r)||"";return o});i=function(e,t,n,r){s=n,z(Y(e?e+"{"+t.styles+"}":t.styles),h),r&&(v.inserted[t.name]=!0)};var v={key:t,sheet:new r({key:t,container:a,nonce:e.nonce,speedy:e.speedy,prepend:e.prepend,insertionPoint:e.insertionPoint}),nonce:e.nonce,inserted:u,registered:{},insert:i};return v.sheet.hydrate(c),v}},4880:(e,t,n)=>{"use strict";n.d(t,{C:()=>s,E:()=>y,T:()=>f,c:()=>h,h:()=>u,u:()=>d,w:()=>l});var r=n(7363),o=n(6898),a=n(7728),i=n(8947),u={}.hasOwnProperty,c=(0,r.createContext)("undefined"!=typeof HTMLElement?(0,o.Z)({key:"css"}):null);var s=c.Provider,l=function(e){return(0,r.forwardRef)((function(t,n){var o=(0,r.useContext)(c);return e(t,o,n)}))},f=(0,r.createContext)({});var p=r.useInsertionEffect?r.useInsertionEffect:function(e){e()};function d(e){p(e)}var m="__EMOTION_TYPE_PLEASE_DO_NOT_USE__",h=function(e,t){var n={};for(var r in t)u.call(t,r)&&(n[r]=t[r]);return n[m]=e,n},v=function(e){var t=e.cache,n=e.serialized,r=e.isStringTag;(0,a.hC)(t,n,r);d((function(){return(0,a.My)(t,n,r)}));return null},y=l((function(e,t,n){var o=e.css;"string"==typeof o&&void 0!==t.registered[o]&&(o=t.registered[o]);var c=e[m],s=[o],l="";"string"==typeof e.className?l=(0,a.fp)(t.registered,s,e.className):null!=e.className&&(l=e.className+" ");var p=(0,i.O)(s,void 0,(0,r.useContext)(f));l+=t.key+"-"+p.name;var d={};for(var h in e)u.call(e,h)&&"css"!==h&&h!==m&&(d[h]=e[h]);return d.ref=n,d.className=l,(0,r.createElement)(r.Fragment,null,(0,r.createElement)(v,{cache:t,serialized:p,isStringTag:"string"==typeof c}),(0,r.createElement)(c,d))}))},917:(e,t,n)=>{"use strict";n.d(t,{F4:()=>s,iv:()=>c,ms:()=>p,tZ:()=>u});var r=n(7363),o=(n(6898),n(4880)),a=(n(8679),n(7728)),i=n(8947),u=function(e,t){var n=arguments;if(null==t||!o.h.call(t,"css"))return r.createElement.apply(void 0,n);var a=n.length,i=new Array(a);i[0]=o.E,i[1]=(0,o.c)(e,t);for(var u=2;u<a;u++)i[u]=n[u];return r.createElement.apply(null,i)};r.useInsertionEffect?r.useInsertionEffect:r.useLayoutEffect;function c(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,i.O)(t)}var s=function(){var e=c.apply(void 0,arguments),t="animation-"+e.name;return{name:t,styles:"@keyframes "+t+"{"+e.styles+"}",anim:1,toString:function(){return"_EMO_"+this.name+"_"+this.styles+"_EMO_"}}},l=function e(t){for(var n=t.length,r=0,o="";r<n;r++){var a=t[r];if(null!=a){var i=void 0;switch(typeof a){case"boolean":break;case"object":if(Array.isArray(a))i=e(a);else for(var u in i="",a)a[u]&&u&&(i&&(i+=" "),i+=u);break;default:i=a}i&&(o&&(o+=" "),o+=i)}}return o};var f=function(e){var t=e.cache,n=e.serializedArr;(0,o.u)((function(){for(var e=0;e<n.length;e++)(0,a.My)(t,n[e],!1)}));return null},p=(0,o.w)((function(e,t){var n=[],u=function(){for(var e=arguments.length,r=new Array(e),o=0;o<e;o++)r[o]=arguments[o];var u=(0,i.O)(r,t.registered);return n.push(u),(0,a.hC)(t,u,!1),t.key+"-"+u.name},c={css:u,cx:function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return function(e,t,n){var r=[],o=(0,a.fp)(e,r,n);return r.length<2?n:o+t(r)}(t.registered,u,l(n))},theme:(0,r.useContext)(o.T)},s=e.children(c);return!0,(0,r.createElement)(r.Fragment,null,(0,r.createElement)(f,{cache:t,serializedArr:n}),s)}))},7728:(e,t,n)=>{"use strict";n.d(t,{My:()=>a,fp:()=>r,hC:()=>o});function r(e,t,n){var r="";return n.split(" ").forEach((function(n){void 0!==e[n]?t.push(e[n]+";"):r+=n+" "})),r}var o=function(e,t,n){var r=e.key+"-"+t.name;!1===n&&void 0===e.registered[r]&&(e.registered[r]=t.styles)},a=function(e,t,n){o(e,t,n);var r=e.key+"-"+t.name;if(void 0===e.inserted[t.name]){var a=t;do{e.insert(t===a?"."+r:"",a,e.sheet,!0);a=a.next}while(void 0!==a)}}},8947:(e,t,n)=>{"use strict";n.d(t,{O:()=>m});const r=function(e){for(var t,n=0,r=0,o=e.length;o>=4;++r,o-=4)t=1540483477*(65535&(t=255&e.charCodeAt(r)|(255&e.charCodeAt(++r))<<8|(255&e.charCodeAt(++r))<<16|(255&e.charCodeAt(++r))<<24))+(59797*(t>>>16)<<16),n=1540483477*(65535&(t^=t>>>24))+(59797*(t>>>16)<<16)^1540483477*(65535&n)+(59797*(n>>>16)<<16);switch(o){case 3:n^=(255&e.charCodeAt(r+2))<<16;case 2:n^=(255&e.charCodeAt(r+1))<<8;case 1:n=1540483477*(65535&(n^=255&e.charCodeAt(r)))+(59797*(n>>>16)<<16)}return(((n=1540483477*(65535&(n^=n>>>13))+(59797*(n>>>16)<<16))^n>>>15)>>>0).toString(36)};const o={animationIterationCount:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1};var a=/[A-Z]|^ms/g,i=/_EMO_([^_]+?)_([^]*?)_EMO_/g,u=function(e){return 45===e.charCodeAt(1)},c=function(e){return null!=e&&"boolean"!=typeof e},s=function(e){var t=Object.create(null);return function(n){return void 0===t[n]&&(t[n]=e(n)),t[n]}}((function(e){return u(e)?e:e.replace(a,"-$&").toLowerCase()})),l=function(e,t){switch(e){case"animation":case"animationName":if("string"==typeof t)return t.replace(i,(function(e,t,n){return p={name:t,styles:n,next:p},t}))}return 1===o[e]||u(e)||"number"!=typeof t||0===t?t:t+"px"};function f(e,t,n){if(null==n)return"";if(void 0!==n.__emotion_styles)return n;switch(typeof n){case"boolean":return"";case"object":if(1===n.anim)return p={name:n.name,styles:n.styles,next:p},n.name;if(void 0!==n.styles){var r=n.next;if(void 0!==r)for(;void 0!==r;)p={name:r.name,styles:r.styles,next:p},r=r.next;return n.styles+";"}return function(e,t,n){var r="";if(Array.isArray(n))for(var o=0;o<n.length;o++)r+=f(e,t,n[o])+";";else for(var a in n){var i=n[a];if("object"!=typeof i)null!=t&&void 0!==t[i]?r+=a+"{"+t[i]+"}":c(i)&&(r+=s(a)+":"+l(a,i)+";");else if(!Array.isArray(i)||"string"!=typeof i[0]||null!=t&&void 0!==t[i[0]]){var u=f(e,t,i);switch(a){case"animation":case"animationName":r+=s(a)+":"+u+";";break;default:r+=a+"{"+u+"}"}}else for(var p=0;p<i.length;p++)c(i[p])&&(r+=s(a)+":"+l(a,i[p])+";")}return r}(e,t,n);case"function":if(void 0!==e){var o=p,a=n(e);return p=o,f(e,t,a)}}if(null==t)return n;var i=t[n];return void 0!==i?i:n}var p,d=/label:\s*([^\s;\n{]+)\s*(;|$)/g;var m=function(e,t,n){if(1===e.length&&"object"==typeof e[0]&&null!==e[0]&&void 0!==e[0].styles)return e[0];var o=!0,a="";p=void 0;var i=e[0];null==i||void 0===i.raw?(o=!1,a+=f(n,t,i)):a+=i[0];for(var u=1;u<e.length;u++)a+=f(n,t,e[u]),o&&(a+=i[u]);d.lastIndex=0;for(var c,s="";null!==(c=d.exec(a));)s+="-"+c[1];return{name:r(a)+s,styles:a,next:p}}},336:(e,t,n)=>{"use strict";n.d(t,{Z:()=>a});var r=n(7537),o=n(8003);function a(e){var t=e.type,n=void 0===t?"field":t,a=e.id,i=e.deleteProp;return React.createElement(r.Dashicon,{className:"sss-action sss-action--delete--".concat(n),icon:"field"===n?"dismiss":"trash",onClick:function(e){confirm((0,o.__)("Are you sure you want to delete?"))&&(e.stopPropagation(),i(a))}})}},2388:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>m});var r=n(4619),o=n(7537),a=n(2610),i=n(8003),u=n(9518),c=n(1457);function s(e){return function(e){if(Array.isArray(e))return p(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||f(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function l(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==n)return;var r,o,a=[],i=!0,u=!1;try{for(n=n.call(e);!(i=(r=n.next()).done)&&(a.push(r.value),!t||a.length!==t);i=!0);}catch(e){u=!0,o=e}finally{try{i||null==n.return||n.return()}finally{if(u)throw o}}return a}(e,t)||f(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function f(e,t){if(e){if("string"==typeof e)return p(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?p(e,t):void 0}}function p(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var d=function(e){var t=e.name,n=e.item,r=e.removeItem,u=(0,a.useRef)();return React.createElement("div",{className:"sss-custom"},React.createElement("input",{type:"hidden",name:"".concat(t,"[id]"),defaultValue:n.id}),React.createElement("input",{type:"text",placeholder:"Enter key",name:"".concat(t,"[key]"),defaultValue:n.key}),React.createElement("div",{className:"sss-input-wrapper"},React.createElement("input",{type:"text",placeholder:"Enter value",name:"".concat(t,"[value]"),defaultValue:n.value,ref:u}),React.createElement(c.default,{inputRef:u})),React.createElement("button",{type:"button",className:"sss-custom__remove",title:(0,i.__)("Remove","meta-box-builder"),onClick:function(){return r(n.id)}},React.createElement(o.Dashicon,{icon:"dismiss"})))};const m=function(e){var t=e.property,n=e.className,o=(e.deleteProp,t.id),c=t.label,f=t.required,p=t.std,m=t.tooltip,h=l((0,a.useState)(Object.values(p||{})),2),v=h[0],y=h[1],b=function(e){return y((function(t){return t.filter((function(t){return t.id!==e}))}))};return React.createElement(r.oT,{className:n,label:c,id:o,required:f,tooltip:m},v.map((function(e){return React.createElement(d,{key:e.id,item:e,removeItem:b,name:"".concat(o,"[").concat(e.id,"]")})})),React.createElement("button",{type:"button",className:"button",onClick:function(){return y((function(e){return[].concat(s(e),[{key:"",value:"",id:(0,u.Uz)()}])}))}},(0,i.__)("+ Add New","slim-seo-schema")))}},4343:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>m});var r=n(4619),o=n(2610),a=n(336),i=n(1457);function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function c(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach((function(t){s(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function s(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function l(e){return function(e){if(Array.isArray(e))return d(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||p(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function f(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==n)return;var r,o,a=[],i=!0,u=!1;try{for(n=n.call(e);!(i=(r=n.next()).done)&&(a.push(r.value),!t||a.length!==t);i=!0);}catch(e){u=!0,o=e}finally{try{i||null==n.return||n.return()}finally{if(u)throw o}}return a}(e,t)||p(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function p(e,t){if(e){if("string"==typeof e)return d(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?d(e,t):void 0}}function d(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}const m=(0,o.memo)((function(e){var t=e.className,n=e.property,u=e.setProperties,s=e.deleteProp,p=(0,o.useRef)(),d=(0,o.useRef)(),m=n.id,h=n.label,v=n.required,y=n.std,b=n.tooltip,g=n.options,O=m.replace("[fields]",""),w=Array.isArray(g)?g:Object.entries(g).reduce((function(e,t){var n=f(t,2),r=n[0],o=n[1];return[].concat(l(e),[{value:r,label:o}])}),[]),S=function(e){n.dependant&&u((function(t){return t.map((function(t){if(!t.dependency)return t;var n=t.dependency.split(":")[1]!==e;return c(c({},t),{},{hidden:n})}))}))};(0,o.useEffect)((function(){S(y)}),[]);return React.createElement(r.oT,{className:t,label:h,id:m,required:v,tooltip:b},React.createElement("input",{type:"hidden",ref:p,name:m,defaultValue:y}),React.createElement("div",{className:"sss-input-wrapper"},React.createElement("input",{type:"text",list:O,ref:d,onChange:function(e){p.current.value=w.reduce((function(e,t){var n=t.value,r=t.label;return e.replace(r,n)}),e.target.value),S(p.current.value)},defaultValue:w.reduce((function(e,t){var n=t.value,r=t.label;return String(e).replace(n,r)}),y)}),React.createElement(i.default,{inputRef:p,extraInputRef:d})),React.createElement("datalist",{id:O},w.map((function(e){return React.createElement("option",{key:e.value},e.label)}))),v||React.createElement(a.Z,{id:m,deleteProp:s}))}),(function(e,t){return e.property.id===t.property.id}))},9592:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>c});var r=n(4619),o=n(7537),a=n(2610),i=n(336),u=n(1457);const c=(0,a.memo)((function(e){var t=e.className,n=e.property,c=e.deleteProp,s=(0,a.useRef)(),l=n.id,f=n.label,p=n.required,d=n.std,m=n.tooltip;return React.createElement(r.oT,{className:t,label:f,id:l,required:p,tooltip:m},React.createElement("div",{className:"sss-input-wrapper"},React.createElement(o.Dropdown,{className:"sss-datetime",position:"bottom left",renderToggle:function(e){var t=e.onToggle;return React.createElement("input",{type:"text",id:l,name:l,defaultValue:d,ref:s,onFocus:t})},renderContent:function(e){e.onToggle;return React.createElement(o.DateTimePicker,{onChange:function(e){return s.current.value=e.replace("T"," ").replace(/\..*$/,"")}})}}),React.createElement(u.default,{inputRef:s})),p||React.createElement(i.Z,{id:l,deleteProp:c}))}),(function(e,t){return e.property.id===t.property.id}))},1910:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>m});var r=n(4619),o=n(2610),a=n(8003),i=n(6010),u=n(9518);function c(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function s(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?c(Object(n),!0).forEach((function(t){l(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):c(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function l(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function f(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==n)return;var r,o,a=[],i=!0,u=!1;try{for(n=n.call(e);!(i=(r=n.next()).done)&&(a.push(r.value),!t||a.length!==t);i=!0);}catch(e){u=!0,o=e}finally{try{i||null==n.return||n.return()}finally{if(u)throw o}}return a}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return p(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return p(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function p(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var d=(0,o.memo)((function(e){var t=e.item,n=e.deleteProp,r=t,c=r.type,l=void 0===c?"Text":c,f=r.dependency,p=(r.parentID,(0,u.Ql)(l));"Group"!==l&&(t=s(s({},t),{},{label:""}));var d=(0,i.Z)("sss-field","sss-field--".concat(l.toLowerCase()),f&&"dep:".concat(f));return React.createElement(o.Suspense,{fallback:(0,a.__)("Loading fields... Please wait","slim-seo-schema")},React.createElement(p,{className:d,deleteProp:n,property:t}))}),(function(e,t){return e.item.id===t.item.id}));const m=function(e){var t=e.property,n=f((0,o.useState)({}),2),i=n[0],c=n[1],p=t.id,m=t.label,h=t.required,v=(t.std,t.tooltip);(0,o.useEffect)((function(){if(t.std){var e={};Object.entries(t.std).forEach((function(n){var r=f(n,2),o=r[0],a=r[1],i="".concat(t.id,"[").concat(o,"]");e=s(s({},e),{},l({},i,s(s({},t),{},{id:i,std:a})))})),c(s(s({},i),e)),t.std=""}}),[]);var y=function(e){c((function(t){var n=Object.entries(t).filter((function(t){var n=f(t,2),r=n[0];n[1];return r!==e}));return Object.fromEntries(n)}))};return React.createElement(r.oT,{className:"sss-field sss-field--duplicate",label:m,required:h,id:p,tooltip:v},Object.entries(i).map((function(e){var t=f(e,2),n=t[0],r=t[1];return React.createElement(d,{key:n,item:r,deleteProp:y})})),React.createElement("button",{type:"button",onClick:function(){return function(e){var t="".concat(e.id,"[").concat((0,u.Uz)(),"]");c((function(n){return s(s({},n),{},l({},t,s(s({},e),{},{id:t,std:e.fieldStd})))}))}(t)},className:"button"},(0,a.__)("+ Add new","slim-seo-schema")))}},4151:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>u});var r=n(2610),o=n(8003),a=n(6010),i=n(9518);const u=(0,r.memo)((function(e){var t=e.property,n=e.deleteProp,u=e.setProperties,c=t.type,s=void 0===c?"Text":c,l=t.dependency,f=t.parentID,p=(0,i.Ql)(s),d=(0,a.Z)("sss-field","sss-field--".concat(s.toLowerCase()),l&&"dep:".concat(f).concat(l));return React.createElement(r.Suspense,{fallback:(0,o.__)("Loading fields... Please wait","slim-seo-schema")},React.createElement(p,{className:d,property:t,deleteProp:n,setProperties:u}))}),(function(e,t){return e.property.id===t.property.id&&e.property.type===t.property.type}))},3502:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>a});var r=n(7537),o=n(8003);const a=function(e){var t=e.property;return React.createElement("a",{className:"sss-docs-link",href:t.url,target:"_blank",rel:"noopener noreferrer"},(0,o.__)("Google documentation","slim-seo-schema")," ",React.createElement(r.Icon,{icon:"external",size:14}))}},9406:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>P});var r=n(4619),o=n(7537),a=n(2610),i=n(8003),u=n(336),c=n(6521),s=n(6010);function l(e){var t=e.className;return React.createElement("button",{type:"button",className:"sss-action sss-action--toggle dashicons ".concat(t)})}function f(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==n)return;var r,o,a=[],i=!0,u=!1;try{for(n=n.call(e);!(i=(r=n.next()).done)&&(a.push(r.value),!t||a.length!==t);i=!0);}catch(e){u=!0,o=e}finally{try{i||null==n.return||n.return()}finally{if(u)throw o}}return a}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return p(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return p(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function p(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var d=wp.element,m=(d.useState,d.useReducer);function h(e){var t=e.title,n=e.defaultExpanded,r=void 0!==n&&n,o=e.className,a=e.children,i=e.required,c=e.id,p=e.deleteProp,d=f(m((function(e){return!e}),r),2),h=d[0],v=d[1],y=(0,s.Z)("sss-panel",o,{"sss-panel--expanded":h}),b="dashicons-".concat(h?"arrow-up-alt2":"arrow-down-alt2");return t?React.createElement("div",{className:y},t&&React.createElement("div",{className:"sss-panel__header",onClick:v},React.createElement("div",{className:"sss-panel__title"},t),React.createElement("div",{className:"sss-panel__actions"},!i&&React.createElement(u.Z,{deleteProp:p,id:c,type:"panel"}),React.createElement(l,{className:b}))),React.createElement("div",{className:"sss-panel__body"},a)):a}var v=n(4953);function y(e){return function(e){if(Array.isArray(e))return E(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||S(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function b(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function g(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?b(Object(n),!0).forEach((function(t){O(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):b(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function O(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function w(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==n)return;var r,o,a=[],i=!0,u=!1;try{for(n=n.call(e);!(i=(r=n.next()).done)&&(a.push(r.value),!t||a.length!==t);i=!0);}catch(e){u=!0,o=e}finally{try{i||null==n.return||n.return()}finally{if(u)throw o}}return a}(e,t)||S(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function S(e,t){if(e){if("string"==typeof e)return E(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?E(e,t):void 0}}function E(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}const P=(0,a.memo)((function(e){var t=e.property,n=e.deleteProp,s=w((0,a.useState)([]),2),l=s[0],f=s[1],p=w((0,a.useState)([]),2),d=p[0],m=p[1],b=w((0,a.useState)([]),2),O=b[0],S=b[1],E=t.std||{},P=t.id;(0,a.useEffect)((function(){f((function(){return t.fields.map((function(e){var t=x(e)||e.required||e.show,n=e.name?"".concat(P,"[").concat(e.name,"]"):"".concat(P),r="".concat(n,"[").concat(e.id,"]"),o=R(e);return g(g({},e),{},{id:r,std:o,parentID:n,visible:t,hidden:!1})}))}))}),[]),(0,a.useEffect)((function(){m((function(){return l.filter((function(e){return e.visible&&!e.hidden}))})),S((function(){return l.filter((function(e){return!e.visible&&!e.hidden}))}))}),[l]);var x=function(e){return e.name?!!E[e.name]&&E[e.name].hasOwnProperty(e.id):E.hasOwnProperty(e.id)},R=function(e){var t=e.std||"";return e.name?E[e.name]&&E[e.name][e.id]||t:E[e.id]||t},C=function(e){var t=d.filter((function(t){return t.id===e}));m((function(t){return t.filter((function(t){return t.id!==e}))})),S((function(e){return[].concat(y(e),y(t))}))},A=t.cloneable,I=t.cloneItemHeading,j=t.label,k=t.tooltip,M=t.panelNoBorder,D=I||"",T=t.required&&!A,Z=!A&&j,N=!A||M?"sss-panel--no-border":"";return React.createElement(r.oT,{label:Z,id:t.id,required:T,tooltip:k},React.createElement(h,{title:D,className:N,required:T,defaultExpanded:!0,deleteProp:n,id:t.id},d.map((function(e){return React.createElement(v.Z,{key:e.id,property:e,setProperties:f,properties:l,deleteProp:C})})),Object.keys(O).length>0&&React.createElement(o.Dropdown,{className:"sss-dropdown",position:"bottom right",renderToggle:function(e){var t=e.onToggle;return React.createElement("a",{href:"#",onClick:function(e){e.preventDefault(),t()}},(0,i.__)("+ Add Property","slim-seo-schema"))},renderContent:function(e){var t=e.onToggle;return React.createElement(c.Z,{items:O,onSelect:function(e){return function(e,t){t();var n=e.target.dataset.id,r=O.filter((function(e){return e.id===n}));r=r.map((function(e){return g(g({},e),{},{std:""})})),m((function(e){return[].concat(y(e),y(r))})),S((function(e){return e.filter((function(e){return e.id!==n}))}))}(e,t)}})}}),T||React.createElement(u.Z,{id:P,deleteProp:n})))}))},2177:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>x});var r=n(4619),o=n(7537),a=n(2610),i=n(8003),u=n(7462),c=n(7363),s=n(3520),l=n(4766),f=n(4942),p=n(9344),d=n(885),m=n(4925),h=["defaultOptions","cacheOptions","loadOptions","options","isLoading","onInputChange","filterOption"];n(1533);const v=(0,c.forwardRef)((function(e,t){var n=function(e){var t=e.defaultOptions,n=void 0!==t&&t,r=e.cacheOptions,o=void 0!==r&&r,a=e.loadOptions;e.options;var i=e.isLoading,u=void 0!==i&&i,s=e.onInputChange,l=e.filterOption,v=void 0===l?null:l,y=(0,m.Z)(e,h),b=y.inputValue,g=(0,c.useRef)(void 0),O=(0,c.useRef)(!1),w=(0,c.useState)(Array.isArray(n)?n:void 0),S=(0,d.Z)(w,2),E=S[0],P=S[1],x=(0,c.useState)(void 0!==b?b:""),R=(0,d.Z)(x,2),C=R[0],A=R[1],I=(0,c.useState)(!0===n),j=(0,d.Z)(I,2),k=j[0],M=j[1],D=(0,c.useState)(void 0),T=(0,d.Z)(D,2),Z=T[0],N=T[1],L=(0,c.useState)([]),V=(0,d.Z)(L,2),_=V[0],F=V[1],U=(0,c.useState)(!1),H=(0,d.Z)(U,2),$=H[0],z=H[1],B=(0,c.useState)({}),G=(0,d.Z)(B,2),Y=G[0],W=G[1],q=(0,c.useState)(void 0),J=(0,d.Z)(q,2),K=J[0],X=J[1],Q=(0,c.useState)(void 0),ee=(0,d.Z)(Q,2),te=ee[0],ne=ee[1];o!==te&&(W({}),ne(o)),n!==K&&(P(Array.isArray(n)?n:void 0),X(n)),(0,c.useEffect)((function(){return O.current=!0,function(){O.current=!1}}),[]);var re=(0,c.useCallback)((function(e,t){if(!a)return t();var n=a(e,t);n&&"function"==typeof n.then&&n.then(t,(function(){return t()}))}),[a]);(0,c.useEffect)((function(){!0===n&&re(C,(function(e){O.current&&(P(e||[]),M(!!g.current))}))}),[]);var oe=(0,c.useCallback)((function(e,t){var n=(0,p.N)(e,t,s);if(!n)return g.current=void 0,A(""),N(""),F([]),M(!1),void z(!1);if(o&&Y[n])A(n),N(n),F(Y[n]),M(!1),z(!1);else{var r=g.current={};A(n),M(!0),z(!Z),re(n,(function(e){O&&r===g.current&&(g.current=void 0,M(!1),N(n),F(e||[]),z(!1),W(e?(0,p.a)((0,p.a)({},Y),{},(0,f.Z)({},n,e)):Y))}))}}),[o,re,Z,Y,s]),ae=$?[]:C&&Z?_:E||[];return(0,p.a)((0,p.a)({},y),{},{options:ae,isLoading:k||u,onInputChange:oe,filterOption:v})}(e),r=(0,l.u)(n);return c.createElement(s.S,(0,u.Z)({ref:t},r))}));var y=n(9518);function b(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function g(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?b(Object(n),!0).forEach((function(t){O(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):b(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function O(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function w(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==n)return;var r,o,a=[],i=!0,u=!1;try{for(n=n.call(e);!(i=(r=n.next()).done)&&(a.push(r.value),!t||a.length!==t);i=!0);}catch(e){u=!0,o=e}finally{try{i||null==n.return||n.return()}finally{if(u)throw o}}return a}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return S(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return S(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function S(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var E=function(e){var t=e.group,n=e.locations,r=e.base,o=w((0,a.useState)(t),2),i=o[0],u=o[1],c=function(){return u((function(e){return g(g({},e),{},O({},(0,y.Uz)(),{name:"general:all",value:"all",label:SSSchema.text.all}))}))},s=function(e){return u((function(t){var n=g({},t);return delete n[e],n}))};return 0===Object.entries(i).length?"":React.createElement("div",{className:"sss-location-group"},Object.entries(i).map((function(e){var t=w(e,2),o=t[0],a=t[1];return React.createElement(P,{key:o,rule:a,ruleId:o,locations:n,base:"".concat(r,"[").concat(o,"]"),addRule:c,removeRule:s})})),React.createElement("div",{className:"sss-location-group__and"},SSSchema.text.and))},P=function(e){var t=e.rule,n=e.ruleId,r=e.locations,i=e.base,u=e.addRule,c=e.removeRule,s=((0,a.useRef)(),w((0,a.useState)(t),2)),l=s[0],f=s[1],p=!l.name.includes("general:")&&!l.name.includes(":archive");return React.createElement("div",{className:"sss-location-rule"},React.createElement("select",{className:"sss-location-rule__name",name:"".concat(i,"[name]"),defaultValue:l.name,onChange:function(e){var t=e.target.value;return f((function(e){return g(g({},e),{},{name:t})}))}},Object.values(r).map((function(e){var t=e.label,n=e.options;return React.createElement("optgroup",{key:t,label:t},n.map((function(e){var t=e.value,n=e.label;return React.createElement("option",{key:t,value:t},n)})))}))),p&&React.createElement(v,{className:"sss-location-rule__value",classNamePrefix:"react-select",name:"".concat(i,"[value]"),key:l.name,defaultOptions:!0,loadOptions:function(e){var t=/:post$/.test(l.name)?"posts":"terms";return(0,y.WY)(t,{term:e,name:l.name,selected:l.value})},defaultValue:t,onChange:function(e,t){"select-option"===t.action&&f((function(t){return g(g({},t),e)}))}}),React.createElement("input",{type:"hidden",name:"".concat(i,"[label]"),value:l.label}),React.createElement("button",{type:"button",className:"button sss-location-rule__add",onClick:u},SSSchema.text.or),React.createElement("button",{type:"button",className:"sss-location-rule__remove",onClick:function(){return c(n)}},React.createElement(o.Dashicon,{icon:"dismiss"})))};const x=function(e){var t=e.className,n=e.property,o=w((0,a.useState)((function(){if(!Array.isArray(n.std))return n.std;var e={};return n.std.forEach((function(t){var n=(0,y.Uz)();e[n]={},t.forEach((function(t){var r=(0,y.Uz)();e[n][r]=t}))})),e})),2),u=o[0],c=o[1];return React.createElement(r.oT,{className:t,label:(0,i.__)("Location","slim-seo-schema")},Object.entries(u).map((function(e){var t=w(e,2),r=t[0],o=t[1];return React.createElement(E,{key:r,group:o,locations:n.locations,base:"".concat(n.id,"[").concat(r,"]")})})),React.createElement("button",{type:"button",className:"button sss-location-add-group",onClick:function(){return c((function(e){var t=g({},e),n=(0,y.Uz)(),r=(0,y.Uz)();return t[n]=O({},r,{name:"general:all",value:"all",label:SSSchema.text.all}),t}))}},SSSchema.text.addGroup))}},7762:(e,t,n)=>{"use strict";function r(e){var t=e.property;return React.createElement("input",{type:"hidden",name:"".concat(t.id),value:t.std})}n.r(t),n.d(t,{default:()=>r})},5937:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>c});var r=n(4619),o=n(7537),a=n(2610),i=n(336),u=n(1457);const c=(0,a.memo)((function(e){var t=e.className,n=e.property,c=e.deleteProp,s=(0,a.useRef)(),l=n.id,f=n.label,p=n.required,d=n.std,m=n.tooltip;return React.createElement(r.oT,{className:t,label:f,id:l,required:p,tooltip:m},React.createElement("div",{className:"sss-input-wrapper"},React.createElement("input",{type:"text",id:l,name:l,defaultValue:d,ref:s}),React.createElement(o.Button,{icon:"format-image",onClick:function(e){e.preventDefault();var t=wp.media({multiple:!1,title:SSSchema.mediaPopupTitle});t.open(),t.off("select"),t.on("select",(function(){var e=t.state().get("selection").first().toJSON().url;s.current.value+=e}))},className:"sss-insert-image"}),React.createElement(u.default,{inputRef:s})),p||React.createElement(i.Z,{id:l,deleteProp:c}))}),(function(e,t){return e.property.id===t.property.id}))},1457:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>j});var r=n(7537),o=n(2610),a=n(8003),i=n(4766),u=n(7462),c=n(7363),s=n(3520),l=n(5671),f=n(3144),p=n(9340),d=n(9344),m=n(4880),h=n(6898),v=n(845),y=(n(1533),(0,c.forwardRef)((function(e,t){var n=(0,i.u)(e);return c.createElement(s.S,(0,u.Z)({ref:t},n))})));c.Component;const b=y;var g=n(1304),O=n.n(g),w=n(3667),S=n(9518),E=n(6521);function P(e){return function(e){if(Array.isArray(e))return C(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||R(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function x(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==n)return;var r,o,a=[],i=!0,u=!1;try{for(n=n.call(e);!(i=(r=n.next()).done)&&(a.push(r.value),!t||a.length!==t);i=!0);}catch(e){u=!0,o=e}finally{try{i||null==n.return||n.return()}finally{if(u)throw o}}return a}(e,t)||R(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function R(e,t){if(e){if("string"==typeof e)return C(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?C(e,t):void 0}}function C(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var A=function(e){var t=e.onSelect,n=x((0,o.useState)([]),2),r=n[0],i=n[1],u=(0,o.useContext)(w.u).schemaLinks,c={};return Object.values(u).forEach((function(e){var t=O()(e,{lower:!0,replacement:"_"});t=t.replace(".","_"),c["schemas.".concat(t)]=e})),(0,o.useEffect)((function(){(0,S.WY)("data",{type:"variables"}).then((function(e){var t=P(e);t.push({label:(0,a.__)("Schema","slim-seo-schema"),options:c}),i(t)}))}),[]),React.createElement(E.Z,{items:r,group:!0,hasSearch:!0,onSelect:t})},I=function(e){var t=e.setShowModal,n=e.setValue,r=x((0,o.useState)([]),2),i=r[0],u=r[1];(0,o.useEffect)((function(){(0,S.WY)("meta_keys").then(u)}),[]);var c=function(){return t(!1)};return React.createElement(React.Fragment,null,React.createElement("div",{className:"sss-modal-overlay",onClick:c}),React.createElement("div",{className:"sss-modal-body"},React.createElement("h3",{className:"sss-modal-heading"},(0,a.__)("Select a custom field","slim-seo-schema"),React.createElement("span",{className:"sss-modal__close",onClick:c},"×")),React.createElement(b,{classNamePrefix:"react-select",options:i,defaultOptions:!0,onChange:function(e){n("{{ post.custom_field.".concat(e.value," }}")),t(!1)}})))};const j=function(e){var t=e.inputRef,n=e.extraInputRef,a=x((0,o.useState)(!1),2),i=a[0],u=a[1],c=function(e){t.current.value+=e,n&&(n.current.value+=e)};return React.createElement(React.Fragment,null,React.createElement(r.Dropdown,{className:"sss-dropdown sss-inserter",position:"bottom left",renderToggle:function(e){var t=e.onToggle;return React.createElement(r.Button,{icon:"ellipsis",onClick:t})},renderContent:function(e){var t=e.onToggle;return React.createElement(A,{onSelect:function(e){return function(e,t){t(),"post.custom_field"!==e.target.dataset.value?c("{{ ".concat(e.target.dataset.value," }}")):u(!0)}(e,t)}})}}),i&&React.createElement(I,{setShowModal:u,setValue:c}))}},9304:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>a});var r=n(7537),o=n(8003);const a=function(e){var t=e.property;return React.createElement("a",{className:"sss-docs-link",href:t.url,target:"_blank",rel:"noopener noreferrer"},(0,o.__)("Schema.org documentation","slim-seo-schema")," ",React.createElement(r.Icon,{icon:"external",size:14}))}},6534:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>c});var r=n(4619),o=n(2610);function a(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function i(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?a(Object(n),!0).forEach((function(t){u(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):a(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function u(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const c=(0,o.memo)((function(e){var t=e.className,n=e.property,a=e.setProperties,u=n.id,c=n.label,s=n.required,l=n.std,f=n.tooltip,p=n.options,d=function(e){n.dependant&&a((function(t){return t.map((function(t){if(!t.dependency)return t;var n=t.dependency.split(":")[1]!==e;return i(i({},t),{},{hidden:n})}))}))};return(0,o.useEffect)((function(){d(l)}),[]),React.createElement(r.Ph,{className:t,label:c,id:u,name:u,value:l,required:s,options:p,tooltip:f,onChange:function(e){return d(e.target.value)}})}),(function(e,t){return e.property.id===t.property.id}))},4632:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>c});var r=n(4619),o=n(2610),a=n(336),i=n(1457);function u(){return u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},u.apply(this,arguments)}const c=(0,o.memo)((function(e){var t=e.className,n=e.property,c=e.deleteProp,s=e.onChange,l=(0,o.useRef)(),f=n.id,p=(n.label,n.required),d=n.std;n.tooltip;return React.createElement(r.oT,u({className:t},n),React.createElement("div",{className:"sss-input-wrapper"},React.createElement("input",{type:"text",id:f,name:f,defaultValue:d,ref:l,onChange:s}),React.createElement(i.default,{inputRef:l})),p||React.createElement(a.Z,{id:f,deleteProp:c}))}),(function(e,t){return e.property.id===t.property.id}))},8787:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>u});var r=n(4619),o=n(2610),a=n(336),i=n(1457);const u=(0,o.memo)((function(e){var t=e.className,n=e.property,u=e.deleteProp,c=(0,o.useRef)(),s=n.id,l=n.label,f=n.required,p=n.std,d=n.tooltip,m=n.hasInsert,h=void 0===m||m,v=n.rows,y=void 0===v?2:v;return React.createElement(r.oT,{className:t,label:l,id:s,required:f,tooltip:d},React.createElement("div",{className:"sss-input-wrapper"},React.createElement("textarea",{ref:c,defaultValue:p,id:s,name:s,rows:y}),h&&React.createElement(i.default,{inputRef:c})),f||React.createElement(a.Z,{id:s,deleteProp:u}))}),(function(e,t){return e.property.id===t.property.id}))},6521:(e,t,n)=>{"use strict";n.d(t,{Z:()=>d});var r=n(2610),o=n(8003),a=n(3517),i=n.n(a);function u(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==n)return;var r,o,a=[],i=!0,u=!1;try{for(n=n.call(e);!(i=(r=n.next()).done)&&(a.push(r.value),!t||a.length!==t);i=!0);}catch(e){u=!0,o=e}finally{try{i||null==n.return||n.return()}finally{if(u)throw o}}return a}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return c(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return c(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function c(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var s=function(e){var t=e.group,n=e.searchTerm.toLowerCase(),o=Object.entries(t.options).filter((function(e){var t=u(e,2),r=(t[0],t[1]);return!n||r.toLowerCase().includes(n)}));return o.length>0&&React.createElement(React.Fragment,null,React.createElement("div",{className:"sss-dropdown__title"},t.label),React.createElement("div",{className:"sss-dropdown__items"},o.map((function(e){var t=u(e,2),n=t[0],o=t[1];return React.createElement(r.RawHTML,{key:n,className:"sss-dropdown__item","data-value":n},o)}))))},l=function(e){return e.items.map((function(e){return React.createElement(r.RawHTML,{key:e.id,className:"sss-dropdown__item","data-type":e.type,"data-id":e.id},p(e))}))},f=function(e){var t=e.handleSearch;return React.createElement("div",{className:"sss-dropdown__search"},React.createElement("input",{onInput:t,type:"text",placeholder:(0,o.__)("Search...","slim-seo-schema")}))},p=function e(t){var n=i().get(t,"label","");return n||(i().get(t,"fields",[]).forEach((function(t){n||(n=e(t))})),n)};function d(e){var t=e.items,n=void 0===t?[]:t,o=e.group,a=void 0!==o&&o,i=e.hasSearch,c=void 0!==i&&i,p=e.onSelect,d=u((0,r.useState)(""),2),m=d[0],h=d[1];return React.createElement("div",{onClick:function(e){return e.target.matches(".sss-dropdown__item")&&p(e)}},c&&React.createElement(f,{handleSearch:function(e){return h(e.target.value)}}),a?n.map((function(e,t){return React.createElement(s,{key:t,group:e,searchTerm:m})})):React.createElement(l,{items:n}))}},4953:(e,t,n)=>{"use strict";n.d(t,{Z:()=>a});var r=n(1910),o=n(4151);const a=function(e){var t=e.setProperties,n=e.property,a=e.deleteProp;return n.cloneable?React.createElement(r.default,{property:n}):React.createElement(o.default,{property:n,setProperties:t,deleteProp:a})}},3667:(e,t,n)=>{"use strict";n.d(t,{u:()=>se,Z:()=>le});var r=n(2610),o=n(3517),a=n.n(o);function i(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];throw Error("[Immer] minified error nr: "+e+(n.length?" "+n.map((function(e){return"'"+e+"'"})).join(","):"")+". Find the full error at: https://bit.ly/3cXEKWf")}function u(e){return!!e&&!!e[W]}function c(e){return!!e&&(function(e){if(!e||"object"!=typeof e)return!1;var t=Object.getPrototypeOf(e);if(null===t)return!0;var n=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return n===Object||"function"==typeof n&&Function.toString.call(n)===q}(e)||Array.isArray(e)||!!e[Y]||!!e.constructor[Y]||h(e)||v(e))}function s(e,t,n){void 0===n&&(n=!1),0===l(e)?(n?Object.keys:J)(e).forEach((function(r){n&&"symbol"==typeof r||t(r,e[r],e)})):e.forEach((function(n,r){return t(r,n,e)}))}function l(e){var t=e[W];return t?t.i>3?t.i-4:t.i:Array.isArray(e)?1:h(e)?2:v(e)?3:0}function f(e,t){return 2===l(e)?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function p(e,t){return 2===l(e)?e.get(t):e[t]}function d(e,t,n){var r=l(e);2===r?e.set(t,n):3===r?(e.delete(t),e.add(n)):e[t]=n}function m(e,t){return e===t?0!==e||1/e==1/t:e!=e&&t!=t}function h(e){return $&&e instanceof Map}function v(e){return z&&e instanceof Set}function y(e){return e.o||e.t}function b(e){if(Array.isArray(e))return Array.prototype.slice.call(e);var t=K(e);delete t[W];for(var n=J(t),r=0;r<n.length;r++){var o=n[r],a=t[o];!1===a.writable&&(a.writable=!0,a.configurable=!0),(a.get||a.set)&&(t[o]={configurable:!0,writable:!0,enumerable:a.enumerable,value:e[o]})}return Object.create(Object.getPrototypeOf(e),t)}function g(e,t){return void 0===t&&(t=!1),w(e)||u(e)||!c(e)||(l(e)>1&&(e.set=e.add=e.clear=e.delete=O),Object.freeze(e),t&&s(e,(function(e,t){return g(t,!0)}),!0)),e}function O(){i(2)}function w(e){return null==e||"object"!=typeof e||Object.isFrozen(e)}function S(e){var t=X[e];return t||i(18,e),t}function E(){return U}function P(e,t){t&&(S("Patches"),e.u=[],e.s=[],e.v=t)}function x(e){R(e),e.p.forEach(A),e.p=null}function R(e){e===U&&(U=e.l)}function C(e){return U={p:[],l:U,h:e,m:!0,_:0}}function A(e){var t=e[W];0===t.i||1===t.i?t.j():t.O=!0}function I(e,t){t._=t.p.length;var n=t.p[0],r=void 0!==e&&e!==n;return t.h.g||S("ES5").S(t,e,r),r?(n[W].P&&(x(t),i(4)),c(e)&&(e=j(t,e),t.l||M(t,e)),t.u&&S("Patches").M(n[W].t,e,t.u,t.s)):e=j(t,n,[]),x(t),t.u&&t.v(t.u,t.s),e!==G?e:void 0}function j(e,t,n){if(w(t))return t;var r=t[W];if(!r)return s(t,(function(o,a){return k(e,r,t,o,a,n)}),!0),t;if(r.A!==e)return t;if(!r.P)return M(e,r.t,!0),r.t;if(!r.I){r.I=!0,r.A._--;var o=4===r.i||5===r.i?r.o=b(r.k):r.o;s(3===r.i?new Set(o):o,(function(t,a){return k(e,r,o,t,a,n)})),M(e,o,!1),n&&e.u&&S("Patches").R(r,n,e.u,e.s)}return r.o}function k(e,t,n,r,o,a){if(u(o)){var i=j(e,o,a&&t&&3!==t.i&&!f(t.D,r)?a.concat(r):void 0);if(d(n,r,i),!u(i))return;e.m=!1}if(c(o)&&!w(o)){if(!e.h.F&&e._<1)return;j(e,o),t&&t.A.l||M(e,o)}}function M(e,t,n){void 0===n&&(n=!1),e.h.F&&e.m&&g(t,n)}function D(e,t){var n=e[W];return(n?y(n):e)[t]}function T(e,t){if(t in e)for(var n=Object.getPrototypeOf(e);n;){var r=Object.getOwnPropertyDescriptor(n,t);if(r)return r;n=Object.getPrototypeOf(n)}}function Z(e){e.P||(e.P=!0,e.l&&Z(e.l))}function N(e){e.o||(e.o=b(e.t))}function L(e,t,n){var r=h(t)?S("MapSet").N(t,n):v(t)?S("MapSet").T(t,n):e.g?function(e,t){var n=Array.isArray(e),r={i:n?1:0,A:t?t.A:E(),P:!1,I:!1,D:{},l:t,t:e,k:null,o:null,j:null,C:!1},o=r,a=Q;n&&(o=[r],a=ee);var i=Proxy.revocable(o,a),u=i.revoke,c=i.proxy;return r.k=c,r.j=u,c}(t,n):S("ES5").J(t,n);return(n?n.A:E()).p.push(r),r}function V(e){return u(e)||i(22,e),function e(t){if(!c(t))return t;var n,r=t[W],o=l(t);if(r){if(!r.P&&(r.i<4||!S("ES5").K(r)))return r.t;r.I=!0,n=_(t,o),r.I=!1}else n=_(t,o);return s(n,(function(t,o){r&&p(r.t,t)===o||d(n,t,e(o))})),3===o?new Set(n):n}(e)}function _(e,t){switch(t){case 2:return new Map(e);case 3:return Array.from(e)}return b(e)}var F,U,H="undefined"!=typeof Symbol&&"symbol"==typeof Symbol("x"),$="undefined"!=typeof Map,z="undefined"!=typeof Set,B="undefined"!=typeof Proxy&&void 0!==Proxy.revocable&&"undefined"!=typeof Reflect,G=H?Symbol.for("immer-nothing"):((F={})["immer-nothing"]=!0,F),Y=H?Symbol.for("immer-draftable"):"__$immer_draftable",W=H?Symbol.for("immer-state"):"__$immer_state",q=("undefined"!=typeof Symbol&&Symbol.iterator,""+Object.prototype.constructor),J="undefined"!=typeof Reflect&&Reflect.ownKeys?Reflect.ownKeys:void 0!==Object.getOwnPropertySymbols?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:Object.getOwnPropertyNames,K=Object.getOwnPropertyDescriptors||function(e){var t={};return J(e).forEach((function(n){t[n]=Object.getOwnPropertyDescriptor(e,n)})),t},X={},Q={get:function(e,t){if(t===W)return e;var n=y(e);if(!f(n,t))return function(e,t,n){var r,o=T(t,n);return o?"value"in o?o.value:null===(r=o.get)||void 0===r?void 0:r.call(e.k):void 0}(e,n,t);var r=n[t];return e.I||!c(r)?r:r===D(e.t,t)?(N(e),e.o[t]=L(e.A.h,r,e)):r},has:function(e,t){return t in y(e)},ownKeys:function(e){return Reflect.ownKeys(y(e))},set:function(e,t,n){var r=T(y(e),t);if(null==r?void 0:r.set)return r.set.call(e.k,n),!0;if(!e.P){var o=D(y(e),t),a=null==o?void 0:o[W];if(a&&a.t===n)return e.o[t]=n,e.D[t]=!1,!0;if(m(n,o)&&(void 0!==n||f(e.t,t)))return!0;N(e),Z(e)}return e.o[t]===n&&"number"!=typeof n&&(void 0!==n||t in e.o)||(e.o[t]=n,e.D[t]=!0,!0)},deleteProperty:function(e,t){return void 0!==D(e.t,t)||t in e.t?(e.D[t]=!1,N(e),Z(e)):delete e.D[t],e.o&&delete e.o[t],!0},getOwnPropertyDescriptor:function(e,t){var n=y(e),r=Reflect.getOwnPropertyDescriptor(n,t);return r?{writable:!0,configurable:1!==e.i||"length"!==t,enumerable:r.enumerable,value:n[t]}:r},defineProperty:function(){i(11)},getPrototypeOf:function(e){return Object.getPrototypeOf(e.t)},setPrototypeOf:function(){i(12)}},ee={};s(Q,(function(e,t){ee[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}})),ee.deleteProperty=function(e,t){return ee.set.call(this,e,t,void 0)},ee.set=function(e,t,n){return Q.set.call(this,e[0],t,n,e[0])};var te=function(){function e(e){var t=this;this.g=B,this.F=!0,this.produce=function(e,n,r){if("function"==typeof e&&"function"!=typeof n){var o=n;n=e;var a=t;return function(e){var t=this;void 0===e&&(e=o);for(var r=arguments.length,i=Array(r>1?r-1:0),u=1;u<r;u++)i[u-1]=arguments[u];return a.produce(e,(function(e){var r;return(r=n).call.apply(r,[t,e].concat(i))}))}}var u;if("function"!=typeof n&&i(6),void 0!==r&&"function"!=typeof r&&i(7),c(e)){var s=C(t),l=L(t,e,void 0),f=!0;try{u=n(l),f=!1}finally{f?x(s):R(s)}return"undefined"!=typeof Promise&&u instanceof Promise?u.then((function(e){return P(s,r),I(e,s)}),(function(e){throw x(s),e})):(P(s,r),I(u,s))}if(!e||"object"!=typeof e){if(void 0===(u=n(e))&&(u=e),u===G&&(u=void 0),t.F&&g(u,!0),r){var p=[],d=[];S("Patches").M(e,u,p,d),r(p,d)}return u}i(21,e)},this.produceWithPatches=function(e,n){if("function"==typeof e)return function(n){for(var r=arguments.length,o=Array(r>1?r-1:0),a=1;a<r;a++)o[a-1]=arguments[a];return t.produceWithPatches(n,(function(t){return e.apply(void 0,[t].concat(o))}))};var r,o,a=t.produce(e,n,(function(e,t){r=e,o=t}));return"undefined"!=typeof Promise&&a instanceof Promise?a.then((function(e){return[e,r,o]})):[a,r,o]},"boolean"==typeof(null==e?void 0:e.useProxies)&&this.setUseProxies(e.useProxies),"boolean"==typeof(null==e?void 0:e.autoFreeze)&&this.setAutoFreeze(e.autoFreeze)}var t=e.prototype;return t.createDraft=function(e){c(e)||i(8),u(e)&&(e=V(e));var t=C(this),n=L(this,e,void 0);return n[W].C=!0,R(t),n},t.finishDraft=function(e,t){var n=(e&&e[W]).A;return P(n,t),I(void 0,n)},t.setAutoFreeze=function(e){this.F=e},t.setUseProxies=function(e){e&&!B&&i(20),this.g=e},t.applyPatches=function(e,t){var n;for(n=t.length-1;n>=0;n--){var r=t[n];if(0===r.path.length&&"replace"===r.op){e=r.value;break}}n>-1&&(t=t.slice(n+1));var o=S("Patches").$;return u(e)?o(e,t):this.produce(e,(function(e){return o(e,t)}))},e}(),ne=new te,re=ne.produce;ne.produceWithPatches.bind(ne),ne.setAutoFreeze.bind(ne),ne.setUseProxies.bind(ne),ne.applyPatches.bind(ne),ne.createDraft.bind(ne),ne.finishDraft.bind(ne);const oe=re;var ae=n(7363);var ie=n(9518);function ue(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==n)return;var r,o,a=[],i=!0,u=!1;try{for(n=n.call(e);!(i=(r=n.next()).done)&&(a.push(r.value),!t||a.length!==t);i=!0);}catch(e){u=!0,o=e}finally{try{i||null==n.return||n.return()}finally{if(u)throw o}}return a}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return ce(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return ce(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ce(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var se=(0,r.createContext)({}),le=function(e){var t=e.children,n=function(e){var t=(0,ae.useState)((function(){return g("function"==typeof e?e():e,!0)})),n=t[1];return[t[0],(0,ae.useCallback)((function(e){n("function"==typeof e?oe(e):g(e))}),[])]}({}),o=ue(n,2),i=o[0],u=o[1];(0,r.useEffect)((function(){(0,ie.WY)("schemas").then((function(e){e&&u((function(t){Object.entries(e).forEach((function(e){var n=ue(e,2),r=n[0],o=n[1];t[r]=a().get(o,"fields._label",o.type)}))}))}))}),[]);return React.createElement(se.Provider,{value:{schemaLinks:i,addSchemaLink:function(e,t){return u((function(n){n[e]=a().get(t,"fields._label",t.type)}))},removeSchemaLink:function(e){return u((function(t){delete t[e]}))},updateSchemaLinkLabel:function(e,t){return u((function(n){n[e]=t}))}}},t)}},9518:(e,t,n)=>{"use strict";n.d(t,{Ql:()=>f,Uz:()=>u,WY:()=>s});var r=n(2610);function o(e){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}function a(){a=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,r="function"==typeof Symbol?Symbol:{},i=r.iterator||"@@iterator",u=r.asyncIterator||"@@asyncIterator",c=r.toStringTag||"@@toStringTag";function s(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{s({},"")}catch(e){s=function(e,t,n){return e[t]=n}}function l(e,t,n,r){var o=t&&t.prototype instanceof d?t:d,a=Object.create(o.prototype),i=new x(r||[]);return a._invoke=function(e,t,n){var r="suspendedStart";return function(o,a){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===o)throw a;return C()}for(n.method=o,n.arg=a;;){var i=n.delegate;if(i){var u=S(i,n);if(u){if(u===p)continue;return u}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var c=f(e,t,n);if("normal"===c.type){if(r=n.done?"completed":"suspendedYield",c.arg===p)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(r="completed",n.method="throw",n.arg=c.arg)}}}(e,n,i),a}function f(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=l;var p={};function d(){}function m(){}function h(){}var v={};s(v,i,(function(){return this}));var y=Object.getPrototypeOf,b=y&&y(y(R([])));b&&b!==t&&n.call(b,i)&&(v=b);var g=h.prototype=d.prototype=Object.create(v);function O(e){["next","throw","return"].forEach((function(t){s(e,t,(function(e){return this._invoke(t,e)}))}))}function w(e,t){function r(a,i,u,c){var s=f(e[a],e,i);if("throw"!==s.type){var l=s.arg,p=l.value;return p&&"object"==o(p)&&n.call(p,"__await")?t.resolve(p.__await).then((function(e){r("next",e,u,c)}),(function(e){r("throw",e,u,c)})):t.resolve(p).then((function(e){l.value=e,u(l)}),(function(e){return r("throw",e,u,c)}))}c(s.arg)}var a;this._invoke=function(e,n){function o(){return new t((function(t,o){r(e,n,t,o)}))}return a=a?a.then(o,o):o()}}function S(e,t){var n=e.iterator[t.method];if(void 0===n){if(t.delegate=null,"throw"===t.method){if(e.iterator.return&&(t.method="return",t.arg=void 0,S(e,t),"throw"===t.method))return p;t.method="throw",t.arg=new TypeError("The iterator does not provide a 'throw' method")}return p}var r=f(n,e.iterator,t.arg);if("throw"===r.type)return t.method="throw",t.arg=r.arg,t.delegate=null,p;var o=r.arg;return o?o.done?(t[e.resultName]=o.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,p):o:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,p)}function E(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function P(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function x(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(E,this),this.reset(!0)}function R(e){if(e){var t=e[i];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,o=function t(){for(;++r<e.length;)if(n.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=void 0,t.done=!0,t};return o.next=o}}return{next:C}}function C(){return{value:void 0,done:!0}}return m.prototype=h,s(g,"constructor",h),s(h,"constructor",m),m.displayName=s(h,c,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===m||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,h):(e.__proto__=h,s(e,c,"GeneratorFunction")),e.prototype=Object.create(g),e},e.awrap=function(e){return{__await:e}},O(w.prototype),s(w.prototype,u,(function(){return this})),e.AsyncIterator=w,e.async=function(t,n,r,o,a){void 0===a&&(a=Promise);var i=new w(l(t,n,r,o),a);return e.isGeneratorFunction(n)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},O(g),s(g,c,"Generator"),s(g,i,(function(){return this})),s(g,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=[];for(var n in e)t.push(n);return t.reverse(),function n(){for(;t.length;){var r=t.pop();if(r in e)return n.value=r,n.done=!1,n}return n.done=!0,n}},e.values=R,x.prototype={constructor:x,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(P),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(n,r){return i.type="throw",i.arg=e,t.next=n,r&&(t.method="next",t.arg=void 0),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],i=a.completion;if("root"===a.tryLoc)return r("end");if(a.tryLoc<=this.prev){var u=n.call(a,"catchLoc"),c=n.call(a,"finallyLoc");if(u&&c){if(this.prev<a.catchLoc)return r(a.catchLoc,!0);if(this.prev<a.finallyLoc)return r(a.finallyLoc)}else if(u){if(this.prev<a.catchLoc)return r(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return r(a.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=e,i.arg=t,a?(this.method="next",this.next=a.finallyLoc,p):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),p},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),P(n),p}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;P(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:R(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),p}},e}function i(e,t,n,r,o,a,i){try{var u=e[a](i),c=u.value}catch(e){return void n(e)}u.done?t(c):Promise.resolve(c).then(r,o)}var u=function(){return Math.random().toString(36).substr(2)},c={},s=function(){var e,t=(e=a().mark((function e(t){var n,r,o,i,u,s,l,f,p=arguments;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=p.length>1&&void 0!==p[1]?p[1]:{},r=p.length>2&&void 0!==p[2]?p[2]:"GET",o=!(p.length>3&&void 0!==p[3])||p[3],i=JSON.stringify({apiName:t,params:n,method:r}),!o||!c[i]){e.next=6;break}return e.abrupt("return",c[i]);case 6:return u={method:r,headers:{"X-WP-Nonce":SSSchema.nonce,"Content-Type":"application/json"}},s="".concat(SSSchema.rest,"/slim-seo-schema/").concat(t),l=new URLSearchParams(n).toString(),"POST"===r?u.body=JSON.stringify(n):l&&(s+=SSSchema.rest.includes("?")?"&".concat(l):"?".concat(l)),e.next=12,fetch(s,u).then((function(e){return e.json()}));case 12:return f=e.sent,c[i]=f,e.abrupt("return",f);case 15:case"end":return e.stop()}}),e)})),function(){var t=this,n=arguments;return new Promise((function(r,o){var a=e.apply(t,n);function u(e){i(a,r,o,u,c,"next",e)}function c(e){i(a,r,o,u,c,"throw",e)}u(void 0)}))});return function(e){return t.apply(this,arguments)}}(),l={},f=function(e){return(0,r.lazy)((function(){return l[e]||(l[e]=n(967)("./".concat(e))),l[e]}))}},6010:(e,t,n)=>{"use strict";function r(e){var t,n,o="";if("string"==typeof e||"number"==typeof e)o+=e;else if("object"==typeof e)if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(n=r(e[t]))&&(o&&(o+=" "),o+=n);else for(t in e)e[t]&&(o&&(o+=" "),o+=t);return o}n.d(t,{Z:()=>o});const o=function(){for(var e,t,n=0,o="";n<arguments.length;)(e=arguments[n++])&&(t=r(e))&&(o&&(o+=" "),o+=t);return o}},3517:(e,t,n)=>{"use strict";const r=n(4290),o=new Set(["__proto__","prototype","constructor"]);function a(e){const t=e.split("."),n=[];for(let e=0;e<t.length;e++){let r=t[e];for(;"\\"===r[r.length-1]&&void 0!==t[e+1];)r=r.slice(0,-1)+".",r+=t[++e];n.push(r)}return n.some((e=>o.has(e)))?[]:n}e.exports={get(e,t,n){if(!r(e)||"string"!=typeof t)return void 0===n?e:n;const o=a(t);if(0!==o.length){for(let t=0;t<o.length;t++)if(null==(e=e[o[t]])){if(t!==o.length-1)return n;break}return void 0===e?n:e}},set(e,t,n){if(!r(e)||"string"!=typeof t)return e;const o=e,i=a(t);for(let t=0;t<i.length;t++){const o=i[t];r(e[o])||(e[o]={}),t===i.length-1&&(e[o]=n),e=e[o]}return o},delete(e,t){if(!r(e)||"string"!=typeof t)return!1;const n=a(t);for(let t=0;t<n.length;t++){const o=n[t];if(t===n.length-1)return delete e[o],!0;if(e=e[o],!r(e))return!1}},has(e,t){if(!r(e)||"string"!=typeof t)return!1;const n=a(t);if(0===n.length)return!1;for(let t=0;t<n.length;t++){if(!r(e))return!1;if(!(n[t]in e))return!1;e=e[n[t]]}return!0}}},8679:(e,t,n)=>{"use strict";var r=n(1296),o={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},a={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},i={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},u={};function c(e){return r.isMemo(e)?i:u[e.$$typeof]||o}u[r.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},u[r.Memo]=i;var s=Object.defineProperty,l=Object.getOwnPropertyNames,f=Object.getOwnPropertySymbols,p=Object.getOwnPropertyDescriptor,d=Object.getPrototypeOf,m=Object.prototype;e.exports=function e(t,n,r){if("string"!=typeof n){if(m){var o=d(n);o&&o!==m&&e(t,o,r)}var i=l(n);f&&(i=i.concat(f(n)));for(var u=c(t),h=c(n),v=0;v<i.length;++v){var y=i[v];if(!(a[y]||r&&r[y]||h&&h[y]||u&&u[y])){var b=p(n,y);try{s(t,y,b)}catch(e){}}}}return t}},6103:(e,t)=>{"use strict";var n="function"==typeof Symbol&&Symbol.for,r=n?Symbol.for("react.element"):60103,o=n?Symbol.for("react.portal"):60106,a=n?Symbol.for("react.fragment"):60107,i=n?Symbol.for("react.strict_mode"):60108,u=n?Symbol.for("react.profiler"):60114,c=n?Symbol.for("react.provider"):60109,s=n?Symbol.for("react.context"):60110,l=n?Symbol.for("react.async_mode"):60111,f=n?Symbol.for("react.concurrent_mode"):60111,p=n?Symbol.for("react.forward_ref"):60112,d=n?Symbol.for("react.suspense"):60113,m=n?Symbol.for("react.suspense_list"):60120,h=n?Symbol.for("react.memo"):60115,v=n?Symbol.for("react.lazy"):60116,y=n?Symbol.for("react.block"):60121,b=n?Symbol.for("react.fundamental"):60117,g=n?Symbol.for("react.responder"):60118,O=n?Symbol.for("react.scope"):60119;function w(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case r:switch(e=e.type){case l:case f:case a:case u:case i:case d:return e;default:switch(e=e&&e.$$typeof){case s:case p:case v:case h:case c:return e;default:return t}}case o:return t}}}function S(e){return w(e)===f}t.AsyncMode=l,t.ConcurrentMode=f,t.ContextConsumer=s,t.ContextProvider=c,t.Element=r,t.ForwardRef=p,t.Fragment=a,t.Lazy=v,t.Memo=h,t.Portal=o,t.Profiler=u,t.StrictMode=i,t.Suspense=d,t.isAsyncMode=function(e){return S(e)||w(e)===l},t.isConcurrentMode=S,t.isContextConsumer=function(e){return w(e)===s},t.isContextProvider=function(e){return w(e)===c},t.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===r},t.isForwardRef=function(e){return w(e)===p},t.isFragment=function(e){return w(e)===a},t.isLazy=function(e){return w(e)===v},t.isMemo=function(e){return w(e)===h},t.isPortal=function(e){return w(e)===o},t.isProfiler=function(e){return w(e)===u},t.isStrictMode=function(e){return w(e)===i},t.isSuspense=function(e){return w(e)===d},t.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===a||e===f||e===u||e===i||e===d||e===m||"object"==typeof e&&null!==e&&(e.$$typeof===v||e.$$typeof===h||e.$$typeof===c||e.$$typeof===s||e.$$typeof===p||e.$$typeof===b||e.$$typeof===g||e.$$typeof===O||e.$$typeof===y)},t.typeOf=w},1296:(e,t,n)=>{"use strict";e.exports=n(6103)},4290:e=>{"use strict";e.exports=e=>{const t=typeof e;return null!==e&&("object"===t||"function"===t)}},845:(e,t,n)=>{"use strict";n.d(t,{Z:()=>a});var r=Number.isNaN||function(e){return"number"==typeof e&&e!=e};function o(e,t){if(e.length!==t.length)return!1;for(var n=0;n<e.length;n++)if(o=e[n],a=t[n],!(o===a||r(o)&&r(a)))return!1;var o,a;return!0}const a=function(e,t){var n;void 0===t&&(t=o);var r,a=[],i=!1;return function(){for(var o=[],u=0;u<arguments.length;u++)o[u]=arguments[u];return i&&n===this&&t(o,a)||(r=e.apply(this,o),i=!0,n=this,a=o),r}}},3520:(e,t,n)=>{"use strict";n.d(t,{S:()=>ae});var r=n(7462),o=n(9344),a=n(5671),i=n(3144),u=n(9340),c=n(907);var s=n(181);function l(e){return function(e){if(Array.isArray(e))return(0,c.Z)(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||(0,s.Z)(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var f=n(7363),p=n(917),d=n(845),m=n(4925);for(var h={name:"7pg0cj-a11yText",styles:"label:a11yText;z-index:9999;border:0;clip:rect(1px, 1px, 1px, 1px);height:1px;width:1px;position:absolute;overflow:hidden;padding:0;white-space:nowrap"},v=function(e){return(0,p.tZ)("span",(0,r.Z)({css:h},e))},y={guidance:function(e){var t=e.isSearchable,n=e.isMulti,r=e.isDisabled,o=e.tabSelectsValue;switch(e.context){case"menu":return"Use Up and Down to choose options".concat(r?"":", press Enter to select the currently focused option",", press Escape to exit the menu").concat(o?", press Tab to select the option and exit the menu":"",".");case"input":return"".concat(e["aria-label"]||"Select"," is focused ").concat(t?",type to refine list":"",", press Down to open the menu, ").concat(n?" press left to focus selected values":"");case"value":return"Use left and right to toggle between focused values, press Backspace to remove the currently focused value";default:return""}},onChange:function(e){var t=e.action,n=e.label,r=void 0===n?"":n,o=e.labels,a=e.isDisabled;switch(t){case"deselect-option":case"pop-value":case"remove-value":return"option ".concat(r,", deselected.");case"clear":return"All selected options have been cleared.";case"initial-input-focus":return"option".concat(o.length>1?"s":""," ").concat(o.join(","),", selected.");case"select-option":return"option ".concat(r,a?" is disabled. Select another option.":", selected.");default:return""}},onFocus:function(e){var t=e.context,n=e.focused,r=e.options,o=e.label,a=void 0===o?"":o,i=e.selectValue,u=e.isDisabled,c=e.isSelected,s=function(e,t){return e&&e.length?"".concat(e.indexOf(t)+1," of ").concat(e.length):""};if("value"===t&&i)return"value ".concat(a," focused, ").concat(s(i,n),".");if("menu"===t){var l=u?" disabled":"",f="".concat(c?"selected":"focused").concat(l);return"option ".concat(a," ").concat(f,", ").concat(s(r,n),".")}return""},onFilter:function(e){var t=e.inputValue,n=e.resultsMessage;return"".concat(n).concat(t?" for search term "+t:"",".")}},b=function(e){var t=e.ariaSelection,n=e.focusedOption,r=e.focusedValue,a=e.focusableOptions,i=e.isFocused,u=e.selectValue,c=e.selectProps,s=e.id,l=c.ariaLiveMessages,d=c.getOptionLabel,m=c.inputValue,h=c.isMulti,b=c.isOptionDisabled,g=c.isSearchable,O=c.menuIsOpen,w=c.options,S=c.screenReaderStatus,E=c.tabSelectsValue,P=c["aria-label"],x=c["aria-live"],R=(0,f.useMemo)((function(){return(0,o.a)((0,o.a)({},y),l||{})}),[l]),C=(0,f.useMemo)((function(){var e,n="";if(t&&R.onChange){var r=t.option,a=t.options,i=t.removedValue,c=t.removedValues,s=t.value,l=i||r||(e=s,Array.isArray(e)?null:e),f=l?d(l):"",p=a||c||void 0,m=p?p.map(d):[],h=(0,o.a)({isDisabled:l&&b(l,u),label:f,labels:m},t);n=R.onChange(h)}return n}),[t,R,b,u,d]),A=(0,f.useMemo)((function(){var e="",t=n||r,o=!!(n&&u&&u.includes(n));if(t&&R.onFocus){var a={focused:t,label:d(t),isDisabled:b(t,u),isSelected:o,options:w,context:t===n?"menu":"value",selectValue:u};e=R.onFocus(a)}return e}),[n,r,d,b,R,w,u]),I=(0,f.useMemo)((function(){var e="";if(O&&w.length&&R.onFilter){var t=S({count:a.length});e=R.onFilter({inputValue:m,resultsMessage:t})}return e}),[a,m,O,R,w,S]),j=(0,f.useMemo)((function(){var e="";if(R.guidance){var t=r?"value":O?"menu":"input";e=R.guidance({"aria-label":P,context:t,isDisabled:n&&b(n,u),isMulti:h,isSearchable:g,tabSelectsValue:E})}return e}),[P,n,r,h,b,g,O,R,u,E]),k="".concat(A," ").concat(I," ").concat(j),M=(0,p.tZ)(f.Fragment,null,(0,p.tZ)("span",{id:"aria-selection"},C),(0,p.tZ)("span",{id:"aria-context"},k)),D="initial-input-focus"===(null==t?void 0:t.action);return(0,p.tZ)(f.Fragment,null,(0,p.tZ)(v,{id:s},D&&M),(0,p.tZ)(v,{"aria-live":x,"aria-atomic":"false","aria-relevant":"additions text"},i&&!D&&M))},g=[{base:"A",letters:"AⒶＡÀÁÂẦẤẪẨÃĀĂẰẮẴẲȦǠÄǞẢÅǺǍȀȂẠẬẶḀĄȺⱯ"},{base:"AA",letters:"Ꜳ"},{base:"AE",letters:"ÆǼǢ"},{base:"AO",letters:"Ꜵ"},{base:"AU",letters:"Ꜷ"},{base:"AV",letters:"ꜸꜺ"},{base:"AY",letters:"Ꜽ"},{base:"B",letters:"BⒷＢḂḄḆɃƂƁ"},{base:"C",letters:"CⒸＣĆĈĊČÇḈƇȻꜾ"},{base:"D",letters:"DⒹＤḊĎḌḐḒḎĐƋƊƉꝹ"},{base:"DZ",letters:"ǱǄ"},{base:"Dz",letters:"ǲǅ"},{base:"E",letters:"EⒺＥÈÉÊỀẾỄỂẼĒḔḖĔĖËẺĚȄȆẸỆȨḜĘḘḚƐƎ"},{base:"F",letters:"FⒻＦḞƑꝻ"},{base:"G",letters:"GⒼＧǴĜḠĞĠǦĢǤƓꞠꝽꝾ"},{base:"H",letters:"HⒽＨĤḢḦȞḤḨḪĦⱧⱵꞍ"},{base:"I",letters:"IⒾＩÌÍÎĨĪĬİÏḮỈǏȈȊỊĮḬƗ"},{base:"J",letters:"JⒿＪĴɈ"},{base:"K",letters:"KⓀＫḰǨḲĶḴƘⱩꝀꝂꝄꞢ"},{base:"L",letters:"LⓁＬĿĹĽḶḸĻḼḺŁȽⱢⱠꝈꝆꞀ"},{base:"LJ",letters:"Ǉ"},{base:"Lj",letters:"ǈ"},{base:"M",letters:"MⓂＭḾṀṂⱮƜ"},{base:"N",letters:"NⓃＮǸŃÑṄŇṆŅṊṈȠƝꞐꞤ"},{base:"NJ",letters:"Ǌ"},{base:"Nj",letters:"ǋ"},{base:"O",letters:"OⓄＯÒÓÔỒỐỖỔÕṌȬṎŌṐṒŎȮȰÖȪỎŐǑȌȎƠỜỚỠỞỢỌỘǪǬØǾƆƟꝊꝌ"},{base:"OI",letters:"Ƣ"},{base:"OO",letters:"Ꝏ"},{base:"OU",letters:"Ȣ"},{base:"P",letters:"PⓅＰṔṖƤⱣꝐꝒꝔ"},{base:"Q",letters:"QⓆＱꝖꝘɊ"},{base:"R",letters:"RⓇＲŔṘŘȐȒṚṜŖṞɌⱤꝚꞦꞂ"},{base:"S",letters:"SⓈＳẞŚṤŜṠŠṦṢṨȘŞⱾꞨꞄ"},{base:"T",letters:"TⓉＴṪŤṬȚŢṰṮŦƬƮȾꞆ"},{base:"TZ",letters:"Ꜩ"},{base:"U",letters:"UⓊＵÙÚÛŨṸŪṺŬÜǛǗǕǙỦŮŰǓȔȖƯỪỨỮỬỰỤṲŲṶṴɄ"},{base:"V",letters:"VⓋＶṼṾƲꝞɅ"},{base:"VY",letters:"Ꝡ"},{base:"W",letters:"WⓌＷẀẂŴẆẄẈⱲ"},{base:"X",letters:"XⓍＸẊẌ"},{base:"Y",letters:"YⓎＹỲÝŶỸȲẎŸỶỴƳɎỾ"},{base:"Z",letters:"ZⓏＺŹẐŻŽẒẔƵȤⱿⱫꝢ"},{base:"a",letters:"aⓐａẚàáâầấẫẩãāăằắẵẳȧǡäǟảåǻǎȁȃạậặḁąⱥɐ"},{base:"aa",letters:"ꜳ"},{base:"ae",letters:"æǽǣ"},{base:"ao",letters:"ꜵ"},{base:"au",letters:"ꜷ"},{base:"av",letters:"ꜹꜻ"},{base:"ay",letters:"ꜽ"},{base:"b",letters:"bⓑｂḃḅḇƀƃɓ"},{base:"c",letters:"cⓒｃćĉċčçḉƈȼꜿↄ"},{base:"d",letters:"dⓓｄḋďḍḑḓḏđƌɖɗꝺ"},{base:"dz",letters:"ǳǆ"},{base:"e",letters:"eⓔｅèéêềếễểẽēḕḗĕėëẻěȅȇẹệȩḝęḙḛɇɛǝ"},{base:"f",letters:"fⓕｆḟƒꝼ"},{base:"g",letters:"gⓖｇǵĝḡğġǧģǥɠꞡᵹꝿ"},{base:"h",letters:"hⓗｈĥḣḧȟḥḩḫẖħⱨⱶɥ"},{base:"hv",letters:"ƕ"},{base:"i",letters:"iⓘｉìíîĩīĭïḯỉǐȉȋịįḭɨı"},{base:"j",letters:"jⓙｊĵǰɉ"},{base:"k",letters:"kⓚｋḱǩḳķḵƙⱪꝁꝃꝅꞣ"},{base:"l",letters:"lⓛｌŀĺľḷḹļḽḻſłƚɫⱡꝉꞁꝇ"},{base:"lj",letters:"ǉ"},{base:"m",letters:"mⓜｍḿṁṃɱɯ"},{base:"n",letters:"nⓝｎǹńñṅňṇņṋṉƞɲŉꞑꞥ"},{base:"nj",letters:"ǌ"},{base:"o",letters:"oⓞｏòóôồốỗổõṍȭṏōṑṓŏȯȱöȫỏőǒȍȏơờớỡởợọộǫǭøǿɔꝋꝍɵ"},{base:"oi",letters:"ƣ"},{base:"ou",letters:"ȣ"},{base:"oo",letters:"ꝏ"},{base:"p",letters:"pⓟｐṕṗƥᵽꝑꝓꝕ"},{base:"q",letters:"qⓠｑɋꝗꝙ"},{base:"r",letters:"rⓡｒŕṙřȑȓṛṝŗṟɍɽꝛꞧꞃ"},{base:"s",letters:"sⓢｓßśṥŝṡšṧṣṩșşȿꞩꞅẛ"},{base:"t",letters:"tⓣｔṫẗťṭțţṱṯŧƭʈⱦꞇ"},{base:"tz",letters:"ꜩ"},{base:"u",letters:"uⓤｕùúûũṹūṻŭüǜǘǖǚủůűǔȕȗưừứữửựụṳųṷṵʉ"},{base:"v",letters:"vⓥｖṽṿʋꝟʌ"},{base:"vy",letters:"ꝡ"},{base:"w",letters:"wⓦｗẁẃŵẇẅẘẉⱳ"},{base:"x",letters:"xⓧｘẋẍ"},{base:"y",letters:"yⓨｙỳýŷỹȳẏÿỷẙỵƴɏỿ"},{base:"z",letters:"zⓩｚźẑżžẓẕƶȥɀⱬꝣ"}],O=new RegExp("["+g.map((function(e){return e.letters})).join("")+"]","g"),w={},S=0;S<g.length;S++)for(var E=g[S],P=0;P<E.letters.length;P++)w[E.letters[P]]=E.base;var x=function(e){return e.replace(O,(function(e){return w[e]}))},R=(0,d.Z)(x),C=function(e){return e.replace(/^\s+|\s+$/g,"")},A=function(e){return"".concat(e.label," ").concat(e.value)},I=["innerRef"];function j(e){var t=e.innerRef,n=(0,m.Z)(e,I),a=(0,o.r)(n,"onExited","in","enter","exit","appear");return(0,p.tZ)("input",(0,r.Z)({ref:t},a,{css:(0,p.iv)({label:"dummyInput",background:0,border:0,caretColor:"transparent",fontSize:"inherit",gridArea:"1 / 1 / 2 / 3",outline:0,padding:0,width:1,color:"transparent",left:-100,opacity:0,position:"relative",transform:"scale(.01)"},"","")}))}var k=function(e){e.preventDefault(),e.stopPropagation()};var M=["boxSizing","height","overflow","paddingRight","position"],D={boxSizing:"border-box",overflow:"hidden",position:"relative",height:"100%"};function T(e){e.preventDefault()}function Z(e){e.stopPropagation()}function N(){var e=this.scrollTop,t=this.scrollHeight,n=e+this.offsetHeight;0===e?this.scrollTop=1:n===t&&(this.scrollTop=e-1)}function L(){return"ontouchstart"in window||navigator.maxTouchPoints}var V=!("undefined"==typeof window||!window.document||!window.document.createElement),_=0,F={capture:!1,passive:!1};var U=function(){return document.activeElement&&document.activeElement.blur()},H={name:"1kfdb0e",styles:"position:fixed;left:0;bottom:0;right:0;top:0"};function $(e){var t=e.children,n=e.lockEnabled,r=e.captureEnabled,a=function(e){var t=e.isEnabled,n=e.onBottomArrive,r=e.onBottomLeave,a=e.onTopArrive,i=e.onTopLeave,u=(0,f.useRef)(!1),c=(0,f.useRef)(!1),s=(0,f.useRef)(0),l=(0,f.useRef)(null),p=(0,f.useCallback)((function(e,t){if(null!==l.current){var o=l.current,s=o.scrollTop,f=o.scrollHeight,p=o.clientHeight,d=l.current,m=t>0,h=f-p-s,v=!1;h>t&&u.current&&(r&&r(e),u.current=!1),m&&c.current&&(i&&i(e),c.current=!1),m&&t>h?(n&&!u.current&&n(e),d.scrollTop=f,v=!0,u.current=!0):!m&&-t>s&&(a&&!c.current&&a(e),d.scrollTop=0,v=!0,c.current=!0),v&&k(e)}}),[n,r,a,i]),d=(0,f.useCallback)((function(e){p(e,e.deltaY)}),[p]),m=(0,f.useCallback)((function(e){s.current=e.changedTouches[0].clientY}),[]),h=(0,f.useCallback)((function(e){var t=s.current-e.changedTouches[0].clientY;p(e,t)}),[p]),v=(0,f.useCallback)((function(e){if(e){var t=!!o.s&&{passive:!1};e.addEventListener("wheel",d,t),e.addEventListener("touchstart",m,t),e.addEventListener("touchmove",h,t)}}),[h,m,d]),y=(0,f.useCallback)((function(e){e&&(e.removeEventListener("wheel",d,!1),e.removeEventListener("touchstart",m,!1),e.removeEventListener("touchmove",h,!1))}),[h,m,d]);return(0,f.useEffect)((function(){if(t){var e=l.current;return v(e),function(){y(e)}}}),[t,v,y]),function(e){l.current=e}}({isEnabled:void 0===r||r,onBottomArrive:e.onBottomArrive,onBottomLeave:e.onBottomLeave,onTopArrive:e.onTopArrive,onTopLeave:e.onTopLeave}),i=function(e){var t=e.isEnabled,n=e.accountForScrollbars,r=void 0===n||n,o=(0,f.useRef)({}),a=(0,f.useRef)(null),i=(0,f.useCallback)((function(e){if(V){var t=document.body,n=t&&t.style;if(r&&M.forEach((function(e){var t=n&&n[e];o.current[e]=t})),r&&_<1){var a=parseInt(o.current.paddingRight,10)||0,i=document.body?document.body.clientWidth:0,u=window.innerWidth-i+a||0;Object.keys(D).forEach((function(e){var t=D[e];n&&(n[e]=t)})),n&&(n.paddingRight="".concat(u,"px"))}t&&L()&&(t.addEventListener("touchmove",T,F),e&&(e.addEventListener("touchstart",N,F),e.addEventListener("touchmove",Z,F))),_+=1}}),[r]),u=(0,f.useCallback)((function(e){if(V){var t=document.body,n=t&&t.style;_=Math.max(_-1,0),r&&_<1&&M.forEach((function(e){var t=o.current[e];n&&(n[e]=t)})),t&&L()&&(t.removeEventListener("touchmove",T,F),e&&(e.removeEventListener("touchstart",N,F),e.removeEventListener("touchmove",Z,F)))}}),[r]);return(0,f.useEffect)((function(){if(t){var e=a.current;return i(e),function(){u(e)}}}),[t,i,u]),function(e){a.current=e}}({isEnabled:n});return(0,p.tZ)(f.Fragment,null,n&&(0,p.tZ)("div",{onClick:U,css:H}),t((function(e){a(e),i(e)})))}var z={clearIndicator:o.b,container:o.d,control:o.e,dropdownIndicator:o.f,group:o.g,groupHeading:o.h,indicatorsContainer:o.i,indicatorSeparator:o.j,input:o.k,loadingIndicator:o.l,loadingMessage:o.m,menu:o.n,menuList:o.o,menuPortal:o.p,multiValue:o.q,multiValueLabel:o.t,multiValueRemove:o.u,noOptionsMessage:o.v,option:o.w,placeholder:o.x,singleValue:o.y,valueContainer:o.z};var B,G={borderRadius:4,colors:{primary:"#2684FF",primary75:"#4C9AFF",primary50:"#B2D4FF",primary25:"#DEEBFF",danger:"#DE350B",dangerLight:"#FFBDAD",neutral0:"hsl(0, 0%, 100%)",neutral5:"hsl(0, 0%, 95%)",neutral10:"hsl(0, 0%, 90%)",neutral20:"hsl(0, 0%, 80%)",neutral30:"hsl(0, 0%, 70%)",neutral40:"hsl(0, 0%, 60%)",neutral50:"hsl(0, 0%, 50%)",neutral60:"hsl(0, 0%, 40%)",neutral70:"hsl(0, 0%, 30%)",neutral80:"hsl(0, 0%, 20%)",neutral90:"hsl(0, 0%, 10%)"},spacing:{baseUnit:4,controlHeight:38,menuGutter:8}},Y={"aria-live":"polite",backspaceRemovesValue:!0,blurInputOnSelect:(0,o.A)(),captureMenuScroll:!(0,o.A)(),closeMenuOnSelect:!0,closeMenuOnScroll:!1,components:{},controlShouldRenderValue:!0,escapeClearsValue:!1,filterOption:function(e,t){if(e.data.__isNew__)return!0;var n=(0,o.a)({ignoreCase:!0,ignoreAccents:!0,stringify:A,trim:!0,matchFrom:"any"},B),r=n.ignoreCase,a=n.ignoreAccents,i=n.stringify,u=n.trim,c=n.matchFrom,s=u?C(t):t,l=u?C(i(e)):i(e);return r&&(s=s.toLowerCase(),l=l.toLowerCase()),a&&(s=R(s),l=x(l)),"start"===c?l.substr(0,s.length)===s:l.indexOf(s)>-1},formatGroupLabel:function(e){return e.label},getOptionLabel:function(e){return e.label},getOptionValue:function(e){return e.value},isDisabled:!1,isLoading:!1,isMulti:!1,isRtl:!1,isSearchable:!0,isOptionDisabled:function(e){return!!e.isDisabled},loadingMessage:function(){return"Loading..."},maxMenuHeight:300,minMenuHeight:140,menuIsOpen:!1,menuPlacement:"bottom",menuPosition:"absolute",menuShouldBlockScroll:!1,menuShouldScrollIntoView:!(0,o.B)(),noOptionsMessage:function(){return"No options"},openMenuOnFocus:!1,openMenuOnClick:!0,options:[],pageSize:5,placeholder:"Select...",screenReaderStatus:function(e){var t=e.count;return"".concat(t," result").concat(1!==t?"s":""," available")},styles:{},tabIndex:0,tabSelectsValue:!0};function W(e,t,n,r){return{type:"option",data:t,isDisabled:ee(e,t,n),isSelected:te(e,t,n),label:X(e,t),value:Q(e,t),index:r}}function q(e,t){return e.options.map((function(n,r){if("options"in n){var o=n.options.map((function(n,r){return W(e,n,t,r)})).filter((function(t){return K(e,t)}));return o.length>0?{type:"group",data:n,options:o,index:r}:void 0}var a=W(e,n,t,r);return K(e,a)?a:void 0})).filter(o.H)}function J(e){return e.reduce((function(e,t){return"group"===t.type?e.push.apply(e,l(t.options.map((function(e){return e.data})))):e.push(t.data),e}),[])}function K(e,t){var n=e.inputValue,r=void 0===n?"":n,o=t.data,a=t.isSelected,i=t.label,u=t.value;return(!re(e)||!a)&&ne(e,{label:i,value:u,data:o},r)}var X=function(e,t){return e.getOptionLabel(t)},Q=function(e,t){return e.getOptionValue(t)};function ee(e,t,n){return"function"==typeof e.isOptionDisabled&&e.isOptionDisabled(t,n)}function te(e,t,n){if(n.indexOf(t)>-1)return!0;if("function"==typeof e.isOptionSelected)return e.isOptionSelected(t,n);var r=Q(e,t);return n.some((function(t){return Q(e,t)===r}))}function ne(e,t,n){return!e.filterOption||e.filterOption(t,n)}var re=function(e){var t=e.hideSelectedOptions,n=e.isMulti;return void 0===t?n:t},oe=1,ae=function(e){(0,u.Z)(n,e);var t=(0,o._)(n);function n(e){var r;return(0,a.Z)(this,n),(r=t.call(this,e)).state={ariaSelection:null,focusedOption:null,focusedValue:null,inputIsHidden:!1,isFocused:!1,selectValue:[],clearFocusValueOnUpdate:!1,prevWasFocused:!1,inputIsHiddenAfterUpdate:void 0,prevProps:void 0},r.blockOptionHover=!1,r.isComposing=!1,r.commonProps=void 0,r.initialTouchX=0,r.initialTouchY=0,r.instancePrefix="",r.openAfterFocus=!1,r.scrollToFocusedOptionOnUpdate=!1,r.userIsDragging=void 0,r.controlRef=null,r.getControlRef=function(e){r.controlRef=e},r.focusedOptionRef=null,r.getFocusedOptionRef=function(e){r.focusedOptionRef=e},r.menuListRef=null,r.getMenuListRef=function(e){r.menuListRef=e},r.inputRef=null,r.getInputRef=function(e){r.inputRef=e},r.focus=r.focusInput,r.blur=r.blurInput,r.onChange=function(e,t){var n=r.props,o=n.onChange,a=n.name;t.name=a,r.ariaOnChange(e,t),o(e,t)},r.setValue=function(e,t,n){var o=r.props,a=o.closeMenuOnSelect,i=o.isMulti,u=o.inputValue;r.onInputChange("",{action:"set-value",prevInputValue:u}),a&&(r.setState({inputIsHiddenAfterUpdate:!i}),r.onMenuClose()),r.setState({clearFocusValueOnUpdate:!0}),r.onChange(e,{action:t,option:n})},r.selectOption=function(e){var t=r.props,n=t.blurInputOnSelect,a=t.isMulti,i=t.name,u=r.state.selectValue,c=a&&r.isOptionSelected(e,u),s=r.isOptionDisabled(e,u);if(c){var f=r.getOptionValue(e);r.setValue((0,o.C)(u.filter((function(e){return r.getOptionValue(e)!==f}))),"deselect-option",e)}else{if(s)return void r.ariaOnChange((0,o.D)(e),{action:"select-option",option:e,name:i});a?r.setValue((0,o.C)([].concat(l(u),[e])),"select-option",e):r.setValue((0,o.D)(e),"select-option")}n&&r.blurInput()},r.removeValue=function(e){var t=r.props.isMulti,n=r.state.selectValue,a=r.getOptionValue(e),i=n.filter((function(e){return r.getOptionValue(e)!==a})),u=(0,o.E)(t,i,i[0]||null);r.onChange(u,{action:"remove-value",removedValue:e}),r.focusInput()},r.clearValue=function(){var e=r.state.selectValue;r.onChange((0,o.E)(r.props.isMulti,[],null),{action:"clear",removedValues:e})},r.popValue=function(){var e=r.props.isMulti,t=r.state.selectValue,n=t[t.length-1],a=t.slice(0,t.length-1),i=(0,o.E)(e,a,a[0]||null);r.onChange(i,{action:"pop-value",removedValue:n})},r.getValue=function(){return r.state.selectValue},r.cx=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return o.F.apply(void 0,[r.props.classNamePrefix].concat(t))},r.getOptionLabel=function(e){return X(r.props,e)},r.getOptionValue=function(e){return Q(r.props,e)},r.getStyles=function(e,t){var n=z[e](t);n.boxSizing="border-box";var o=r.props.styles[e];return o?o(n,t):n},r.getElementId=function(e){return"".concat(r.instancePrefix,"-").concat(e)},r.getComponents=function(){return(0,o.G)(r.props)},r.buildCategorizedOptions=function(){return q(r.props,r.state.selectValue)},r.getCategorizedOptions=function(){return r.props.menuIsOpen?r.buildCategorizedOptions():[]},r.buildFocusableOptions=function(){return J(r.buildCategorizedOptions())},r.getFocusableOptions=function(){return r.props.menuIsOpen?r.buildFocusableOptions():[]},r.ariaOnChange=function(e,t){r.setState({ariaSelection:(0,o.a)({value:e},t)})},r.onMenuMouseDown=function(e){0===e.button&&(e.stopPropagation(),e.preventDefault(),r.focusInput())},r.onMenuMouseMove=function(e){r.blockOptionHover=!1},r.onControlMouseDown=function(e){if(!e.defaultPrevented){var t=r.props.openMenuOnClick;r.state.isFocused?r.props.menuIsOpen?"INPUT"!==e.target.tagName&&"TEXTAREA"!==e.target.tagName&&r.onMenuClose():t&&r.openMenu("first"):(t&&(r.openAfterFocus=!0),r.focusInput()),"INPUT"!==e.target.tagName&&"TEXTAREA"!==e.target.tagName&&e.preventDefault()}},r.onDropdownIndicatorMouseDown=function(e){if(!(e&&"mousedown"===e.type&&0!==e.button||r.props.isDisabled)){var t=r.props,n=t.isMulti,o=t.menuIsOpen;r.focusInput(),o?(r.setState({inputIsHiddenAfterUpdate:!n}),r.onMenuClose()):r.openMenu("first"),e.preventDefault()}},r.onClearIndicatorMouseDown=function(e){e&&"mousedown"===e.type&&0!==e.button||(r.clearValue(),e.preventDefault(),r.openAfterFocus=!1,"touchend"===e.type?r.focusInput():setTimeout((function(){return r.focusInput()})))},r.onScroll=function(e){"boolean"==typeof r.props.closeMenuOnScroll?e.target instanceof HTMLElement&&(0,o.I)(e.target)&&r.props.onMenuClose():"function"==typeof r.props.closeMenuOnScroll&&r.props.closeMenuOnScroll(e)&&r.props.onMenuClose()},r.onCompositionStart=function(){r.isComposing=!0},r.onCompositionEnd=function(){r.isComposing=!1},r.onTouchStart=function(e){var t=e.touches,n=t&&t.item(0);n&&(r.initialTouchX=n.clientX,r.initialTouchY=n.clientY,r.userIsDragging=!1)},r.onTouchMove=function(e){var t=e.touches,n=t&&t.item(0);if(n){var o=Math.abs(n.clientX-r.initialTouchX),a=Math.abs(n.clientY-r.initialTouchY);r.userIsDragging=o>5||a>5}},r.onTouchEnd=function(e){r.userIsDragging||(r.controlRef&&!r.controlRef.contains(e.target)&&r.menuListRef&&!r.menuListRef.contains(e.target)&&r.blurInput(),r.initialTouchX=0,r.initialTouchY=0)},r.onControlTouchEnd=function(e){r.userIsDragging||r.onControlMouseDown(e)},r.onClearIndicatorTouchEnd=function(e){r.userIsDragging||r.onClearIndicatorMouseDown(e)},r.onDropdownIndicatorTouchEnd=function(e){r.userIsDragging||r.onDropdownIndicatorMouseDown(e)},r.handleInputChange=function(e){var t=r.props.inputValue,n=e.currentTarget.value;r.setState({inputIsHiddenAfterUpdate:!1}),r.onInputChange(n,{action:"input-change",prevInputValue:t}),r.props.menuIsOpen||r.onMenuOpen()},r.onInputFocus=function(e){r.props.onFocus&&r.props.onFocus(e),r.setState({inputIsHiddenAfterUpdate:!1,isFocused:!0}),(r.openAfterFocus||r.props.openMenuOnFocus)&&r.openMenu("first"),r.openAfterFocus=!1},r.onInputBlur=function(e){var t=r.props.inputValue;r.menuListRef&&r.menuListRef.contains(document.activeElement)?r.inputRef.focus():(r.props.onBlur&&r.props.onBlur(e),r.onInputChange("",{action:"input-blur",prevInputValue:t}),r.onMenuClose(),r.setState({focusedValue:null,isFocused:!1}))},r.onOptionHover=function(e){r.blockOptionHover||r.state.focusedOption===e||r.setState({focusedOption:e})},r.shouldHideSelectedOptions=function(){return re(r.props)},r.onKeyDown=function(e){var t=r.props,n=t.isMulti,o=t.backspaceRemovesValue,a=t.escapeClearsValue,i=t.inputValue,u=t.isClearable,c=t.isDisabled,s=t.menuIsOpen,l=t.onKeyDown,f=t.tabSelectsValue,p=t.openMenuOnFocus,d=r.state,m=d.focusedOption,h=d.focusedValue,v=d.selectValue;if(!(c||"function"==typeof l&&(l(e),e.defaultPrevented))){switch(r.blockOptionHover=!0,e.key){case"ArrowLeft":if(!n||i)return;r.focusValue("previous");break;case"ArrowRight":if(!n||i)return;r.focusValue("next");break;case"Delete":case"Backspace":if(i)return;if(h)r.removeValue(h);else{if(!o)return;n?r.popValue():u&&r.clearValue()}break;case"Tab":if(r.isComposing)return;if(e.shiftKey||!s||!f||!m||p&&r.isOptionSelected(m,v))return;r.selectOption(m);break;case"Enter":if(229===e.keyCode)break;if(s){if(!m)return;if(r.isComposing)return;r.selectOption(m);break}return;case"Escape":s?(r.setState({inputIsHiddenAfterUpdate:!1}),r.onInputChange("",{action:"menu-close",prevInputValue:i}),r.onMenuClose()):u&&a&&r.clearValue();break;case" ":if(i)return;if(!s){r.openMenu("first");break}if(!m)return;r.selectOption(m);break;case"ArrowUp":s?r.focusOption("up"):r.openMenu("last");break;case"ArrowDown":s?r.focusOption("down"):r.openMenu("first");break;case"PageUp":if(!s)return;r.focusOption("pageup");break;case"PageDown":if(!s)return;r.focusOption("pagedown");break;case"Home":if(!s)return;r.focusOption("first");break;case"End":if(!s)return;r.focusOption("last");break;default:return}e.preventDefault()}},r.instancePrefix="react-select-"+(r.props.instanceId||++oe),r.state.selectValue=(0,o.J)(e.value),r}return(0,i.Z)(n,[{key:"componentDidMount",value:function(){this.startListeningComposition(),this.startListeningToTouch(),this.props.closeMenuOnScroll&&document&&document.addEventListener&&document.addEventListener("scroll",this.onScroll,!0),this.props.autoFocus&&this.focusInput()}},{key:"componentDidUpdate",value:function(e){var t=this.props,n=t.isDisabled,r=t.menuIsOpen,a=this.state.isFocused;(a&&!n&&e.isDisabled||a&&r&&!e.menuIsOpen)&&this.focusInput(),a&&n&&!e.isDisabled&&this.setState({isFocused:!1},this.onMenuClose),this.menuListRef&&this.focusedOptionRef&&this.scrollToFocusedOptionOnUpdate&&((0,o.K)(this.menuListRef,this.focusedOptionRef),this.scrollToFocusedOptionOnUpdate=!1)}},{key:"componentWillUnmount",value:function(){this.stopListeningComposition(),this.stopListeningToTouch(),document.removeEventListener("scroll",this.onScroll,!0)}},{key:"onMenuOpen",value:function(){this.props.onMenuOpen()}},{key:"onMenuClose",value:function(){this.onInputChange("",{action:"menu-close",prevInputValue:this.props.inputValue}),this.props.onMenuClose()}},{key:"onInputChange",value:function(e,t){this.props.onInputChange(e,t)}},{key:"focusInput",value:function(){this.inputRef&&this.inputRef.focus()}},{key:"blurInput",value:function(){this.inputRef&&this.inputRef.blur()}},{key:"openMenu",value:function(e){var t=this,n=this.state,r=n.selectValue,o=n.isFocused,a=this.buildFocusableOptions(),i="first"===e?0:a.length-1;if(!this.props.isMulti){var u=a.indexOf(r[0]);u>-1&&(i=u)}this.scrollToFocusedOptionOnUpdate=!(o&&this.menuListRef),this.setState({inputIsHiddenAfterUpdate:!1,focusedValue:null,focusedOption:a[i]},(function(){return t.onMenuOpen()}))}},{key:"focusValue",value:function(e){var t=this.state,n=t.selectValue,r=t.focusedValue;if(this.props.isMulti){this.setState({focusedOption:null});var o=n.indexOf(r);r||(o=-1);var a=n.length-1,i=-1;if(n.length){switch(e){case"previous":i=0===o?0:-1===o?a:o-1;break;case"next":o>-1&&o<a&&(i=o+1)}this.setState({inputIsHidden:-1!==i,focusedValue:n[i]})}}}},{key:"focusOption",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"first",t=this.props.pageSize,n=this.state.focusedOption,r=this.getFocusableOptions();if(r.length){var o=0,a=r.indexOf(n);n||(a=-1),"up"===e?o=a>0?a-1:r.length-1:"down"===e?o=(a+1)%r.length:"pageup"===e?(o=a-t)<0&&(o=0):"pagedown"===e?(o=a+t)>r.length-1&&(o=r.length-1):"last"===e&&(o=r.length-1),this.scrollToFocusedOptionOnUpdate=!0,this.setState({focusedOption:r[o],focusedValue:null})}}},{key:"getTheme",value:function(){return this.props.theme?"function"==typeof this.props.theme?this.props.theme(G):(0,o.a)((0,o.a)({},G),this.props.theme):G}},{key:"getCommonProps",value:function(){var e=this.clearValue,t=this.cx,n=this.getStyles,r=this.getValue,o=this.selectOption,a=this.setValue,i=this.props,u=i.isMulti,c=i.isRtl,s=i.options;return{clearValue:e,cx:t,getStyles:n,getValue:r,hasValue:this.hasValue(),isMulti:u,isRtl:c,options:s,selectOption:o,selectProps:i,setValue:a,theme:this.getTheme()}}},{key:"hasValue",value:function(){return this.state.selectValue.length>0}},{key:"hasOptions",value:function(){return!!this.getFocusableOptions().length}},{key:"isClearable",value:function(){var e=this.props,t=e.isClearable,n=e.isMulti;return void 0===t?n:t}},{key:"isOptionDisabled",value:function(e,t){return ee(this.props,e,t)}},{key:"isOptionSelected",value:function(e,t){return te(this.props,e,t)}},{key:"filterOption",value:function(e,t){return ne(this.props,e,t)}},{key:"formatOptionLabel",value:function(e,t){if("function"==typeof this.props.formatOptionLabel){var n=this.props.inputValue,r=this.state.selectValue;return this.props.formatOptionLabel(e,{context:t,inputValue:n,selectValue:r})}return this.getOptionLabel(e)}},{key:"formatGroupLabel",value:function(e){return this.props.formatGroupLabel(e)}},{key:"startListeningComposition",value:function(){document&&document.addEventListener&&(document.addEventListener("compositionstart",this.onCompositionStart,!1),document.addEventListener("compositionend",this.onCompositionEnd,!1))}},{key:"stopListeningComposition",value:function(){document&&document.removeEventListener&&(document.removeEventListener("compositionstart",this.onCompositionStart),document.removeEventListener("compositionend",this.onCompositionEnd))}},{key:"startListeningToTouch",value:function(){document&&document.addEventListener&&(document.addEventListener("touchstart",this.onTouchStart,!1),document.addEventListener("touchmove",this.onTouchMove,!1),document.addEventListener("touchend",this.onTouchEnd,!1))}},{key:"stopListeningToTouch",value:function(){document&&document.removeEventListener&&(document.removeEventListener("touchstart",this.onTouchStart),document.removeEventListener("touchmove",this.onTouchMove),document.removeEventListener("touchend",this.onTouchEnd))}},{key:"renderInput",value:function(){var e=this.props,t=e.isDisabled,n=e.isSearchable,a=e.inputId,i=e.inputValue,u=e.tabIndex,c=e.form,s=e.menuIsOpen,l=this.getComponents().Input,p=this.state,d=p.inputIsHidden,m=p.ariaSelection,h=this.commonProps,v=a||this.getElementId("input"),y=(0,o.a)((0,o.a)((0,o.a)({"aria-autocomplete":"list","aria-expanded":s,"aria-haspopup":!0,"aria-errormessage":this.props["aria-errormessage"],"aria-invalid":this.props["aria-invalid"],"aria-label":this.props["aria-label"],"aria-labelledby":this.props["aria-labelledby"],role:"combobox"},s&&{"aria-controls":this.getElementId("listbox"),"aria-owns":this.getElementId("listbox")}),!n&&{"aria-readonly":!0}),this.hasValue()?"initial-input-focus"===(null==m?void 0:m.action)&&{"aria-describedby":this.getElementId("live-region")}:{"aria-describedby":this.getElementId("placeholder")});return n?f.createElement(l,(0,r.Z)({},h,{autoCapitalize:"none",autoComplete:"off",autoCorrect:"off",id:v,innerRef:this.getInputRef,isDisabled:t,isHidden:d,onBlur:this.onInputBlur,onChange:this.handleInputChange,onFocus:this.onInputFocus,spellCheck:"false",tabIndex:u,form:c,type:"text",value:i},y)):f.createElement(j,(0,r.Z)({id:v,innerRef:this.getInputRef,onBlur:this.onInputBlur,onChange:o.L,onFocus:this.onInputFocus,disabled:t,tabIndex:u,inputMode:"none",form:c,value:""},y))}},{key:"renderPlaceholderOrValue",value:function(){var e=this,t=this.getComponents(),n=t.MultiValue,o=t.MultiValueContainer,a=t.MultiValueLabel,i=t.MultiValueRemove,u=t.SingleValue,c=t.Placeholder,s=this.commonProps,l=this.props,p=l.controlShouldRenderValue,d=l.isDisabled,m=l.isMulti,h=l.inputValue,v=l.placeholder,y=this.state,b=y.selectValue,g=y.focusedValue,O=y.isFocused;if(!this.hasValue()||!p)return h?null:f.createElement(c,(0,r.Z)({},s,{key:"placeholder",isDisabled:d,isFocused:O,innerProps:{id:this.getElementId("placeholder")}}),v);if(m)return b.map((function(t,u){var c=t===g,l="".concat(e.getOptionLabel(t),"-").concat(e.getOptionValue(t));return f.createElement(n,(0,r.Z)({},s,{components:{Container:o,Label:a,Remove:i},isFocused:c,isDisabled:d,key:l,index:u,removeProps:{onClick:function(){return e.removeValue(t)},onTouchEnd:function(){return e.removeValue(t)},onMouseDown:function(e){e.preventDefault()}},data:t}),e.formatOptionLabel(t,"value"))}));if(h)return null;var w=b[0];return f.createElement(u,(0,r.Z)({},s,{data:w,isDisabled:d}),this.formatOptionLabel(w,"value"))}},{key:"renderClearIndicator",value:function(){var e=this.getComponents().ClearIndicator,t=this.commonProps,n=this.props,o=n.isDisabled,a=n.isLoading,i=this.state.isFocused;if(!this.isClearable()||!e||o||!this.hasValue()||a)return null;var u={onMouseDown:this.onClearIndicatorMouseDown,onTouchEnd:this.onClearIndicatorTouchEnd,"aria-hidden":"true"};return f.createElement(e,(0,r.Z)({},t,{innerProps:u,isFocused:i}))}},{key:"renderLoadingIndicator",value:function(){var e=this.getComponents().LoadingIndicator,t=this.commonProps,n=this.props,o=n.isDisabled,a=n.isLoading,i=this.state.isFocused;if(!e||!a)return null;return f.createElement(e,(0,r.Z)({},t,{innerProps:{"aria-hidden":"true"},isDisabled:o,isFocused:i}))}},{key:"renderIndicatorSeparator",value:function(){var e=this.getComponents(),t=e.DropdownIndicator,n=e.IndicatorSeparator;if(!t||!n)return null;var o=this.commonProps,a=this.props.isDisabled,i=this.state.isFocused;return f.createElement(n,(0,r.Z)({},o,{isDisabled:a,isFocused:i}))}},{key:"renderDropdownIndicator",value:function(){var e=this.getComponents().DropdownIndicator;if(!e)return null;var t=this.commonProps,n=this.props.isDisabled,o=this.state.isFocused,a={onMouseDown:this.onDropdownIndicatorMouseDown,onTouchEnd:this.onDropdownIndicatorTouchEnd,"aria-hidden":"true"};return f.createElement(e,(0,r.Z)({},t,{innerProps:a,isDisabled:n,isFocused:o}))}},{key:"renderMenu",value:function(){var e=this,t=this.getComponents(),n=t.Group,a=t.GroupHeading,i=t.Menu,u=t.MenuList,c=t.MenuPortal,s=t.LoadingMessage,l=t.NoOptionsMessage,p=t.Option,d=this.commonProps,m=this.state.focusedOption,h=this.props,v=h.captureMenuScroll,y=h.inputValue,b=h.isLoading,g=h.loadingMessage,O=h.minMenuHeight,w=h.maxMenuHeight,S=h.menuIsOpen,E=h.menuPlacement,P=h.menuPosition,x=h.menuPortalTarget,R=h.menuShouldBlockScroll,C=h.menuShouldScrollIntoView,A=h.noOptionsMessage,I=h.onMenuScrollToTop,j=h.onMenuScrollToBottom;if(!S)return null;var k,M=function(t,n){var o=t.type,a=t.data,i=t.isDisabled,u=t.isSelected,c=t.label,s=t.value,l=m===a,h=i?void 0:function(){return e.onOptionHover(a)},v=i?void 0:function(){return e.selectOption(a)},y="".concat(e.getElementId("option"),"-").concat(n),b={id:y,onClick:v,onMouseMove:h,onMouseOver:h,tabIndex:-1};return f.createElement(p,(0,r.Z)({},d,{innerProps:b,data:a,isDisabled:i,isSelected:u,key:y,label:c,type:o,value:s,isFocused:l,innerRef:l?e.getFocusedOptionRef:void 0}),e.formatOptionLabel(t.data,"menu"))};if(this.hasOptions())k=this.getCategorizedOptions().map((function(t){if("group"===t.type){var o=t.data,i=t.options,u=t.index,c="".concat(e.getElementId("group"),"-").concat(u),s="".concat(c,"-heading");return f.createElement(n,(0,r.Z)({},d,{key:c,data:o,options:i,Heading:a,headingProps:{id:s,data:t.data},label:e.formatGroupLabel(t.data)}),t.options.map((function(e){return M(e,"".concat(u,"-").concat(e.index))})))}if("option"===t.type)return M(t,"".concat(t.index))}));else if(b){var D=g({inputValue:y});if(null===D)return null;k=f.createElement(s,d,D)}else{var T=A({inputValue:y});if(null===T)return null;k=f.createElement(l,d,T)}var Z={minMenuHeight:O,maxMenuHeight:w,menuPlacement:E,menuPosition:P,menuShouldScrollIntoView:C},N=f.createElement(o.M,(0,r.Z)({},d,Z),(function(t){var n=t.ref,o=t.placerProps,a=o.placement,c=o.maxHeight;return f.createElement(i,(0,r.Z)({},d,Z,{innerRef:n,innerProps:{onMouseDown:e.onMenuMouseDown,onMouseMove:e.onMenuMouseMove,id:e.getElementId("listbox")},isLoading:b,placement:a}),f.createElement($,{captureEnabled:v,onTopArrive:I,onBottomArrive:j,lockEnabled:R},(function(t){return f.createElement(u,(0,r.Z)({},d,{innerRef:function(n){e.getMenuListRef(n),t(n)},isLoading:b,maxHeight:c,focusedOption:m}),k)})))}));return x||"fixed"===P?f.createElement(c,(0,r.Z)({},d,{appendTo:x,controlElement:this.controlRef,menuPlacement:E,menuPosition:P}),N):N}},{key:"renderFormField",value:function(){var e=this,t=this.props,n=t.delimiter,r=t.isDisabled,o=t.isMulti,a=t.name,i=this.state.selectValue;if(a&&!r){if(o){if(n){var u=i.map((function(t){return e.getOptionValue(t)})).join(n);return f.createElement("input",{name:a,type:"hidden",value:u})}var c=i.length>0?i.map((function(t,n){return f.createElement("input",{key:"i-".concat(n),name:a,type:"hidden",value:e.getOptionValue(t)})})):f.createElement("input",{name:a,type:"hidden"});return f.createElement("div",null,c)}var s=i[0]?this.getOptionValue(i[0]):"";return f.createElement("input",{name:a,type:"hidden",value:s})}}},{key:"renderLiveRegion",value:function(){var e=this.commonProps,t=this.state,n=t.ariaSelection,o=t.focusedOption,a=t.focusedValue,i=t.isFocused,u=t.selectValue,c=this.getFocusableOptions();return f.createElement(b,(0,r.Z)({},e,{id:this.getElementId("live-region"),ariaSelection:n,focusedOption:o,focusedValue:a,isFocused:i,selectValue:u,focusableOptions:c}))}},{key:"render",value:function(){var e=this.getComponents(),t=e.Control,n=e.IndicatorsContainer,o=e.SelectContainer,a=e.ValueContainer,i=this.props,u=i.className,c=i.id,s=i.isDisabled,l=i.menuIsOpen,p=this.state.isFocused,d=this.commonProps=this.getCommonProps();return f.createElement(o,(0,r.Z)({},d,{className:u,innerProps:{id:c,onKeyDown:this.onKeyDown},isDisabled:s,isFocused:p}),this.renderLiveRegion(),f.createElement(t,(0,r.Z)({},d,{innerRef:this.getControlRef,innerProps:{onMouseDown:this.onControlMouseDown,onTouchEnd:this.onControlTouchEnd},isDisabled:s,isFocused:p,menuIsOpen:l}),f.createElement(a,(0,r.Z)({},d,{isDisabled:s}),this.renderPlaceholderOrValue(),this.renderInput()),f.createElement(n,(0,r.Z)({},d,{isDisabled:s}),this.renderClearIndicator(),this.renderLoadingIndicator(),this.renderIndicatorSeparator(),this.renderDropdownIndicator())),this.renderMenu(),this.renderFormField())}}],[{key:"getDerivedStateFromProps",value:function(e,t){var n=t.prevProps,r=t.clearFocusValueOnUpdate,a=t.inputIsHiddenAfterUpdate,i=t.ariaSelection,u=t.isFocused,c=t.prevWasFocused,s=e.options,l=e.value,f=e.menuIsOpen,p=e.inputValue,d=e.isMulti,m=(0,o.J)(l),h={};if(n&&(l!==n.value||s!==n.options||f!==n.menuIsOpen||p!==n.inputValue)){var v=f?function(e,t){return J(q(e,t))}(e,m):[],y=r?function(e,t){var n=e.focusedValue,r=e.selectValue.indexOf(n);if(r>-1){if(t.indexOf(n)>-1)return n;if(r<t.length)return t[r]}return null}(t,m):null,b=function(e,t){var n=e.focusedOption;return n&&t.indexOf(n)>-1?n:t[0]}(t,v);h={selectValue:m,focusedOption:b,focusedValue:y,clearFocusValueOnUpdate:!1}}var g=null!=a&&e!==n?{inputIsHidden:a,inputIsHiddenAfterUpdate:void 0}:{},O=i,w=u&&c;return u&&!w&&(O={value:(0,o.E)(d,m,m[0]||null),options:m,action:"initial-input-focus"},w=!c),"initial-input-focus"===(null==i?void 0:i.action)&&(O=null),(0,o.a)((0,o.a)((0,o.a)({},h),g),{},{prevProps:e,ariaSelection:O,prevWasFocused:w})}}]),n}(f.Component);ae.defaultProps=Y},9344:(e,t,n)=>{"use strict";n.d(t,{A:()=>M,B:()=>D,C:()=>U,D:()=>F,E:()=>_,F:()=>E,G:()=>Ve,H:()=>V,I:()=>C,J:()=>P,K:()=>k,L:()=>w,M:()=>Y,N:()=>R,_:()=>g,a:()=>v,b:()=>he,d:()=>re,e:()=>we,f:()=>me,g:()=>Ee,h:()=>Pe,i:()=>ae,j:()=>ve,k:()=>Re,l:()=>be,m:()=>K,n:()=>B,o:()=>W,p:()=>te,q:()=>je,r:()=>H,s:()=>L,t:()=>ke,u:()=>Me,v:()=>J,w:()=>Te,x:()=>Ze,y:()=>Ne,z:()=>oe});var r=n(7462),o=n(917);var a=n(4925),i=n(885);function u(e){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},u(e)}var c=n(5671),s=n(3144),l=n(9340),f=n(4942),p=n(7363),d=n(1533);function m(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function h(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function v(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?h(Object(n),!0).forEach((function(t){m(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):h(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function y(e){return y=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},y(e)}function b(e,t){return!t||"object"!=typeof t&&"function"!=typeof t?function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e):t}function g(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=y(e);if(t){var o=y(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return b(this,n)}}var O=["className","clearValue","cx","getStyles","getValue","hasValue","isMulti","isRtl","options","selectOption","selectProps","setValue","theme"],w=function(){};function S(e,t){return t?"-"===t[0]?e+t:e+"__"+t:e}function E(e,t,n){var r=[n];if(t&&e)for(var o in t)t.hasOwnProperty(o)&&t[o]&&r.push("".concat(S(e,o)));return r.filter((function(e){return e})).map((function(e){return String(e).trim()})).join(" ")}var P=function(e){return t=e,Array.isArray(t)?e.filter(Boolean):"object"===u(e)&&null!==e?[e]:[];var t},x=function(e){return e.className,e.clearValue,e.cx,e.getStyles,e.getValue,e.hasValue,e.isMulti,e.isRtl,e.options,e.selectOption,e.selectProps,e.setValue,e.theme,v({},(0,a.Z)(e,O))};function R(e,t,n){if(n){var r=n(e,t);if("string"==typeof r)return r}return e}function C(e){return[document.documentElement,document.body,window].indexOf(e)>-1}function A(e){return C(e)?window.pageYOffset:e.scrollTop}function I(e,t){C(e)?window.scrollTo(0,t):e.scrollTop=t}function j(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:200,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:w,o=A(e),a=t-o,i=0;!function t(){var u,c=a*((u=(u=i+=10)/n-1)*u*u+1)+o;I(e,c),i<n?window.requestAnimationFrame(t):r(e)}()}function k(e,t){var n=e.getBoundingClientRect(),r=t.getBoundingClientRect(),o=t.offsetHeight/3;r.bottom+o>n.bottom?I(e,Math.min(t.offsetTop+t.clientHeight-e.offsetHeight+o,e.scrollHeight)):r.top-o<n.top&&I(e,Math.max(t.offsetTop-o,0))}function M(){try{return document.createEvent("TouchEvent"),!0}catch(e){return!1}}function D(){try{return/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)}catch(e){return!1}}var T=!1,Z={get passive(){return T=!0}},N="undefined"!=typeof window?window:{};N.addEventListener&&N.removeEventListener&&(N.addEventListener("p",w,Z),N.removeEventListener("p",w,!1));var L=T;function V(e){return null!=e}function _(e,t,n){return e?t:n}function F(e){return e}function U(e){return e}var H=function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return Object.entries(e).filter((function(e){var t=(0,i.Z)(e,1)[0];return!n.includes(t)})).reduce((function(e,t){var n=(0,i.Z)(t,2),r=n[0],o=n[1];return e[r]=o,e}),{})};function $(e){var t=e.maxHeight,n=e.menuEl,r=e.minHeight,o=e.placement,a=e.shouldScroll,i=e.isFixedPosition,u=e.theme.spacing,c=function(e){var t=getComputedStyle(e),n="absolute"===t.position,r=/(auto|scroll)/;if("fixed"===t.position)return document.documentElement;for(var o=e;o=o.parentElement;)if(t=getComputedStyle(o),(!n||"static"!==t.position)&&r.test(t.overflow+t.overflowY+t.overflowX))return o;return document.documentElement}(n),s={placement:"bottom",maxHeight:t};if(!n||!n.offsetParent)return s;var l,f=c.getBoundingClientRect().height,p=n.getBoundingClientRect(),d=p.bottom,m=p.height,h=p.top,v=n.offsetParent.getBoundingClientRect().top,y=i?window.innerHeight:C(l=c)?window.innerHeight:l.clientHeight,b=A(c),g=parseInt(getComputedStyle(n).marginBottom,10),O=parseInt(getComputedStyle(n).marginTop,10),w=v-O,S=y-h,E=w+b,P=f-b-h,x=d-y+b+g,R=b+h-O,k=160;switch(o){case"auto":case"bottom":if(S>=m)return{placement:"bottom",maxHeight:t};if(P>=m&&!i)return a&&j(c,x,k),{placement:"bottom",maxHeight:t};if(!i&&P>=r||i&&S>=r)return a&&j(c,x,k),{placement:"bottom",maxHeight:i?S-g:P-g};if("auto"===o||i){var M=t,D=i?w:E;return D>=r&&(M=Math.min(D-g-u.controlHeight,t)),{placement:"top",maxHeight:M}}if("bottom"===o)return a&&I(c,x),{placement:"bottom",maxHeight:t};break;case"top":if(w>=m)return{placement:"top",maxHeight:t};if(E>=m&&!i)return a&&j(c,R,k),{placement:"top",maxHeight:t};if(!i&&E>=r||i&&w>=r){var T=t;return(!i&&E>=r||i&&w>=r)&&(T=i?w-O:E-O),a&&j(c,R,k),{placement:"top",maxHeight:T}}return{placement:"bottom",maxHeight:t};default:throw new Error('Invalid placement provided "'.concat(o,'".'))}return s}var z=function(e){return"auto"===e?"bottom":e},B=function(e){var t,n=e.placement,r=e.theme,o=r.borderRadius,a=r.spacing,i=r.colors;return t={label:"menu"},(0,f.Z)(t,function(e){return e?{bottom:"top",top:"bottom"}[e]:"bottom"}(n),"100%"),(0,f.Z)(t,"backgroundColor",i.neutral0),(0,f.Z)(t,"borderRadius",o),(0,f.Z)(t,"boxShadow","0 0 0 1px hsla(0, 0%, 0%, 0.1), 0 4px 11px hsla(0, 0%, 0%, 0.1)"),(0,f.Z)(t,"marginBottom",a.menuGutter),(0,f.Z)(t,"marginTop",a.menuGutter),(0,f.Z)(t,"position","absolute"),(0,f.Z)(t,"width","100%"),(0,f.Z)(t,"zIndex",1),t},G=(0,p.createContext)({getPortalPlacement:null}),Y=function(e){(0,l.Z)(n,e);var t=g(n);function n(){var e;(0,c.Z)(this,n);for(var r=arguments.length,o=new Array(r),a=0;a<r;a++)o[a]=arguments[a];return(e=t.call.apply(t,[this].concat(o))).state={maxHeight:e.props.maxMenuHeight,placement:null},e.context=void 0,e.getPlacement=function(t){var n=e.props,r=n.minMenuHeight,o=n.maxMenuHeight,a=n.menuPlacement,i=n.menuPosition,u=n.menuShouldScrollIntoView,c=n.theme;if(t){var s="fixed"===i,l=$({maxHeight:o,menuEl:t,minHeight:r,placement:a,shouldScroll:u&&!s,isFixedPosition:s,theme:c}),f=e.context.getPortalPlacement;f&&f(l),e.setState(l)}},e.getUpdatedProps=function(){var t=e.props.menuPlacement,n=e.state.placement||z(t);return v(v({},e.props),{},{placement:n,maxHeight:e.state.maxHeight})},e}return(0,s.Z)(n,[{key:"render",value:function(){return(0,this.props.children)({ref:this.getPlacement,placerProps:this.getUpdatedProps()})}}]),n}(p.Component);Y.contextType=G;var W=function(e){var t=e.maxHeight,n=e.theme.spacing.baseUnit;return{maxHeight:t,overflowY:"auto",paddingBottom:n,paddingTop:n,position:"relative",WebkitOverflowScrolling:"touch"}},q=function(e){var t=e.theme,n=t.spacing.baseUnit;return{color:t.colors.neutral40,padding:"".concat(2*n,"px ").concat(3*n,"px"),textAlign:"center"}},J=q,K=q,X=function(e){var t=e.children,n=e.className,a=e.cx,i=e.getStyles,u=e.innerProps;return(0,o.tZ)("div",(0,r.Z)({css:i("noOptionsMessage",e),className:a({"menu-notice":!0,"menu-notice--no-options":!0},n)},u),t)};X.defaultProps={children:"No options"};var Q=function(e){var t=e.children,n=e.className,a=e.cx,i=e.getStyles,u=e.innerProps;return(0,o.tZ)("div",(0,r.Z)({css:i("loadingMessage",e),className:a({"menu-notice":!0,"menu-notice--loading":!0},n)},u),t)};Q.defaultProps={children:"Loading..."};var ee,te=function(e){var t=e.rect,n=e.offset,r=e.position;return{left:t.left,position:r,top:n,width:t.width,zIndex:1}},ne=function(e){(0,l.Z)(n,e);var t=g(n);function n(){var e;(0,c.Z)(this,n);for(var r=arguments.length,o=new Array(r),a=0;a<r;a++)o[a]=arguments[a];return(e=t.call.apply(t,[this].concat(o))).state={placement:null},e.getPortalPlacement=function(t){var n=t.placement;n!==z(e.props.menuPlacement)&&e.setState({placement:n})},e}return(0,s.Z)(n,[{key:"render",value:function(){var e=this.props,t=e.appendTo,n=e.children,a=e.className,i=e.controlElement,u=e.cx,c=e.innerProps,s=e.menuPlacement,l=e.menuPosition,f=e.getStyles,p="fixed"===l;if(!t&&!p||!i)return null;var m=this.state.placement||z(s),h=function(e){var t=e.getBoundingClientRect();return{bottom:t.bottom,height:t.height,left:t.left,right:t.right,top:t.top,width:t.width}}(i),v=p?0:window.pageYOffset,y={offset:h[m]+v,position:l,rect:h},b=(0,o.tZ)("div",(0,r.Z)({css:f("menuPortal",y),className:u({"menu-portal":!0},a)},c),n);return(0,o.tZ)(G.Provider,{value:{getPortalPlacement:this.getPortalPlacement}},t?(0,d.createPortal)(b,t):b)}}]),n}(p.Component),re=function(e){var t=e.isDisabled;return{label:"container",direction:e.isRtl?"rtl":void 0,pointerEvents:t?"none":void 0,position:"relative"}},oe=function(e){var t=e.theme.spacing,n=e.isMulti,r=e.hasValue,o=e.selectProps.controlShouldRenderValue;return{alignItems:"center",display:n&&r&&o?"flex":"grid",flex:1,flexWrap:"wrap",padding:"".concat(t.baseUnit/2,"px ").concat(2*t.baseUnit,"px"),WebkitOverflowScrolling:"touch",position:"relative",overflow:"hidden"}},ae=function(){return{alignItems:"center",alignSelf:"stretch",display:"flex",flexShrink:0}},ie=["size"];var ue,ce,se={name:"8mmkcg",styles:"display:inline-block;fill:currentColor;line-height:1;stroke:currentColor;stroke-width:0"},le=function(e){var t=e.size,n=(0,a.Z)(e,ie);return(0,o.tZ)("svg",(0,r.Z)({height:t,width:t,viewBox:"0 0 20 20","aria-hidden":"true",focusable:"false",css:se},n))},fe=function(e){return(0,o.tZ)(le,(0,r.Z)({size:20},e),(0,o.tZ)("path",{d:"M14.348 14.849c-0.469 0.469-1.229 0.469-1.697 0l-2.651-3.030-2.651 3.029c-0.469 0.469-1.229 0.469-1.697 0-0.469-0.469-0.469-1.229 0-1.697l2.758-3.15-2.759-3.152c-0.469-0.469-0.469-1.228 0-1.697s1.228-0.469 1.697 0l2.652 3.031 2.651-3.031c0.469-0.469 1.228-0.469 1.697 0s0.469 1.229 0 1.697l-2.758 3.152 2.758 3.15c0.469 0.469 0.469 1.229 0 1.698z"}))},pe=function(e){return(0,o.tZ)(le,(0,r.Z)({size:20},e),(0,o.tZ)("path",{d:"M4.516 7.548c0.436-0.446 1.043-0.481 1.576 0l3.908 3.747 3.908-3.747c0.533-0.481 1.141-0.446 1.574 0 0.436 0.445 0.408 1.197 0 1.615-0.406 0.418-4.695 4.502-4.695 4.502-0.217 0.223-0.502 0.335-0.787 0.335s-0.57-0.112-0.789-0.335c0 0-4.287-4.084-4.695-4.502s-0.436-1.17 0-1.615z"}))},de=function(e){var t=e.isFocused,n=e.theme,r=n.spacing.baseUnit,o=n.colors;return{label:"indicatorContainer",color:t?o.neutral60:o.neutral20,display:"flex",padding:2*r,transition:"color 150ms",":hover":{color:t?o.neutral80:o.neutral40}}},me=de,he=de,ve=function(e){var t=e.isDisabled,n=e.theme,r=n.spacing.baseUnit,o=n.colors;return{label:"indicatorSeparator",alignSelf:"stretch",backgroundColor:t?o.neutral10:o.neutral20,marginBottom:2*r,marginTop:2*r,width:1}},ye=(0,o.F4)(ee||(ue=["\n  0%, 80%, 100% { opacity: 0; }\n  40% { opacity: 1; }\n"],ce||(ce=ue.slice(0)),ee=Object.freeze(Object.defineProperties(ue,{raw:{value:Object.freeze(ce)}})))),be=function(e){var t=e.isFocused,n=e.size,r=e.theme,o=r.colors,a=r.spacing.baseUnit;return{label:"loadingIndicator",color:t?o.neutral60:o.neutral20,display:"flex",padding:2*a,transition:"color 150ms",alignSelf:"center",fontSize:n,lineHeight:1,marginRight:n,textAlign:"center",verticalAlign:"middle"}},ge=function(e){var t=e.delay,n=e.offset;return(0,o.tZ)("span",{css:(0,o.iv)({animation:"".concat(ye," 1s ease-in-out ").concat(t,"ms infinite;"),backgroundColor:"currentColor",borderRadius:"1em",display:"inline-block",marginLeft:n?"1em":void 0,height:"1em",verticalAlign:"top",width:"1em"},"","")})},Oe=function(e){var t=e.className,n=e.cx,a=e.getStyles,i=e.innerProps,u=e.isRtl;return(0,o.tZ)("div",(0,r.Z)({css:a("loadingIndicator",e),className:n({indicator:!0,"loading-indicator":!0},t)},i),(0,o.tZ)(ge,{delay:0,offset:u}),(0,o.tZ)(ge,{delay:160,offset:!0}),(0,o.tZ)(ge,{delay:320,offset:!u}))};Oe.defaultProps={size:4};var we=function(e){var t=e.isDisabled,n=e.isFocused,r=e.theme,o=r.colors,a=r.borderRadius,i=r.spacing;return{label:"control",alignItems:"center",backgroundColor:t?o.neutral5:o.neutral0,borderColor:t?o.neutral10:n?o.primary:o.neutral20,borderRadius:a,borderStyle:"solid",borderWidth:1,boxShadow:n?"0 0 0 1px ".concat(o.primary):void 0,cursor:"default",display:"flex",flexWrap:"wrap",justifyContent:"space-between",minHeight:i.controlHeight,outline:"0 !important",position:"relative",transition:"all 100ms","&:hover":{borderColor:n?o.primary:o.neutral30}}},Se=["data"],Ee=function(e){var t=e.theme.spacing;return{paddingBottom:2*t.baseUnit,paddingTop:2*t.baseUnit}},Pe=function(e){var t=e.theme.spacing;return{label:"group",color:"#999",cursor:"default",display:"block",fontSize:"75%",fontWeight:500,marginBottom:"0.25em",paddingLeft:3*t.baseUnit,paddingRight:3*t.baseUnit,textTransform:"uppercase"}},xe=["innerRef","isDisabled","isHidden","inputClassName"],Re=function(e){var t=e.isDisabled,n=e.value,r=e.theme,o=r.spacing,a=r.colors;return v({margin:o.baseUnit/2,paddingBottom:o.baseUnit/2,paddingTop:o.baseUnit/2,visibility:t?"hidden":"visible",color:a.neutral80,transform:n?"translateZ(0)":""},Ae)},Ce={gridArea:"1 / 2",font:"inherit",minWidth:"2px",border:0,margin:0,outline:0,padding:0},Ae={flex:"1 1 auto",display:"inline-grid",gridArea:"1 / 1 / 2 / 3",gridTemplateColumns:"0 min-content","&:after":v({content:'attr(data-value) " "',visibility:"hidden",whiteSpace:"pre"},Ce)},Ie=function(e){return v({label:"input",color:"inherit",background:0,opacity:e?0:1,width:"100%"},Ce)},je=function(e){var t=e.theme,n=t.spacing,r=t.borderRadius;return{label:"multiValue",backgroundColor:t.colors.neutral10,borderRadius:r/2,display:"flex",margin:n.baseUnit/2,minWidth:0}},ke=function(e){var t=e.theme,n=t.borderRadius,r=t.colors,o=e.cropWithEllipsis;return{borderRadius:n/2,color:r.neutral80,fontSize:"85%",overflow:"hidden",padding:3,paddingLeft:6,textOverflow:o||void 0===o?"ellipsis":void 0,whiteSpace:"nowrap"}},Me=function(e){var t=e.theme,n=t.spacing,r=t.borderRadius,o=t.colors;return{alignItems:"center",borderRadius:r/2,backgroundColor:e.isFocused?o.dangerLight:void 0,display:"flex",paddingLeft:n.baseUnit,paddingRight:n.baseUnit,":hover":{backgroundColor:o.dangerLight,color:o.danger}}},De=function(e){var t=e.children,n=e.innerProps;return(0,o.tZ)("div",n,t)};var Te=function(e){var t=e.isDisabled,n=e.isFocused,r=e.isSelected,o=e.theme,a=o.spacing,i=o.colors;return{label:"option",backgroundColor:r?i.primary:n?i.primary25:"transparent",color:t?i.neutral20:r?i.neutral0:"inherit",cursor:"default",display:"block",fontSize:"inherit",padding:"".concat(2*a.baseUnit,"px ").concat(3*a.baseUnit,"px"),width:"100%",userSelect:"none",WebkitTapHighlightColor:"rgba(0, 0, 0, 0)",":active":{backgroundColor:t?void 0:r?i.primary:i.primary50}}},Ze=function(e){var t=e.theme,n=t.spacing;return{label:"placeholder",color:t.colors.neutral50,gridArea:"1 / 1 / 2 / 3",marginLeft:n.baseUnit/2,marginRight:n.baseUnit/2}},Ne=function(e){var t=e.isDisabled,n=e.theme,r=n.spacing,o=n.colors;return{label:"singleValue",color:t?o.neutral40:o.neutral80,gridArea:"1 / 1 / 2 / 3",marginLeft:r.baseUnit/2,marginRight:r.baseUnit/2,maxWidth:"100%",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"}},Le={ClearIndicator:function(e){var t=e.children,n=e.className,a=e.cx,i=e.getStyles,u=e.innerProps;return(0,o.tZ)("div",(0,r.Z)({css:i("clearIndicator",e),className:a({indicator:!0,"clear-indicator":!0},n)},u),t||(0,o.tZ)(fe,null))},Control:function(e){var t=e.children,n=e.cx,a=e.getStyles,i=e.className,u=e.isDisabled,c=e.isFocused,s=e.innerRef,l=e.innerProps,f=e.menuIsOpen;return(0,o.tZ)("div",(0,r.Z)({ref:s,css:a("control",e),className:n({control:!0,"control--is-disabled":u,"control--is-focused":c,"control--menu-is-open":f},i)},l),t)},DropdownIndicator:function(e){var t=e.children,n=e.className,a=e.cx,i=e.getStyles,u=e.innerProps;return(0,o.tZ)("div",(0,r.Z)({css:i("dropdownIndicator",e),className:a({indicator:!0,"dropdown-indicator":!0},n)},u),t||(0,o.tZ)(pe,null))},DownChevron:pe,CrossIcon:fe,Group:function(e){var t=e.children,n=e.className,a=e.cx,i=e.getStyles,u=e.Heading,c=e.headingProps,s=e.innerProps,l=e.label,f=e.theme,p=e.selectProps;return(0,o.tZ)("div",(0,r.Z)({css:i("group",e),className:a({group:!0},n)},s),(0,o.tZ)(u,(0,r.Z)({},c,{selectProps:p,theme:f,getStyles:i,cx:a}),l),(0,o.tZ)("div",null,t))},GroupHeading:function(e){var t=e.getStyles,n=e.cx,i=e.className,u=x(e);u.data;var c=(0,a.Z)(u,Se);return(0,o.tZ)("div",(0,r.Z)({css:t("groupHeading",e),className:n({"group-heading":!0},i)},c))},IndicatorsContainer:function(e){var t=e.children,n=e.className,a=e.cx,i=e.innerProps,u=e.getStyles;return(0,o.tZ)("div",(0,r.Z)({css:u("indicatorsContainer",e),className:a({indicators:!0},n)},i),t)},IndicatorSeparator:function(e){var t=e.className,n=e.cx,a=e.getStyles,i=e.innerProps;return(0,o.tZ)("span",(0,r.Z)({},i,{css:a("indicatorSeparator",e),className:n({"indicator-separator":!0},t)}))},Input:function(e){var t=e.className,n=e.cx,i=e.getStyles,u=e.value,c=x(e),s=c.innerRef,l=c.isDisabled,f=c.isHidden,p=c.inputClassName,d=(0,a.Z)(c,xe);return(0,o.tZ)("div",{className:n({"input-container":!0},t),css:i("input",e),"data-value":u||""},(0,o.tZ)("input",(0,r.Z)({className:n({input:!0},p),ref:s,style:Ie(f),disabled:l},d)))},LoadingIndicator:Oe,Menu:function(e){var t=e.children,n=e.className,a=e.cx,i=e.getStyles,u=e.innerRef,c=e.innerProps;return(0,o.tZ)("div",(0,r.Z)({css:i("menu",e),className:a({menu:!0},n),ref:u},c),t)},MenuList:function(e){var t=e.children,n=e.className,a=e.cx,i=e.getStyles,u=e.innerProps,c=e.innerRef,s=e.isMulti;return(0,o.tZ)("div",(0,r.Z)({css:i("menuList",e),className:a({"menu-list":!0,"menu-list--is-multi":s},n),ref:c},u),t)},MenuPortal:ne,LoadingMessage:Q,NoOptionsMessage:X,MultiValue:function(e){var t=e.children,n=e.className,r=e.components,a=e.cx,i=e.data,u=e.getStyles,c=e.innerProps,s=e.isDisabled,l=e.removeProps,f=e.selectProps,p=r.Container,d=r.Label,m=r.Remove;return(0,o.tZ)(o.ms,null,(function(r){var h=r.css,y=r.cx;return(0,o.tZ)(p,{data:i,innerProps:v({className:y(h(u("multiValue",e)),a({"multi-value":!0,"multi-value--is-disabled":s},n))},c),selectProps:f},(0,o.tZ)(d,{data:i,innerProps:{className:y(h(u("multiValueLabel",e)),a({"multi-value__label":!0},n))},selectProps:f},t),(0,o.tZ)(m,{data:i,innerProps:v({className:y(h(u("multiValueRemove",e)),a({"multi-value__remove":!0},n)),"aria-label":"Remove ".concat(t||"option")},l),selectProps:f}))}))},MultiValueContainer:De,MultiValueLabel:De,MultiValueRemove:function(e){var t=e.children,n=e.innerProps;return(0,o.tZ)("div",(0,r.Z)({role:"button"},n),t||(0,o.tZ)(fe,{size:14}))},Option:function(e){var t=e.children,n=e.className,a=e.cx,i=e.getStyles,u=e.isDisabled,c=e.isFocused,s=e.isSelected,l=e.innerRef,f=e.innerProps;return(0,o.tZ)("div",(0,r.Z)({css:i("option",e),className:a({option:!0,"option--is-disabled":u,"option--is-focused":c,"option--is-selected":s},n),ref:l,"aria-disabled":u},f),t)},Placeholder:function(e){var t=e.children,n=e.className,a=e.cx,i=e.getStyles,u=e.innerProps;return(0,o.tZ)("div",(0,r.Z)({css:i("placeholder",e),className:a({placeholder:!0},n)},u),t)},SelectContainer:function(e){var t=e.children,n=e.className,a=e.cx,i=e.getStyles,u=e.innerProps,c=e.isDisabled,s=e.isRtl;return(0,o.tZ)("div",(0,r.Z)({css:i("container",e),className:a({"--is-disabled":c,"--is-rtl":s},n)},u),t)},SingleValue:function(e){var t=e.children,n=e.className,a=e.cx,i=e.getStyles,u=e.isDisabled,c=e.innerProps;return(0,o.tZ)("div",(0,r.Z)({css:i("singleValue",e),className:a({"single-value":!0,"single-value--is-disabled":u},n)},c),t)},ValueContainer:function(e){var t=e.children,n=e.className,a=e.cx,i=e.innerProps,u=e.isMulti,c=e.getStyles,s=e.hasValue;return(0,o.tZ)("div",(0,r.Z)({css:c("valueContainer",e),className:a({"value-container":!0,"value-container--is-multi":u,"value-container--has-value":s},n)},i),t)}},Ve=function(e){return v(v({},Le),e.components)}},4766:(e,t,n)=>{"use strict";n.d(t,{u:()=>c});var r=n(9344),o=n(885),a=n(4925),i=n(7363),u=["defaultInputValue","defaultMenuIsOpen","defaultValue","inputValue","menuIsOpen","onChange","onInputChange","onMenuClose","onMenuOpen","value"];function c(e){var t=e.defaultInputValue,n=void 0===t?"":t,c=e.defaultMenuIsOpen,s=void 0!==c&&c,l=e.defaultValue,f=void 0===l?null:l,p=e.inputValue,d=e.menuIsOpen,m=e.onChange,h=e.onInputChange,v=e.onMenuClose,y=e.onMenuOpen,b=e.value,g=(0,a.Z)(e,u),O=(0,i.useState)(void 0!==p?p:n),w=(0,o.Z)(O,2),S=w[0],E=w[1],P=(0,i.useState)(void 0!==d?d:s),x=(0,o.Z)(P,2),R=x[0],C=x[1],A=(0,i.useState)(void 0!==b?b:f),I=(0,o.Z)(A,2),j=I[0],k=I[1],M=(0,i.useCallback)((function(e,t){"function"==typeof m&&m(e,t),k(e)}),[m]),D=(0,i.useCallback)((function(e,t){var n;"function"==typeof h&&(n=h(e,t)),E(void 0!==n?n:e)}),[h]),T=(0,i.useCallback)((function(){"function"==typeof y&&y(),C(!0)}),[y]),Z=(0,i.useCallback)((function(){"function"==typeof v&&v(),C(!1)}),[v]),N=void 0!==p?p:S,L=void 0!==d?d:R,V=void 0!==b?b:j;return(0,r.a)((0,r.a)({},g),{},{inputValue:N,menuIsOpen:L,onChange:M,onInputChange:D,onMenuClose:Z,onMenuOpen:T,value:V})}},1304:function(e){var t;t=function(){var e=JSON.parse('{"$":"dollar","%":"percent","&":"and","<":"less",">":"greater","|":"or","¢":"cent","£":"pound","¤":"currency","¥":"yen","©":"(c)","ª":"a","®":"(r)","º":"o","À":"A","Á":"A","Â":"A","Ã":"A","Ä":"A","Å":"A","Æ":"AE","Ç":"C","È":"E","É":"E","Ê":"E","Ë":"E","Ì":"I","Í":"I","Î":"I","Ï":"I","Ð":"D","Ñ":"N","Ò":"O","Ó":"O","Ô":"O","Õ":"O","Ö":"O","Ø":"O","Ù":"U","Ú":"U","Û":"U","Ü":"U","Ý":"Y","Þ":"TH","ß":"ss","à":"a","á":"a","â":"a","ã":"a","ä":"a","å":"a","æ":"ae","ç":"c","è":"e","é":"e","ê":"e","ë":"e","ì":"i","í":"i","î":"i","ï":"i","ð":"d","ñ":"n","ò":"o","ó":"o","ô":"o","õ":"o","ö":"o","ø":"o","ù":"u","ú":"u","û":"u","ü":"u","ý":"y","þ":"th","ÿ":"y","Ā":"A","ā":"a","Ă":"A","ă":"a","Ą":"A","ą":"a","Ć":"C","ć":"c","Č":"C","č":"c","Ď":"D","ď":"d","Đ":"DJ","đ":"dj","Ē":"E","ē":"e","Ė":"E","ė":"e","Ę":"e","ę":"e","Ě":"E","ě":"e","Ğ":"G","ğ":"g","Ģ":"G","ģ":"g","Ĩ":"I","ĩ":"i","Ī":"i","ī":"i","Į":"I","į":"i","İ":"I","ı":"i","Ķ":"k","ķ":"k","Ļ":"L","ļ":"l","Ľ":"L","ľ":"l","Ł":"L","ł":"l","Ń":"N","ń":"n","Ņ":"N","ņ":"n","Ň":"N","ň":"n","Ō":"O","ō":"o","Ő":"O","ő":"o","Œ":"OE","œ":"oe","Ŕ":"R","ŕ":"r","Ř":"R","ř":"r","Ś":"S","ś":"s","Ş":"S","ş":"s","Š":"S","š":"s","Ţ":"T","ţ":"t","Ť":"T","ť":"t","Ũ":"U","ũ":"u","Ū":"u","ū":"u","Ů":"U","ů":"u","Ű":"U","ű":"u","Ų":"U","ų":"u","Ŵ":"W","ŵ":"w","Ŷ":"Y","ŷ":"y","Ÿ":"Y","Ź":"Z","ź":"z","Ż":"Z","ż":"z","Ž":"Z","ž":"z","Ə":"E","ƒ":"f","Ơ":"O","ơ":"o","Ư":"U","ư":"u","ǈ":"LJ","ǉ":"lj","ǋ":"NJ","ǌ":"nj","Ș":"S","ș":"s","Ț":"T","ț":"t","ə":"e","˚":"o","Ά":"A","Έ":"E","Ή":"H","Ί":"I","Ό":"O","Ύ":"Y","Ώ":"W","ΐ":"i","Α":"A","Β":"B","Γ":"G","Δ":"D","Ε":"E","Ζ":"Z","Η":"H","Θ":"8","Ι":"I","Κ":"K","Λ":"L","Μ":"M","Ν":"N","Ξ":"3","Ο":"O","Π":"P","Ρ":"R","Σ":"S","Τ":"T","Υ":"Y","Φ":"F","Χ":"X","Ψ":"PS","Ω":"W","Ϊ":"I","Ϋ":"Y","ά":"a","έ":"e","ή":"h","ί":"i","ΰ":"y","α":"a","β":"b","γ":"g","δ":"d","ε":"e","ζ":"z","η":"h","θ":"8","ι":"i","κ":"k","λ":"l","μ":"m","ν":"n","ξ":"3","ο":"o","π":"p","ρ":"r","ς":"s","σ":"s","τ":"t","υ":"y","φ":"f","χ":"x","ψ":"ps","ω":"w","ϊ":"i","ϋ":"y","ό":"o","ύ":"y","ώ":"w","Ё":"Yo","Ђ":"DJ","Є":"Ye","І":"I","Ї":"Yi","Ј":"J","Љ":"LJ","Њ":"NJ","Ћ":"C","Џ":"DZ","А":"A","Б":"B","В":"V","Г":"G","Д":"D","Е":"E","Ж":"Zh","З":"Z","И":"I","Й":"J","К":"K","Л":"L","М":"M","Н":"N","О":"O","П":"P","Р":"R","С":"S","Т":"T","У":"U","Ф":"F","Х":"H","Ц":"C","Ч":"Ch","Ш":"Sh","Щ":"Sh","Ъ":"U","Ы":"Y","Ь":"","Э":"E","Ю":"Yu","Я":"Ya","а":"a","б":"b","в":"v","г":"g","д":"d","е":"e","ж":"zh","з":"z","и":"i","й":"j","к":"k","л":"l","м":"m","н":"n","о":"o","п":"p","р":"r","с":"s","т":"t","у":"u","ф":"f","х":"h","ц":"c","ч":"ch","ш":"sh","щ":"sh","ъ":"u","ы":"y","ь":"","э":"e","ю":"yu","я":"ya","ё":"yo","ђ":"dj","є":"ye","і":"i","ї":"yi","ј":"j","љ":"lj","њ":"nj","ћ":"c","ѝ":"u","џ":"dz","Ґ":"G","ґ":"g","Ғ":"GH","ғ":"gh","Қ":"KH","қ":"kh","Ң":"NG","ң":"ng","Ү":"UE","ү":"ue","Ұ":"U","ұ":"u","Һ":"H","һ":"h","Ә":"AE","ә":"ae","Ө":"OE","ө":"oe","Ա":"A","Բ":"B","Գ":"G","Դ":"D","Ե":"E","Զ":"Z","Է":"E\'","Ը":"Y\'","Թ":"T\'","Ժ":"JH","Ի":"I","Լ":"L","Խ":"X","Ծ":"C\'","Կ":"K","Հ":"H","Ձ":"D\'","Ղ":"GH","Ճ":"TW","Մ":"M","Յ":"Y","Ն":"N","Շ":"SH","Չ":"CH","Պ":"P","Ջ":"J","Ռ":"R\'","Ս":"S","Վ":"V","Տ":"T","Ր":"R","Ց":"C","Փ":"P\'","Ք":"Q\'","Օ":"O\'\'","Ֆ":"F","և":"EV","ء":"a","آ":"aa","أ":"a","ؤ":"u","إ":"i","ئ":"e","ا":"a","ب":"b","ة":"h","ت":"t","ث":"th","ج":"j","ح":"h","خ":"kh","د":"d","ذ":"th","ر":"r","ز":"z","س":"s","ش":"sh","ص":"s","ض":"dh","ط":"t","ظ":"z","ع":"a","غ":"gh","ف":"f","ق":"q","ك":"k","ل":"l","م":"m","ن":"n","ه":"h","و":"w","ى":"a","ي":"y","ً":"an","ٌ":"on","ٍ":"en","َ":"a","ُ":"u","ِ":"e","ْ":"","٠":"0","١":"1","٢":"2","٣":"3","٤":"4","٥":"5","٦":"6","٧":"7","٨":"8","٩":"9","پ":"p","چ":"ch","ژ":"zh","ک":"k","گ":"g","ی":"y","۰":"0","۱":"1","۲":"2","۳":"3","۴":"4","۵":"5","۶":"6","۷":"7","۸":"8","۹":"9","฿":"baht","ა":"a","ბ":"b","გ":"g","დ":"d","ე":"e","ვ":"v","ზ":"z","თ":"t","ი":"i","კ":"k","ლ":"l","მ":"m","ნ":"n","ო":"o","პ":"p","ჟ":"zh","რ":"r","ს":"s","ტ":"t","უ":"u","ფ":"f","ქ":"k","ღ":"gh","ყ":"q","შ":"sh","ჩ":"ch","ც":"ts","ძ":"dz","წ":"ts","ჭ":"ch","ხ":"kh","ჯ":"j","ჰ":"h","Ṣ":"S","ṣ":"s","Ẁ":"W","ẁ":"w","Ẃ":"W","ẃ":"w","Ẅ":"W","ẅ":"w","ẞ":"SS","Ạ":"A","ạ":"a","Ả":"A","ả":"a","Ấ":"A","ấ":"a","Ầ":"A","ầ":"a","Ẩ":"A","ẩ":"a","Ẫ":"A","ẫ":"a","Ậ":"A","ậ":"a","Ắ":"A","ắ":"a","Ằ":"A","ằ":"a","Ẳ":"A","ẳ":"a","Ẵ":"A","ẵ":"a","Ặ":"A","ặ":"a","Ẹ":"E","ẹ":"e","Ẻ":"E","ẻ":"e","Ẽ":"E","ẽ":"e","Ế":"E","ế":"e","Ề":"E","ề":"e","Ể":"E","ể":"e","Ễ":"E","ễ":"e","Ệ":"E","ệ":"e","Ỉ":"I","ỉ":"i","Ị":"I","ị":"i","Ọ":"O","ọ":"o","Ỏ":"O","ỏ":"o","Ố":"O","ố":"o","Ồ":"O","ồ":"o","Ổ":"O","ổ":"o","Ỗ":"O","ỗ":"o","Ộ":"O","ộ":"o","Ớ":"O","ớ":"o","Ờ":"O","ờ":"o","Ở":"O","ở":"o","Ỡ":"O","ỡ":"o","Ợ":"O","ợ":"o","Ụ":"U","ụ":"u","Ủ":"U","ủ":"u","Ứ":"U","ứ":"u","Ừ":"U","ừ":"u","Ử":"U","ử":"u","Ữ":"U","ữ":"u","Ự":"U","ự":"u","Ỳ":"Y","ỳ":"y","Ỵ":"Y","ỵ":"y","Ỷ":"Y","ỷ":"y","Ỹ":"Y","ỹ":"y","–":"-","‘":"\'","’":"\'","“":"\\"","”":"\\"","„":"\\"","†":"+","•":"*","…":"...","₠":"ecu","₢":"cruzeiro","₣":"french franc","₤":"lira","₥":"mill","₦":"naira","₧":"peseta","₨":"rupee","₩":"won","₪":"new shequel","₫":"dong","€":"euro","₭":"kip","₮":"tugrik","₯":"drachma","₰":"penny","₱":"peso","₲":"guarani","₳":"austral","₴":"hryvnia","₵":"cedi","₸":"kazakhstani tenge","₹":"indian rupee","₺":"turkish lira","₽":"russian ruble","₿":"bitcoin","℠":"sm","™":"tm","∂":"d","∆":"delta","∑":"sum","∞":"infinity","♥":"love","元":"yuan","円":"yen","﷼":"rial","ﻵ":"laa","ﻷ":"laa","ﻹ":"lai","ﻻ":"la"}'),t=JSON.parse('{"bg":{"Й":"Y","Ц":"Ts","Щ":"Sht","Ъ":"A","Ь":"Y","й":"y","ц":"ts","щ":"sht","ъ":"a","ь":"y"},"de":{"Ä":"AE","ä":"ae","Ö":"OE","ö":"oe","Ü":"UE","ü":"ue","ß":"ss","%":"prozent","&":"und","|":"oder","∑":"summe","∞":"unendlich","♥":"liebe"},"es":{"%":"por ciento","&":"y","<":"menor que",">":"mayor que","|":"o","¢":"centavos","£":"libras","¤":"moneda","₣":"francos","∑":"suma","∞":"infinito","♥":"amor"},"fr":{"%":"pourcent","&":"et","<":"plus petit",">":"plus grand","|":"ou","¢":"centime","£":"livre","¤":"devise","₣":"franc","∑":"somme","∞":"infini","♥":"amour"},"pt":{"%":"porcento","&":"e","<":"menor",">":"maior","|":"ou","¢":"centavo","∑":"soma","£":"libra","∞":"infinito","♥":"amor"},"uk":{"И":"Y","и":"y","Й":"Y","й":"y","Ц":"Ts","ц":"ts","Х":"Kh","х":"kh","Щ":"Shch","щ":"shch","Г":"H","г":"h"},"vi":{"Đ":"D","đ":"d"},"da":{"Ø":"OE","ø":"oe","Å":"AA","å":"aa","%":"procent","&":"og","|":"eller","$":"dollar","<":"mindre end",">":"større end"},"nb":{"&":"og","Å":"AA","Æ":"AE","Ø":"OE","å":"aa","æ":"ae","ø":"oe"},"it":{"&":"e"},"nl":{"&":"en"},"sv":{"&":"och","Å":"AA","Ä":"AE","Ö":"OE","å":"aa","ä":"ae","ö":"oe"}}');function n(n,r){if("string"!=typeof n)throw new Error("slugify: string argument expected");var o=t[(r="string"==typeof r?{replacement:r}:r||{}).locale]||{},a=void 0===r.replacement?"-":r.replacement,i=void 0===r.trim||r.trim,u=n.normalize().split("").reduce((function(t,n){var i=o[n]||e[n]||n;return i===a&&(i=" "),t+i.replace(r.remove||/[^\w\s$*_+~.()'"!\-:@]+/g,"")}),"");return r.strict&&(u=u.replace(/[^A-Za-z0-9\s]/g,"")),i&&(u=u.trim()),u=u.replace(/\s+/g,a),r.lower&&(u=u.toLowerCase()),u}return n.extend=function(t){Object.assign(e,t)},n},e.exports=t(),e.exports.default=t()},7363:e=>{"use strict";e.exports=React},1533:e=>{"use strict";e.exports=ReactDOM},7537:e=>{"use strict";e.exports=wp.components},2610:e=>{"use strict";e.exports=wp.element},8003:e=>{"use strict";e.exports=wp.i18n},907:(e,t,n)=>{"use strict";function r(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}n.d(t,{Z:()=>r})},5671:(e,t,n)=>{"use strict";function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}n.d(t,{Z:()=>r})},3144:(e,t,n)=>{"use strict";function r(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function o(e,t,n){return t&&r(e.prototype,t),n&&r(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}n.d(t,{Z:()=>o})},4942:(e,t,n)=>{"use strict";function r(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}n.d(t,{Z:()=>r})},7462:(e,t,n)=>{"use strict";function r(){return r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},r.apply(this,arguments)}n.d(t,{Z:()=>r})},9340:(e,t,n)=>{"use strict";function r(e,t){return r=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},r(e,t)}function o(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&r(e,t)}n.d(t,{Z:()=>o})},4925:(e,t,n)=>{"use strict";function r(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}n.d(t,{Z:()=>r})},885:(e,t,n)=>{"use strict";n.d(t,{Z:()=>o});var r=n(181);function o(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,a=[],i=!0,u=!1;try{for(n=n.call(e);!(i=(r=n.next()).done)&&(a.push(r.value),!t||a.length!==t);i=!0);}catch(e){u=!0,o=e}finally{try{i||null==n.return||n.return()}finally{if(u)throw o}}return a}}(e,t)||(0,r.Z)(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}},181:(e,t,n)=>{"use strict";n.d(t,{Z:()=>o});var r=n(907);function o(e,t){if(e){if("string"==typeof e)return(0,r.Z)(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?(0,r.Z)(e,t):void 0}}}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var a=t[r]={exports:{}};return e[r].call(a.exports,a,a.exports,n),a.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.e=()=>Promise.resolve(),n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{"use strict";var e=n(2610),t=n(7537),r=n(8003),o=n(3517),a=n.n(o),i=n(9518),u=n(7762),c=n(6521),s=n(4953);function l(e){return function(e){if(Array.isArray(e))return v(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||h(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function f(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function p(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?f(Object(n),!0).forEach((function(t){d(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):f(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function d(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function m(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==n)return;var r,o,a=[],i=!0,u=!1;try{for(n=n.call(e);!(i=(r=n.next()).done)&&(a.push(r.value),!t||a.length!==t);i=!0);}catch(e){u=!0,o=e}finally{try{i||null==n.return||n.return()}finally{if(u)throw o}}return a}(e,t)||h(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function h(e,t){if(e){if("string"==typeof e)return v(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?v(e,t):void 0}}function v(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}const y=function(n){var o=n.schemaID,f=n.schema,d=m((0,e.useState)([]),2),h=d[0],v=d[1],y=m((0,e.useState)([]),2),b=y[0],g=y[1],O=m((0,e.useState)([]),2),w=O[0],S=O[1],E=m((0,e.useState)([]),2),P=E[0],x=E[1],R=(f.title,f.type),C=(f.location,f.fields||{}),A="schemas[".concat(o,"]"),I=f._removed?f._removed.split(","):[];(0,e.useEffect)((function(){(0,i.WY)("types",{type:R}).then((function(e){var t=e.map((function(e){var t=e.id,n=j(e)||e.required||e.show&&!I.includes(t),r="".concat(A,"[fields]");r+=e.name?"[".concat(e.name,"]"):"";var o="".concat(r,"[").concat(e.id,"]"),a=k(e);return p(p({},e),{},{id:o,_id:t,std:a,parentID:r,visible:n,hidden:!1})}));v(t)}))}),[R]),(0,e.useEffect)((function(){S((function(){return h.filter((function(e){return e.visible&&!e.hidden}))})),g((function(){return h.filter((function(e){return!e.visible&&!e.hidden}))})),x((function(){return h.filter((function(e){return I.includes(e._id)}))}))}),[h]);var j=function(e){return e.name?!!C[e.name]&&C[e.name].hasOwnProperty(e.id):C.hasOwnProperty(e.id)},k=function(e){var t=a().get(e,"std","");return e.name?a().get(C,"".concat(e.name,".").concat(e.id),t):a().get(C,e.id,t)},M=function(e){var t=w.filter((function(t){return t.id===e}));S((function(t){return t.filter((function(t){return t.id!==e}))})),g((function(e){return[].concat(l(e),l(t))})),x((function(e){return[].concat(l(e),l(t))}))},D=P.map((function(e){return e._id})).join(",");return React.createElement(React.Fragment,null,D&&React.createElement("input",{type:"hidden",name:"".concat(A,"[_removed]"),value:D}),React.createElement(u.default,{property:{id:"".concat(A,"[type]"),std:R}}),w.length>0&&w.map((function(e){return React.createElement(s.Z,{key:e.id,deleteProp:M,property:e,setProperties:v})})),!["CustomJsonLd","SearchAction","BreadcrumbList"].includes(R)&&b.length>0&&React.createElement(t.Dropdown,{className:"sss-dropdown",position:"bottom right",renderToggle:function(e){var t=e.onToggle;return React.createElement("a",{href:"#",onClick:function(e){e.preventDefault(),t()}},(0,r.__)("+ Add Property","slim-seo-schema"))},renderContent:function(e){var t=e.onToggle;return React.createElement(c.Z,{items:b,onSelect:function(e){return function(e,t){t();var n=e.target.dataset.id,r=b.filter((function(e){return e.id===n}));S((function(e){return[].concat(l(e),l(r))})),g((function(e){return e.filter((function(e){return e.id!==n}))}))}(e,t)}})}}),["SearchAction","BreadcrumbList"].includes(R)&&React.createElement("p",null,(0,r.__)("This schema does not have any editable properties.","slim-seo-schema")))};var b=n(3667),g=n(4619);function O(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==n)return;var r,o,a=[],i=!0,u=!1;try{for(n=n.call(e);!(i=(r=n.next()).done)&&(a.push(r.value),!t||a.length!==t);i=!0);}catch(e){u=!0,o=e}finally{try{i||null==n.return||n.return()}finally{if(u)throw o}}return a}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return w(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return w(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function w(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function S(t){var n=t.value,o=t.onChange,a=O((0,e.useState)([]),2),u=a[0],c=a[1];return(0,e.useEffect)((function(){(0,i.WY)("data",{type:"schemas"}).then(c)}),[]),u.length>0&&React.createElement(g.Ph,{id:"schema-type",label:(0,r.__)("Schema type","slim-seo-schema"),placeholder:(0,r.__)("Inherit from global settings","slim-seo-schema"),options:u,value:n,onChange:o})}function E(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==n)return;var r,o,a=[],i=!0,u=!1;try{for(n=n.call(e);!(i=(r=n.next()).done)&&(a.push(r.value),!t||a.length!==t);i=!0);}catch(e){u=!0,o=e}finally{try{i||null==n.return||n.return()}finally{if(u)throw o}}return a}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return P(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return P(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function P(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var x=function(){var t=E((0,e.useState)(SSSchema.schema),2),n=t[0],r=t[1];return React.createElement(b.Z,null,React.createElement(S,{value:n.type,onChange:function(e){return r({type:e.target.value,fields:{}})}}),n.type&&React.createElement(y,{schemaID:"post",schema:n}))};(0,e.render)(React.createElement(x,null),document.getElementById("sss-post"))})()})();