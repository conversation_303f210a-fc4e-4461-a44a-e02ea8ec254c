import { Control } from "@elightup/form";
import { Dashicon } from "@wordpress/components";
import { useRef, useState } from "@wordpress/element";
import { __ } from "@wordpress/i18n";
import AsyncSelect from "react-select/async";
import { request, uniqueID } from "../../functions";

const GroupLocation = ( { className, property } ) => {
	/*
	groups = {
		group_id_1: {
			rule_id_1: { name: 'Name 1', value: 'Value 1' }
			rule_id_2: { name: 'Name 2', value: 'Value 2' }
		},
		group_id_2: {
			rule_id_1: { name: 'Name 1', value: 'Value 1' }
			rule_id_2: { name: 'Name 2', value: 'Value 2' }
		}
	}
	*/
	const [ groups, setGroups ] = useState( () => {
		// Already converted.
		if ( !Array.isArray( property.std ) ) {
			return property.std;
		}

		// Add unique IDs to the group and rules.
		let newGroups = {};
		property.std.forEach( group => {
			const newGroupId = uniqueID();
			newGroups[ newGroupId ] = {};

			group.forEach( rule => {
				const newRuleId = uniqueID();

				newGroups[ newGroupId ][ newRuleId ] = rule;
			} );
		} );
		return newGroups;
	} );

	const addGroup = () => setGroups( prevGroups => {
		let newGroups = { ...prevGroups };
		const newGroupId = uniqueID();
		const newRuleId = uniqueID();

		newGroups[ newGroupId ] = {
			[ newRuleId ]: {
				name: 'general:all',
				value: 'all',
				label: SSSchema.text.all
			}
		};
		return newGroups;
	} );

	return (
		<Control className={ className } label={ __( 'Location', 'slim-seo-schema' ) }>
			{
				Object.entries( groups ).map( ( [ groupId, group ] ) => (
					<Group
						key={ groupId }
						group={ group }
						locations={ property.locations }
						base={ `${ property.id }[${ groupId }]` }
					/>
				) )
			}
			<button type="button" className="button sss-location-add-group" onClick={ addGroup }>{ SSSchema.text.addGroup }</button>
		</Control>
	);
};

const Group = ( { group, locations, base } ) => {
	const [ rules, setRules ] = useState( group );

	const addRule = () => setRules( prevRules => ( {
		...prevRules,
		[ uniqueID() ]: {
			name: 'general:all',
			value: 'all',
			label: SSSchema.text.all
		}
	} ) );
	const removeRule = id => setRules( prevRules => {
		let newRules = { ...prevRules };
		delete newRules[ id ];
		return newRules;
	} );

	if ( Object.entries( rules ).length === 0 ) {
		return '';
	}

	return (
		<div className="sss-location-group">
			{
				Object.entries( rules ).map( ( [ ruleId, rule ] ) => (
					<Rule
						key={ ruleId }
						rule={ rule }
						ruleId={ ruleId }
						locations={ locations }
						base={ `${ base }[${ ruleId }]` }
						addRule={ addRule }
						removeRule={ removeRule }
					/>
				) )
			}
			<div className="sss-location-group__and">{ SSSchema.text.and }</div>
		</div>
	);
};

const Rule = ( { rule, ruleId, locations, base, addRule, removeRule } ) => {
	const inputEl = useRef();
	const [ selected, setSelected ] = useState( rule );
	const onChangeName = ( { target: { value } } ) => setSelected( prevSelected => ( { ...prevSelected, name: value } ) );
	const onChangeValue = ( item, { action } ) => {
		if ( action === 'select-option' ) {
			setSelected( prevSelected => ( { ...prevSelected, ...item } ) );
		}
	};

	const loadOptions = term => {
		const apiName = /:post$/.test( selected.name ) ? 'posts' : 'terms';
		return request( apiName, { term, name: selected.name, selected: selected.value } );
	};

	const hasValue = !selected.name.includes( 'general:' ) && !selected.name.includes( ':archive' );

	return (
		<div className="sss-location-rule">
			<select
				className="sss-location-rule__name"
				name={ `${ base }[name]` }
				defaultValue={ selected.name }
				onChange={ onChangeName }>
				{
					Object.values( locations ).map( ( { label, options } ) => (
						<optgroup key={ label } label={ label }>
							{ options.map( ( { value, label } ) => <option key={ value } value={ value }>{ label }</option> ) }
						</optgroup>
					) )
				}
			</select>
			{
				// Using an unused "key" prop for AsyncSelect forces rerendering, which makes the loadOptions callback work.
				hasValue &&
				<AsyncSelect
					className="sss-location-rule__value"
					classNamePrefix="react-select"
					name={ `${ base }[value]` }
					key={ selected.name }
					defaultOptions
					loadOptions={ loadOptions }
					defaultValue={ rule }
					onChange={ onChangeValue }
				/>
			}
			<input type="hidden" name={ `${ base }[label]` } value={ selected.label } />
			<button type="button" className="button sss-location-rule__add" onClick={ addRule }>{ SSSchema.text.or }</button>
			<button type="button" className="sss-location-rule__remove" onClick={ () => removeRule( ruleId ) }><Dashicon icon="dismiss" /></button>
		</div >
	);
};

export default GroupLocation;
