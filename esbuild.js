const esbuild = require( "esbuild" );
const { externalGlobalPlugin } = require( "esbuild-plugin-external-global" );

const config = {
	bundle: true,
	minify: true,
	loader: {
		'.js': 'jsx',
	},
	plugins: [
		externalGlobalPlugin( {
			'react': 'React',
			'react-dom': 'ReactDOM',
			'@wordpress/i18n': 'wp.i18n',
			'@wordpress/element': 'wp.element',
			'@wordpress/components': 'wp.components',
			'@wordpress/hooks': 'wp.hooks',
			'jquery': 'jQuery',
		} ),
	],
};

esbuild.build( {
	...config,
	entryPoints: [ 'js/import-export/App.js' ],
	outfile: 'js/import-export.js',
} );
