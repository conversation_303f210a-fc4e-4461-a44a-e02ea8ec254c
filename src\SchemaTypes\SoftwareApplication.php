<?php
namespace SlimSEOPro\Schema\SchemaTypes;

return [
	[
		'id'   => 'googleDocs',
		'type' => 'GoogleDocs',
		'url'  => 'https://developers.google.com/search/docs/appearance/structured-data/software-app',
		'show' => true,
	],
	[
		'label'     => __( 'Type', 'slim-seo-schema' ),
		'id'        => '@type',
		'type'      => 'ReactSelect',
		'required'  => true,
		'dependant' => true,
		'std'       => 'WebApplication',
		'options'   => [
			'MobileApplication' => __( 'Mobile application', 'slim-seo-schema' ),
			'VideoGame'         => __( 'Video game', 'slim-seo-schema' ),
			'WebApplication'    => __( 'Web application', 'slim-seo-schema' ),
		],
	],
	Helper::get_property( 'name', [
		'required' => true,
		'tooltip'  => __( 'The name of the app.', 'slim-seo-schema' ),
	] ),
	[
		'id'         => 'carrierRequirements',
		'label'      => __( 'Carrier requirements', 'slim-seo-schema' ),
		'tooltip'    => __( 'Specifies specific carrier(s) requirements for the application (e.g. an application may only work on a specific carrier network).', 'slim-seo-schema' ),
		'required'   => true,
		'dependency' => '[@type]:MobileApplication',
	],
	[
		'id'         => 'browserRequirements',
		'label'      => __( 'Browser requirements', 'slim-seo-schema' ),
		'tooltip'    => __( 'Specifies browser requirements in human-readable text. For exp "requires HTML5 support".', 'slim-seo-schema' ),
		'required'   => true,
		'dependency' => '[@type]:WebApplication',
	],
	Helper::get_property( 'Person', [
		'id'         => 'actor',

		'label'      => __( 'Actor', 'slim-seo-schema' ),
		'tooltip'    => __( 'An actor in video game.', 'slim-seo-schema' ),
		'dependency' => '[@type]:VideoGame',
	] ),
	Helper::get_property( 'Person', [
		'id'         => 'director',
		'label'      => __( 'Director', 'slim-seo-schema' ),
		'tooltip'    => __( 'A director of video game.', 'slim-seo-schema' ),
		'show'       => true,
		'dependency' => '[@type]:VideoGame',
	] ),
	[
		'id'         => 'cheatCode',
		'label'      => __( 'Cheat code', 'slim-seo-schema' ),
		'tooltip'    => __( 'Cheat codes to the game.', 'slim-seo-schema' ),
		'dependency' => '[@type]:VideoGame',
	],
	[
		'id'         => 'gameEdition',
		'label'      => __( 'Game edition', 'slim-seo-schema' ),
		'show'       => true,
		'tooltip'    => __( 'The edition of video game.', 'slim-seo-schema' ),
		'dependency' => '[@type]:VideoGame',
	],
	[
		'id'         => 'gamePlatform',
		'label'      => __( 'Game platform', 'slim-seo-schema' ),
		'show'       => true,
		'tooltip'    => __( 'The electronic systems used to play video games.', 'slim-seo-schema' ),
		'dependency' => '[@type]:VideoGame',
	],
	Helper::get_property( 'Person', [
		'id'         => 'musicBy',
		'label'      => __( 'Music by', 'slim-seo-schema' ),
		'tooltip'    => __( 'The composer of the soundtrack.', 'slim-seo-schema' ),
		'dependency' => '[@type]:VideoGame',
	] ),
	[
		'id'         => 'playMode',
		'label'      => __( 'Play mode', 'slim-seo-schema' ),
		'tooltip'    => __( 'Indicates whether this game is multi-player, co-op or single-player. The game can be marked as multi-player, co-op and single-player at the same time.', 'slim-seo-schema' ),
		'type'       => 'ReactSelect',
		'dependency' => '[@type]:VideoGame',
		'options'    => [
			'CoOp'         => __( 'Co-op', 'slim-seo-schema' ),
			'MultiPlayer'  => __( 'Multi player', 'slim-seo-schema' ),
			'SinglePlayer' => __( 'Single player', 'slim-seo-schema' ),
		],
	],
	Helper::get_property( 'VideoObject', [
		'id'         => 'trailer',
		'label'      => __( 'Trailer', 'slim-seo-schema' ),
		'tooltip'    => __( 'The trailer of video game.', 'slim-seo-schema' ),
		'dependency' => '[@type]:VideoGame',
	] ),
	Helper::get_property( 'aggregateRating', [
		'required' => true,
	] ),
	[
		'id'       => 'offers',
		'label'    => __( 'Offers', 'slim-seo-schema' ),
		'tooltip'  => __( 'An offer to sell the app.', 'slim-seo-schema' ),
		'type'     => 'Group',
		'required' => true,
		'fields'   => [
			[
				'id'       => '@type',
				'std'      => 'Offer',
				'type'     => 'Hidden',
				'required' => true,
			],
			Helper::get_property( 'name', [
				'required' => true,
				'std'      => '{{ post.title }}',
				'tooltip'  => __( 'The name of the offer.', 'slim-seo-schema' ),
			] ),
			[
				'id'       => 'acceptedPaymentMethod',
				'label'    => __( 'Accepted payment method', 'slim-seo-schema' ),
				'tooltip'  => __( 'The payment method(s) accepted by seller for this offer.', 'slim-seo-schema' ),
				'type'     => 'DataList',
				'std'      => 'http://purl.org/goodrelations/v1#Cash',
				'required' => true,
				'options'  => [
					'http://purl.org/goodrelations/v1#ByBankTransferInAdvance' => __( 'By bank transfer in advance', 'slim-seo-schema' ),
					'http://purl.org/goodrelations/v1#ByInvoice' => __( 'By invoice', 'slim-seo-schema' ),
					'http://purl.org/goodrelations/v1#Cash' => __( 'Cash', 'slim-seo-schema' ),
					'http://purl.org/goodrelations/v1#CheckInAdvance' => __( 'Check in advance', 'slim-seo-schema' ),
					'http://purl.org/goodrelations/v1#COD' => __( 'COD', 'slim-seo-schema' ),
					'http://purl.org/goodrelations/v1#DirectDebit' => __( 'Direct debit', 'slim-seo-schema' ),
					'http://purl.org/goodrelations/v1#GoogleCheckout' => __( 'Google checkout', 'slim-seo-schema' ),
					'http://purl.org/goodrelations/v1#PayPal' => __( 'PayPal', 'slim-seo-schema' ),
					'http://purl.org/goodrelations/v1#PaySwarm' => __( 'PaySwarm', 'slim-seo-schema' ),
				],
			],
			[
				'id'       => 'price',
				'label'    => __( 'Price', 'slim-seo-schema' ),
				'tooltip'  => __( 'The price of the app.', 'slim-seo-schema' ),
				'required' => true,
			],
			[
				'id'      => 'priceCurrency',
				'std'     => 'USD',
				'label'   => __( 'Price currency', 'slim-seo-schema' ),
				'tooltip' => __( 'Include if the app has a price greater than 0.', 'slim-seo-schema' ),
				'show'    => true,
			],
			[
				'id'      => 'priceValidUntil',
				'label'   => __( 'Price valid until', 'slim-seo-schema' ),
				'type'    => 'Date',
				'tooltip' => __( 'The date (in ISO 8601 date format) after which the price is no longer available.', 'slim-seo-schema' ),
			],
			Helper::get_property( 'Review' ),
			[
				'id'          => 'seller',
				'label'       => __( 'Seller', 'slim-seo-schema' ),
				'tooltip'     => __( 'An entity which offers (sells / leases / lends / loans) the services / goods. A seller may also be a provider.', 'slim-seo-schema' ),
				'description' => __( 'Please create a Person or an Organization schema and link to this property via a dynamic variable', 'slim-seo-schema' ),
			],
			[
				'label'   => __( 'Serial number', 'slim-seo-schema' ),
				'id'      => 'serialNumber',
				'tooltip' => __( 'The serial number or any alphanumeric identifier of a particular product. When attached to an offer, it is a shortcut for the serial number of the product included in the offer.', 'slim-seo-schema' ),
			],
			[
				'label'   => __( 'SKU', 'slim-seo-schema' ),
				'id'      => 'sku',
				'tooltip' => __( 'The Stock Keeping Unit (SKU), i.e. a merchant-specific identifier for a product or service, or the product to which the offer refers.', 'slim-seo-schema' ),
			],
		],
	],
	[
		'id'      => 'applicationCategory',
		'label'   => __( 'Application category', 'slim-seo-schema' ),
		'show'    => true,
		'tooltip' => __( 'Type of software application.', 'slim-seo-schema' ),
		'type'    => 'DataList',
		'std'     => 'GameApplication',
		'options' => [
			'GameApplication'               => __( 'Game application', 'slim-seo-schema' ),
			'SocialNetworkingApplication'   => __( 'Social networking application', 'slim-seo-schema' ),
			'TravelApplication'             => __( 'Travel application', 'slim-seo-schema' ),
			'ShoppingApplication'           => __( 'Shopping application', 'slim-seo-schema' ),
			'SportsApplication'             => __( 'Sports application', 'slim-seo-schema' ),
			'LifestyleApplication'          => __( 'Lifestyle application', 'slim-seo-schema' ),
			'BusinessApplication'           => __( 'Business application', 'slim-seo-schema' ),
			'DesignApplication'             => __( 'Design application', 'slim-seo-schema' ),
			'DeveloperApplication'          => __( 'Developer application', 'slim-seo-schema' ),
			'DriverApplication'             => __( 'Driver application', 'slim-seo-schema' ),
			'EducationalApplication'        => __( 'Educational application', 'slim-seo-schema' ),
			'HealthApplication'             => __( 'Health application', 'slim-seo-schema' ),
			'FinanceApplication'            => __( 'Finance application', 'slim-seo-schema' ),
			'SecurityApplication'           => __( 'Security application', 'slim-seo-schema' ),
			'BrowserApplication'            => __( 'Browser application', 'slim-seo-schema' ),
			'CommunicationApplication'      => __( 'Communication application', 'slim-seo-schema' ),
			'DesktopEnhancementApplication' => __( 'Desktop enhancement application', 'slim-seo-schema' ),
			'EntertainmentApplication'      => __( 'Entertainment application', 'slim-seo-schema' ),
			'MultimediaApplication'         => __( 'Multimedia application', 'slim-seo-schema' ),
			'HomeApplication'               => __( 'Home application', 'slim-seo-schema' ),
			'UtilitiesApplication'          => __( 'Utilities application', 'slim-seo-schema' ),
			'ReferenceApplication'          => __( 'Reference application', 'slim-seo-schema' ),
		],
	],
	[
		'id'      => 'applicationSubCategory',
		'label'   => __( 'Application subcategory', 'slim-seo-schema' ),
		'tooltip' => __( 'Subcategory of the application, e.g. \'Arcade Game\'.', 'slim-seo-schema' ),
	],
	[
		'id'      => 'applicationSuite',
		'label'   => __( 'Application suite', 'slim-seo-schema' ),
		'tooltip' => __( 'The name of the application suite to which the application belongs.', 'slim-seo-schema' ),
	],
	[
		'id'      => 'availableOnDevice',
		'label'   => __( 'Available on device', 'slim-seo-schema' ),
		'show'    => true,
		'tooltip' => __( 'Device required to run the application. Used in cases where a specific make/model is required to run the application.', 'slim-seo-schema' ),
	],
	[
		'id'      => 'countriesSupported',
		'label'   => __( 'Countries supported', 'slim-seo-schema' ),
		'tooltip' => __( 'Countries for which the application is supported. You can also provide the two-letter ISO 3166-1 alpha-2 country code.', 'slim-seo-schema' ),
	],
	[
		'id'      => 'countriesNotSupported',
		'label'   => __( 'Countries not supported', 'slim-seo-schema' ),
		'tooltip' => __( 'Countries for which the application is not supported. You can also provide the two-letter ISO 3166-1 alpha-2 country code.', 'slim-seo-schema' ),
	],
	[
		'id'      => 'downloadUrl',
		'label'   => __( 'Download url', 'slim-seo-schema' ),
		'show'    => true,
		'tooltip' => __( 'If the file can be downloaded, URL to download the binary.', 'slim-seo-schema' ),
	],
	[
		'id'      => 'featureList',
		'label'   => __( 'Feature list', 'slim-seo-schema' ),
		'show'    => true,
		'tooltip' => __( 'Features or modules provided by this application (and possibly required by other applications).', 'slim-seo-schema' ),
		'type'    => 'Textarea',
	],
	[
		'id'      => 'fileSize',
		'label'   => __( 'File size', 'slim-seo-schema' ),
		'tooltip' => __( 'Size of the application (e.g. 18MB). In the absence of a unit, KB will be assumed.', 'slim-seo-schema' ),
	],
	[
		'id'      => 'installUrl',
		'label'   => __( 'Install url', 'slim-seo-schema' ),
		'tooltip' => __( 'URL at which the app may be installed, if different from the URL of the item.', 'slim-seo-schema' ),
	],
	[
		'id'      => 'memoryRequirements',
		'label'   => __( 'Minimum memory requirements', 'slim-seo-schema' ),
		'tooltip' => __( 'Minimum memory requirements.', 'slim-seo-schema' ),
	],
	[
		'id'      => 'releaseNotes',
		'label'   => __( 'Release notes', 'slim-seo-schema' ),
		'tooltip' => __( 'Description of what changed in this version.', 'slim-seo-schema' ),
	],
	[
		'id'      => 'operatingSystem',
		'label'   => __( 'Operating system', 'slim-seo-schema' ),
		'tooltip' => __( 'The operating system(s) required to use the app (for exp. Windows 7, OSX 10.6, Android 1.6)', 'slim-seo-schema' ),
		'show'    => true,
	],
	[
		'id'      => 'permissions',
		'label'   => __( 'Permissions', 'slim-seo-schema' ),
		'tooltip' => __( 'Permission(s) required to run the app (for exp, a mobile app may require full internet access or may run only on wifi).', 'slim-seo-schema' ),
	],
	[
		'id'      => 'processorRequirements',
		'label'   => __( 'Processor requirements', 'slim-seo-schema' ),
		'tooltip' => __( 'Processor architecture required to run the application (e.g. IA64). ', 'slim-seo-schema' ),
	],
	[
		'id'      => 'releaseNotes',
		'label'   => __( 'Release notes', 'slim-seo-schema' ),
		'tooltip' => __( 'Description of what changed in this version.', 'slim-seo-schema' ),
	],
	[
		'id'      => 'screenshot',
		'label'   => __( 'Screenshot', 'slim-seo-schema' ),
		'tooltip' => __( 'A link to a screenshot image of the app.', 'slim-seo-schema' ),
	],
	[
		'id'      => 'softwareRequirements',
		'label'   => __( 'softwareRequirements', 'slim-seo-schema' ),
		'tooltip' => __( 'Component dependency requirements for application. This includes runtime environments and shared libraries that are not included in the application distribution package, but required to run the application (examples: DirectX, Java or .NET runtime).', 'slim-seo-schema' ),
	],
	[
		'id'      => 'softwareVersion',
		'label'   => __( 'Software version', 'slim-seo-schema' ),
		'tooltip' => __( 'Version of the software instance.', 'slim-seo-schema' ),
	],
	[
		'id'      => 'storageRequirements',
		'label'   => __( 'Storage requirements', 'slim-seo-schema' ),
		'tooltip' => __( 'Storage requirements (free space required).', 'slim-seo-schema' ),
	],
	[
		'id'      => 'supportingData',
		'label'   => __( 'Supporting data', 'slim-seo-schema' ),
		'tooltip' => __( 'An offer to sell the app.', 'slim-seo-schema' ),
		'type'    => 'Group',
		'fields'  => [
			[
				'id'       => '@type',
				'std'      => 'DataFeed',
				'type'     => 'Hidden',
				'required' => true,
			],
			Helper::get_property( 'Person', [
				'id'       => 'author',
				'label'    => __( 'Author', 'slim-seo-schema' ),
				'tooltip'  => __( 'The author(s) of the item.', 'slim-seo-schema' ),
				'required' => true,
			] ),
			[
				'label'   => __( 'about', 'slim-seo-schema' ),
				'id'      => 'About',
				'tooltip' => __( 'The subject matter of the item.', 'slim-seo-schema' ),
			],
			[
				'id'      => 'dataFeedElement',
				'label'   => __( 'DataFeed element', 'slim-seo-schema' ),
				'tooltip' => __( 'An item within a data feed. Data feeds may have many elements.', 'slim-seo-schema' ),
				'type'    => 'Group',
				'show'    => true,
				'fields'  => [
					[
						'id'       => '@type',
						'std'      => 'DataFeedItem',
						'type'     => 'Hidden',
						'required' => true,
					],
					Helper::get_property( 'dateCreated', [
						'show'    => true,
						'tooltip' => __( 'The date the item was first released, in ISO 8601 format.', 'slim-seo-schema' ),
						'std'     => '{{ post.date }}',
					] ),
					[
						'id'      => 'datePublished',
						'label'   => __( 'Date published', 'slim-seo-schema' ),
						'tooltip' => __( 'The datetime the item was removed from the DataFeed, in ISO 8601 format', 'slim-seo-schema' ),
						'type'    => 'Date',
					],
					Helper::get_property( 'dateModified', [
						'tooltip' => __( 'The date the item was modified, in ISO 8601 format', 'slim-seo-schema' ),
						'std'     => '{{ post.modified_date }}',
					] ),
					[
						'id'      => 'item',
						'label'   => __( 'Item', 'slim-seo-schema' ),
						'tooltip' => __( 'An entity represented by an entry in a list or data feed.', 'slim-seo-schema' ),
						'type'    => 'Group',
						'fields'  => [
							[
								'id'       => '@type',
								'std'      => 'Thing',
								'type'     => 'Hidden',
								'required' => true,
							],
							[
								'id'       => 'name',
								'required' => true,
							],
							Helper::get_property( 'description', [
								'tooltip' => __( 'A description of the item.', 'slim-seo-schema' ),
							] ),
							Helper::get_property( 'url', [
								'tooltip' => __( 'URL of the item.', 'slim-seo-schema' ),
							] ),
						],
					],
				],
			],
			[
				'id'      => 'version',
				'label'   => __( 'Version', 'slim-seo-schema' ),
				'tooltip' => __( 'Version of the item instance.', 'slim-seo-schema' ),
			],
			Helper::get_property( 'url', [
				'required' => true,
				'tooltip'  => __( 'URL of the item.', 'slim-seo-schema' ),
			] ),
		],
	],
	Helper::get_property( 'Review', [
		'required' => true,
	] ),
];
