<?php
namespace SlimSEOPro\Schema\SchemaTypes;

return [
	[
		'id'   => 'googleDocs',
		'type' => 'GoogleDocs',
		'url'  => 'https://developers.google.com/search/docs/appearance/structured-data/course',
		'show' => true,
	],
	Helper::get_property( 'name', [
		'required' => true,
		'tooltip'  => __( 'The title of the course', 'slim-seo-schema' ),
	] ),
	Helper::get_property( 'Person', [
		'id'       => 'author',
		'label'    => __( 'Author', 'slim-seo-schema' ),
		'tooltip'  => __( 'The author of the content.', 'slim-seo-schema' ),
		'required' => true,
	] ),
	Helper::get_property( 'description', [
		'required' => true,
		'tooltip'  => __( 'A description of the course. Display limit of 60 characters', 'slim-seo-schema' ),

	] ),
	Helper::get_property( 'datePublished', [
		'required' => true,
		'tooltip'  => __( 'The date and time the article was most recently modified, in ISO 8601 format', 'slim-seo-schema' ),
		'std'      => '{{ post.date }}',
	] ),
	Helper::get_property( 'dateModified', [
		'tooltip' => __( 'The date and time the article was first published, in ISO 8601 format', 'slim-seo-schema' ),
		'std'     => '{{ post.modified_date }}',
	] ),
	[
		'id'               => 'hasPart',
		'label'            => __( 'Subscription and paywalled content', 'slim-seo-schema' ),
		'type'             => 'Group',
		'tooltip'          => __( 'Indicates the content that is part of this item.', 'slim-seo-schema' ),
		'cloneable'        => true,
		'cloneItemHeading' => __( 'Part', 'slim-seo-schema' ),
		'fields'           => [
			[
				'id'       => '@type',
				'std'      => 'WebPageElement',
				'type'     => 'Hidden',
				'required' => true,
			],
			[
				'id'       => 'isAccessibleForFree',
				'label'    => __( 'Is accessible for free?', 'slim-seo-schema' ),
				'tooltip'  => __( 'Whether the dataset is accessible without payment.', 'slim-seo-schema' ),
				'type'     => 'DataList',
				'std'      => 'true',
				'options'  => [
					'true'  => __( 'Yes', 'slim-seo-schema' ),
					'false' => __( 'No', 'slim-seo-schema' ),
				],
				'required' => true,
			],
			[
				'id'       => 'cssSelector',
				'label'    => __( 'CSS selector', 'slim-seo-schema' ),
				'tooltip'  => __( 'Class name around each paywalled section of your page.', 'slim-seo-schema' ),
				'required' => true,
			],
		],
	],
	[
		'label'    => __( 'Headline', 'slim-seo-schema' ),
		'id'       => 'headline',
		'type'     => 'Text',
		'required' => true,
		'tooltip'  => __( 'The headline of the article. Headlines should not exceed 110 characters.', 'slim-seo-schema' ),
		'std'      => '{{ post.title }}',
	],
	Helper::get_property( 'image', [ 'required' => true ] ),
	[
		'id'      => 'isAccessibleForFree',
		'label'   => __( 'Is accessible for free?', 'slim-seo-schema' ),
		'tooltip' => __( 'Whether the dataset is accessible without payment.', 'slim-seo-schema' ),
		'type'    => 'DataList',
		'std'     => 'true',
		'options' => [
			'true'  => __( 'Yes', 'slim-seo-schema' ),
			'false' => __( 'No', 'slim-seo-schema' ),
		],
	],
	Helper::get_property( 'mainEntityOfPage' ),
	[
		'label'   => __( 'Provider', 'slim-seo-schema' ),
		'id'      => 'provider',
		'tooltip' => __( 'The organization that publishes the source content of the course. For example, UC Berkeley.', 'slim-seo-schema' ),
		'std'     => '{{ schemas.organization }}',
	],
	[
		'id'       => 'publisher',
		'label'    => __( 'Publisher', 'slim-seo-schema' ),
		'tooltip'  => __( 'The publisher of the creative work.', 'slim-seo-schema' ),
		'std'      => '{{ schemas.organization }}',
		'required' => true,
	],
	Helper::get_property( 'url', [
		'required' => true,
		'std'      => '{{ post.url }}',
	] ),
];
