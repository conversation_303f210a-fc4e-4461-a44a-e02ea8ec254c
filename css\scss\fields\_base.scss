.sss-field {
	.sss-panel__header {
		padding: 8px 16px;
	}

	.components-panel__body-toggle {
		padding: 8px 16px;
	}
}

.sss-field--duplicate,
.sss-field--grouplocation {
	&>.ef-control__input {
		display: block;
	}
}

.ef-control__input {
	position: relative;
}

.sss-input-wrapper {
	flex: 1;
	position: relative;
}

// Insert button.
.sss-inserter>button,
.sss-insert-image {
	box-sizing: border-box;
	height: 100%;
	color: var(--c-gray);

	.dashicons {
		font-size: 16px;
		width: 16px;
		height: 16px;
	}

	&:focus:not(:disabled) {
		box-shadow: none;
	}
}

.sss-inserter {
	position: absolute;
	top: 0;
	right: 0;
	height: 30px;
}

.sss-action {
	background: none;
	border: none;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	color: var(--c-gray);
	width: 16px;
	height: 16px;
	min-width: 16px;
	font-size: 16px;
	padding: 0;
	cursor: pointer;

	&+& {
		margin-left: 8px;
	}

	&:hover {
		color: var(--c-red);
	}
}

// Delete field button.
.sss-action--delete--field {
	background: #fff;
	visibility: hidden;
	position: absolute;
	right: -8px;
	top: -8px;

	.ef-control__input:hover>& {
		visibility: visible;
	}
}

// Google and schema.org docs.
.sss-docs-link {
	position: absolute;
	top: 0;
	right: 0;
	text-decoration: none;

	.dashicons {
		font-size: 14px;
		width: 1em;
		height: 1em;
		position: relative;
		top: 3px;
	}
}