<?php
namespace SlimSEOPro\Schema\SchemaTypes;

return [
	Helper::get_property( 'name', [
		'required' => true,
		'std'      => '{{ post.title }}',
		'tooltip'  => __( 'The name of the offer.', 'slim-seo-schema' ),
	] ),
	[
		'id'       => 'acceptedPaymentMethod',
		'label'    => __( 'Accepted payment method', 'slim-seo-schema' ),
		'tooltip'  => __( 'The payment method(s) accepted by seller for this offer.', 'slim-seo-schema' ),
		'type'     => 'DataList',
		'std'      => 'http://purl.org/goodrelations/v1#Cash',
		'required' => true,
		'options'  => [
			'http://purl.org/goodrelations/v1#ByBankTransferInAdvance' => __( 'By bank transfer in advance', 'slim-seo-schema' ),
			'http://purl.org/goodrelations/v1#ByInvoice'   => __( 'By invoice', 'slim-seo-schema' ),
			'http://purl.org/goodrelations/v1#Cash'        => __( 'Cash', 'slim-seo-schema' ),
			'http://purl.org/goodrelations/v1#CheckInAdvance' => __( 'Check in advance', 'slim-seo-schema' ),
			'http://purl.org/goodrelations/v1#COD'         => __( 'COD', 'slim-seo-schema' ),
			'http://purl.org/goodrelations/v1#DirectDebit' => __( 'Direct debit', 'slim-seo-schema' ),
			'http://purl.org/goodrelations/v1#GoogleCheckout' => __( 'Google checkout', 'slim-seo-schema' ),
			'http://purl.org/goodrelations/v1#PayPal'      => __( 'PayPal', 'slim-seo-schema' ),
			'http://purl.org/goodrelations/v1#PaySwarm'    => __( 'PaySwarm', 'slim-seo-schema' ),
		],
	],
	[
		'id'          => 'addOn',
		'label'       => __( 'Add on', 'slim-seo-schema' ),
		'tooltip'     => __( 'An additional offer that can only be obtained in combination with the first base offer (e.g. supplements and extensions that are available for a surcharge).', 'slim-seo-schema' ),
		'description' => __( 'Please create another Offer schema and link to this property via a dynamic variable', 'slim-seo-schema' ),
	],
	Helper::get_property( 'duration', [
		'id'      => 'advanceBookingRequirement',
		'label'   => __( 'Advance booking requirement', 'slim-seo-schema' ),
		'type'    => 'Group',
		'tooltip' => __( 'The amount of time that is required between accepting the offer and the actual usage of the resource or service.', 'slim-seo-schema' ),
	] ),
	Helper::get_property( 'aggregateRating', [
		'tooltip' => __( 'The overall rating, based on a collection of reviews or ratings, of the offer.', 'slim-seo-schema' ),
	] ),
	[
		'id'      => 'areaServed',
		'label'   => __( 'Area served', 'slim-seo-schema' ),
		'tooltip' => __( 'The geographic area where a service or offered item is provided.', 'slim-seo-schema' ),
	],
	[
		'id'      => 'asin',
		'label'   => __( 'Asin', 'slim-seo-schema' ),
		'tooltip' => __( 'An Amazon Standard Identification Number (ASIN) is a 10-character alphanumeric unique identifier assigned by Amazon.com and its partners for product identification within the Amazon organization. See documentation from Amazon for authoritative details', 'slim-seo-schema' ),
	],
	[
		'id'       => 'availability',
		'label'    => __( 'Availability', 'slim-seo-schema' ),
		'type'     => 'DataList',
		'std'      => 'https://schema.org/InStock',
		'required' => true,
		'options'  => [
			'https://schema.org/InStock'  => __( 'In stock', 'slim-seo-schema' ),
			'https://schema.org/SoldOut'  => __( 'Sold out', 'slim-seo-schema' ),
			'https://schema.org/PreOrder' => __( 'Pre order', 'slim-seo-schema' ),
		],
	],
	[
		'id'      => 'availabilityStarts',
		'label'   => __( 'Availability starts', 'slim-seo-schema' ),
		'tooltip' => __( 'The beginning of the availability of the product or service included in the offer.', 'slim-seo-schema' ),
		'type'    => 'Date',
	],
	[
		'id'      => 'availabilityEnds',
		'label'   => __( 'Availability ends', 'slim-seo-schema' ),
		'tooltip' => __( 'The end of the availability of the product or service included in the offer.', 'slim-seo-schema' ),
		'type'    => 'Date',
	],
	[
		'id'               => 'availableAtOrFrom',
		'label'            => __( 'Available at or from', 'slim-seo-schema' ),
		'type'             => 'Group',
		'tooltip'          => __( 'The place(s) from which the offer can be obtained (e.g. store locations).', 'slim-seo-schema' ),
		'cloneable'        => true,
		'cloneItemHeading' => __( 'Place', 'slim-seo-schema' ),
		'fields'           => [
			[
				'id'       => '@type',
				'std'      => 'Place',
				'type'     => 'Hidden',
				'required' => true,
			],
			Helper::get_property( 'name', [
				'label'    => __( 'Name', 'slim-seo-schema' ),
				'tooltip'  => __( 'The name of the item.', 'slim-seo-schema' ),
				'required' => true,
			] ),
			Helper::get_property( 'address', [
				'label'    => '',
				'required' => true,
				'tooltip'  => __( 'The physical address where students go to take the program.', 'slim-seo-schema' ),
			] ),
			[
				'id'      => 'url',
				'label'   => __( 'URL', 'slim-seo-schema' ),
				'tooltip' => __( 'URL of the item.', 'slim-seo-schema' ),
			],
		],
	],
	[
		'id'      => 'availableDeliveryMethod',
		'label'   => __( 'Available delivery method', 'slim-seo-schema' ),
		'type'    => 'DataList',
		'options' => [
			'http://purl.org/goodrelations/v1#DeliveryModeDirectDownload' => __( 'Direct download', 'slim-seo-schema' ),
			'http://purl.org/goodrelations/v1#DeliveryModeFreight' => __( 'Freight', 'slim-seo-schema' ),
			'http://purl.org/goodrelations/v1#DeliveryModeMail' => __( 'Mail', 'slim-seo-schema' ),
			'http://purl.org/goodrelations/v1#DeliveryModeOwnFleet' => __( 'Own fleet', 'slim-seo-schema' ),
			'http://purl.org/goodrelations/v1#DeliveryModePickUp' => __( 'Pick up', 'slim-seo-schema' ),
			'http://purl.org/goodrelations/v1#DHL' => __( 'DHL', 'slim-seo-schema' ),
			'http://purl.org/goodrelations/v1#FederalExpress' => __( 'Federal express', 'slim-seo-schema' ),
			'http://purl.org/goodrelations/v1#UPS' => __( 'UPS', 'slim-seo-schema' ),
		],
	],
	[
		'id'      => 'businessFunction',
		'label'   => __( 'Business function', 'slim-seo-schema' ),
		'tooltip' => __( 'The business function specifies the type of activity or access (i.e., the bundle of rights) offered by the organization or business person through the offer.', 'slim-seo-schema' ),
		'type'    => 'DataList',
		'std'     => 'http://purl.org/goodrelations/v1#Sell',
		'options' => [
			'http://purl.org/goodrelations/v1#ConstructionInstallation' => __( 'Construction installation', 'slim-seo-schema' ),
			'http://purl.org/goodrelations/v1#Dispose'  => __( 'Dispose', 'slim-seo-schema' ),
			'http://purl.org/goodrelations/v1#LeaseOut' => __( 'Lease out', 'slim-seo-schema' ),
			'http://purl.org/goodrelations/v1#Maintain' => __( 'Maintain', 'slim-seo-schema' ),
			'http://purl.org/goodrelations/v1#ProvideService' => __( 'Provide service', 'slim-seo-schema' ),
			'http://purl.org/goodrelations/v1#Repair'   => __( 'Repair', 'slim-seo-schema' ),
			'http://purl.org/goodrelations/v1#Sell'     => __( 'Sell', 'slim-seo-schema' ),
			'http://purl.org/goodrelations/v1#Buy'      => __( 'Buy', 'slim-seo-schema' ),
		],
	],
	[
		'label'   => __( 'Category', 'slim-seo-schema' ),
		'id'      => 'category',
		'tooltip' => __( 'A category for the item. Greater signs or slashes can be used to informally indicate a category hierarchy.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Checkout page URL template', 'slim-seo-schema' ),
		'id'      => 'checkoutPageURLTemplate',
		'tooltip' => __( 'A URL template (RFC 6570) for a checkout page for an offer.', 'slim-seo-schema' ),
	],
	Helper::get_property( 'duration', [
		'id'      => 'deliveryLeadTime',
		'label'   => __( 'Delivery lead time', 'slim-seo-schema' ),
		'type'    => 'Group',
		'tooltip' => __( 'The typical delay between the receipt of the order and the goods either leaving the warehouse or being prepared for pickup, in case the delivery method is on site pickup.', 'slim-seo-schema' ),
	] ),
	Helper::get_property( 'description', [
		'tooltip' => __( 'A description of the offer.', 'slim-seo-schema' ),
	] ),
	[
		'label'   => __( 'Disambiguating description', 'slim-seo-schema' ),
		'id'      => 'disambiguatingDescription',
		'tooltip' => __( 'A sub property of description. A short description of the item used to disambiguate from other.', 'slim-seo-schema' ),
	],
	[
		'id'      => 'eligibleCustomerType',
		'label'   => __( 'Eligible customer type', 'slim-seo-schema' ),
		'tooltip' => __( 'The type(s) of customers for which the given offer is valid.', 'slim-seo-schema' ),
		'type'    => 'DataList',
		'std'     => 'http://purl.org/goodrelations/v1#Business',
		'options' => [
			'http://purl.org/goodrelations/v1#Business' => __( 'Business', 'slim-seo-schema' ),
			'http://purl.org/goodrelations/v1#Enduser'  => __( 'End-user', 'slim-seo-schema' ),
			'http://purl.org/goodrelations/v1#PublicInstitution' => __( 'Public institution', 'slim-seo-schema' ),
			'http://purl.org/goodrelations/v1#Reseller' => __( 'Reseller', 'slim-seo-schema' ),
		],
	],
	Helper::get_property( 'duration', [
		'id'      => 'eligibleDuration',
		'label'   => __( 'Eligible duration', 'slim-seo-schema' ),
		'type'    => 'Group',
		'tooltip' => __( 'The duration for which the given offer is valid.', 'slim-seo-schema' ),
	] ),
	Helper::get_property( 'QuantitativeValue', [
		'id'      => 'eligibleQuantity',
		'label'   => __( 'Eligible quantity', 'slim-seo-schema' ),
		'type'    => 'Group',
		'tooltip' => __( 'The interval and unit of measurement of ordering quantities for which the offer or price specification is valid. This allows e.g. specifying that a certain freight charge is valid only for a certain quantity.', 'slim-seo-schema' ),
	] ),
	[
		'id'        => 'eligibleRegion',
		'label'     => __( 'Eligible region', 'slim-seo-schema' ),
		'tooltip'   => __( 'The ISO 3166-1 (ISO 3166-1 alpha-2) or ISO 3166-2 code, the place, or the GeoShape for the geo-political region(s) for which the offer or delivery charge specification is valid.', 'slim-seo-schema' ),
		'cloneable' => true,
	],
	[
		'id'      => 'eligibleTransactionVolume',
		'label'   => __( 'Eligible transaction volume', 'slim-seo-schema' ),
		'tooltip' => __( 'The transaction volume, in a monetary unit, for which the offer or price specification is valid.', 'slim-seo-schema' ),
		'type'    => 'Group',
		'fields'  => [
			[
				'id'       => '@type',
				'std'      => 'PriceSpecification',
				'type'     => 'Hidden',
				'required' => true,
			],
			[
				'id'       => 'price',
				'label'    => __( 'Price', 'slim-seo-schema' ),
				'tooltip'  => __( 'The price amount for the specified offer.', 'slim-seo-schema' ),
				'required' => true,
			],
			[
				'id'       => 'priceCurrency',
				'label'    => __( 'Price currency', 'slim-seo-schema' ),
				'tooltip'  => __( 'The currency of the price for the specified offer.', 'slim-seo-schema' ),
				'required' => true,
			],
		],
	],
	[
		'label'   => __( 'GTIN', 'slim-seo-schema' ),
		'id'      => 'gtin',
		'tooltip' => __( 'A Global Trade Item Number (GTIN). GTINs identify trade items, including products and services, using numeric identification codes.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'GTIN-8', 'slim-seo-schema' ),
		'id'      => 'gtin8',
		'tooltip' => __( 'The GTIN-8 code of the product, or the product to which the offer refers. This code is also known as EAN/UCC-8 or 8-digit EAN.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'GTIN-12', 'slim-seo-schema' ),
		'id'      => 'gtin12',
		'tooltip' => __( 'The GTIN-12 code of the product, or the product to which the offer refers. The GTIN-12 is the 12-digit GS1 Identification Key composed of a U.P.C. Company Prefix, Item Reference, and Check Digit used to identify trade items.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'GTIN-13', 'slim-seo-schema' ),
		'id'      => 'gtin13',
		'tooltip' => __( 'The GTIN-13 code of the product, or the product to which the offer refers. This is equivalent to 13-digit ISBN codes and EAN UCC-13. Former 12-digit UPC codes can be converted into a GTIN-13 code by simply adding a preceding zero.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'GTIN-14', 'slim-seo-schema' ),
		'id'      => 'gtin14',
		'tooltip' => __( 'The GTIN-14 code of the product, or the product to which the offer refers.', 'slim-seo-schema' ),
	],
	[
		'id'      => 'hasAdultConsideration',
		'label'   => __( 'Has adult consideration', 'slim-seo-schema' ),
		'tooltip' => __( 'Used to tag an item to be intended or suitable for consumption or use by adults only.', 'slim-seo-schema' ),
		'type'    => 'DataList',
		'options' => [
			'https://schema/org/AlcoholConsideration'    => 'Alcohol',
			'https://schema/org/DangerousGoodConsideration' => 'Dangerous good',
			'https://schema/org/HealthcareConsideration' => 'Healthcare',
			'https://schema/org/NarcoticConsideration'   => 'Narcotic',
			'https://schema/org/ReducedRelevanceForChildrenConsideration' => 'Reduced relevance for children',
			'https://schema/org/SexualContentConsideration' => 'Sexual content',
			'https://schema/org/TobaccoNicotineConsideration' => 'Tobacco nicotine',
			'https://schema/org/UnclassifiedAdultConsideration' => 'Unclassified adult',
			'https://schema/org/ViolenceConsideration'   => 'Violence',
			'https://schema/org/WeaponConsideration'     => 'Weapon',

		],
	],
	Helper::get_property( 'QuantitativeValue', [
		'id'      => 'hasMeasurement',
		'label'   => __( 'Has measurement', 'slim-seo-schema' ),
		'type'    => 'Group',
		'tooltip' => __( 'A product measurement, for example the inseam of pants, the wheel size of a bicycle, or the gauge of a screw. Usually an exact measurement, but can also be a range of measurements for adjustable products, for exp belts and ski bindings.', 'slim-seo-schema' ),
	] ),
	[
		'id'               => 'includesObject',
		'label'            => __( 'Includes object', 'slim-seo-schema' ),
		'type'             => 'Group',
		'tooltip'          => __( 'This links to a node or nodes indicating the exact quantity of the products included in an Offer or ProductCollection.', 'slim-seo-schema' ),
		'cloneable'        => true,
		'cloneItemHeading' => __( 'Object', 'slim-seo-schema' ),
		'fields'           => [
			[
				'id'       => '@type',
				'std'      => 'TypeAndQuantityNode',
				'type'     => 'Hidden',
				'required' => true,
			],
			[
				'id'      => 'amountOfThisGood',
				'label'   => __( 'Amount of this good', 'slim-seo-schema' ),
				'tooltip' => __( 'The quantity of the goods included in the offer.', 'slim-seo-schema' ),
				'show'    => true,
			],
			[
				'id'          => 'typeOfGood',
				'label'       => __( 'Type of good', 'slim-seo-schema' ),
				'tooltip'     => __( 'The product or service that this is referring to.', 'slim-seo-schema' ),
				'description' => __( 'Please create a Product or Service and link to this property via a dynamic variable.', 'slim-seo-schema' ),
				'show'        => true,
			],
			[
				'id'      => 'businessFunction',
				'label'   => __( 'Business function', 'slim-seo-schema' ),
				'tooltip' => __( 'The business function specifies the type of activity or access (i.e., the bundle of rights) offered by the organization or business person through the offer.', 'slim-seo-schema' ),
				'type'    => 'DataList',
				'std'     => 'http://purl.org/goodrelations/v1#Sell',
				'options' => [
					'http://purl.org/goodrelations/v1#ConstructionInstallation' => __( 'Construction installation', 'slim-seo-schema' ),

					'http://purl.org/goodrelations/v1#Dispose' => __( 'Dispose', 'slim-seo-schema' ),
					'http://purl.org/goodrelations/v1#LeaseOut' => __( 'Lease out', 'slim-seo-schema' ),
					'http://purl.org/goodrelations/v1#Maintain' => __( 'Maintain', 'slim-seo-schema' ),
					'http://purl.org/goodrelations/v1#ProvideService' => __( 'Provide service', 'slim-seo-schema' ),
					'http://purl.org/goodrelations/v1#Repair' => __( 'Repair', 'slim-seo-schema' ),
					'http://purl.org/goodrelations/v1#Sell' => __( 'Sell', 'slim-seo-schema' ),
					'http://purl.org/goodrelations/v1#Buy' => __( 'Buy', 'slim-seo-schema' ),
				],
			],
			[
				'id'      => 'unitCode',
				'label'   => __( 'Unit ode', 'slim-seo-schema' ),
				'tooltip' => __( 'The unit of measurement given using the UN/CEFACT Common Code (3 characters) or a URL. Other codes than the UN/CEFACT Common Code may be used with a prefix followed by a colon.', 'slim-seo-schema' ),
			],
			[
				'id'      => 'unitText',
				'label'   => __( 'Unit text', 'slim-seo-schema' ),
				'tooltip' => __( 'A string or text indicating the unit of measurement. Useful if you cannot provide a standard unit code for unitCode.', 'slim-seo-schema' ),
			],
		],
	],
	Helper::get_property( 'image', [
		'tooltip' => __( 'An image of the offer.', 'slim-seo-schema' ),
	] ),
	[
		'id'        => 'ineligibleRegion',
		'label'     => __( 'Ineligible region', 'slim-seo-schema' ),
		'tooltip'   => __( 'The ISO 3166-1 (ISO 3166-1 alpha-2) or ISO 3166-2 code, the place, or the GeoShape for the geo-political region(s) for which the offer or delivery charge specification is not valid, e.g. a region where the transaction is not allowed.', 'slim-seo-schema' ),
		'cloneable' => true,
	],
	Helper::get_property( 'QuantitativeValue', [
		'id'      => 'inventoryLevel',
		'label'   => __( 'Inventory level', 'slim-seo-schema' ),
		'type'    => 'Group',
		'tooltip' => __( 'The current approximate inventory level for the item or items.', 'slim-seo-schema' ),
	] ),
	[
		'id'      => 'isFamilyFriendly',
		'label'   => __( 'Is family friendly', 'slim-seo-schema' ),
		'tooltip' => __( 'Indicates whether this content is family friendly.', 'slim-seo-schema' ),
		'type'    => 'DataList',
		'std'     => 'True',
		'options' => [
			'True'  => __( 'True', 'slim-seo-schema' ),
			'False' => __( 'False', 'slim-seo-schema' ),
		],
	],
	Helper::get_property( 'duration', [
		'id'      => 'leaseLength',
		'label'   => __( 'Lease length', 'slim-seo-schema' ),
		'type'    => 'Group',
		'tooltip' => __( 'Length of the lease for some Accommodation, either particular to some Offer or in some cases intrinsic to the property.', 'slim-seo-schema' ),
	] ),
	[
		'label'   => __( 'Mobile URL', 'slim-seo-schema' ),
		'id'      => 'mobileUrl',
		'tooltip' => __( 'Provided for specific situations in which data consumers need to determine whether one of several provided URLs is a dedicated \'mobile site\'. The property is expected only on Product and Offer, rather than Thing.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'MPN', 'slim-seo-schema' ),
		'id'      => 'mpn',
		'tooltip' => __( 'The Manufacturer Part Number (MPN) of the product, or the product to which the offer refers', 'slim-seo-schema' ),
	],
	[
		'id'          => 'offeredBy',
		'label'       => __( 'Offered by', 'slim-seo-schema' ),
		'tooltip'     => __( 'A pointer to the organization or person making the offer.', 'slim-seo-schema' ),
		'description' => __( 'Please create a Person or an Organization schema and link to this property via a dynamic variable', 'slim-seo-schema' ),
	],
	[
		'id'       => 'price',
		'label'    => __( 'Price', 'slim-seo-schema' ),
		'tooltip'  => __( 'The offer price of a product, or of a price component when attached to PriceSpecification and its subtypes. Use the priceCurrency property (with standard formats: ISO 4217 currency format, e.g. "USD"; Ticker symbol for cryptocurrencies, e.g. "BTC"; well known names for Local Exchange Trading Systems (LETS) and other currency types, e.g. "Ithaca HOUR") instead of including ambiguous symbols such as \'$\' in the value.', 'slim-seo-schema' ),
		'required' => true,
	],
	[
		'id'       => 'priceCurrency',
		'label'    => __( 'Price currency', 'slim-seo-schema' ),
		'tooltip'  => __( 'The currency of the price, or a price component when attached to PriceSpecification and its subtypes. Use standard formats: ISO 4217 currency format, e.g. "USD"; Ticker symbol for cryptocurrencies, e.g. "BTC"; well known names for Local Exchange Trading Systems (LETS) and other currency types, e.g. "Ithaca HOUR". ', 'slim-seo-schema' ),
		'required' => true,
		'std'      => 'USD',
	],
	[
		'id'      => 'priceValidUntil',
		'label'   => __( 'Price valid until', 'slim-seo-schema' ),
		'type'    => 'Date',
		'tooltip' => __( 'The date (in ISO 8601 date format) after which the price is no longer available.', 'slim-seo-schema' ),
	],
	Helper::get_property( 'Review' ),
	[
		'id'          => 'seller',
		'label'       => __( 'Seller', 'slim-seo-schema' ),
		'tooltip'     => __( 'An entity which offers (sells / leases / lends / loans) the services / goods. A seller may also be a provider.', 'slim-seo-schema' ),
		'description' => __( 'Please create a Person or an Organization schema and link to this property via a dynamic variable', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Serial number', 'slim-seo-schema' ),
		'id'      => 'serialNumber',
		'tooltip' => __( 'The serial number or any alphanumeric identifier of a particular product. When attached to an offer, it is a shortcut for the serial number of the product included in the offer.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'SKU', 'slim-seo-schema' ),
		'id'      => 'sku',
		'tooltip' => __( 'The Stock Keeping Unit (SKU), i.e. a merchant-specific identifier for a product or service, or the product to which the offer refers.', 'slim-seo-schema' ),
	],
	[
		'id'      => 'shippingDetails',
		'label'   => __( 'Shipping details', 'slim-seo-schema' ),
		'type'    => 'Group',
		'tooltip' => __( 'Nested information about the shipping policies and options associated with an Offer', 'slim-seo-schema' ),
		'fields'  => [
			[
				'id'       => '@type',
				'std'      => 'OfferShippingDetails',
				'type'     => 'Hidden',
				'required' => true,
			],
			[
				'id'       => 'shippingDestination',
				'label'    => __( 'Destination', 'slim-seo-schema' ),
				'type'     => 'Group',
				'required' => true,
				'fields'   => [
					[
						'id'       => '@type',
						'std'      => 'DefinedRegion',
						'type'     => 'Hidden',
						'required' => true,
					],
					[
						'id'       => 'addressCountry',
						'label'    => __( 'Country code', 'slim-seo-schema' ),
						'tooltip'  => __( 'The 2-digit country code, in ISO 3166-1 format', 'slim-seo-schema' ),
						'required' => true,
					],
					[
						'id'    => 'addressRegion',
						'label' => __( 'Region', 'slim-seo-schema' ),
						'show'  => true,
					],
				],
			],
			[
				'id'      => 'deliveryTime',
				'label'   => __( 'Delivery time', 'slim-seo-schema' ),
				'type'    => 'Group',
				'tooltip' => __( 'The total delay between the receipt of the order and the goods reaching the final customer', 'slim-seo-schema' ),
				'fields'  => [
					[
						'id'       => '@type',
						'std'      => 'ShippingDeliveryTime',
						'type'     => 'Hidden',
						'required' => true,
					],
					[
						'id'      => 'handlingTime',
						'label'   => __( 'Handling time', 'slim-seo-schema' ),
						'type'    => 'Group',
						'tooltip' => __( 'The typical delay between the receipt of the order and the goods either leaving the warehouse or being prepared for pickup, in case the delivery method is on site pickup', 'slim-seo-schema' ),
						'fields'  => [
							[
								'id'       => '@type',
								'std'      => 'QuantitativeValue',
								'type'     => 'Hidden',
								'required' => true,
							],
							[
								'id'    => 'minValue',
								'label' => __( 'Min value', 'slim-seo-schema' ),
								'show'  => true,
							],
							[
								'id'    => 'maxValue',
								'label' => __( 'Max value', 'slim-seo-schema' ),
								'show'  => true,
							],
						],
					],
					[
						'id'      => 'transitTime',
						'label'   => __( 'Transit time', 'slim-seo-schema' ),
						'type'    => 'Group',
						'tooltip' => __( 'The typical delay the order has been sent for delivery and the goods reach the final customer.', 'slim-seo-schema' ),
						'fields'  => [
							[
								'id'       => '@type',
								'std'      => 'QuantitativeValue',
								'type'     => 'Hidden',
								'required' => true,
							],
							[
								'id'    => 'minValue',
								'label' => __( 'Min value', 'slim-seo-schema' ),
								'show'  => true,
							],
							[
								'id'    => 'maxValue',
								'label' => __( 'Max value', 'slim-seo-schema' ),
								'show'  => true,
							],
						],
					],
					[
						'id'      => 'cutOffTime',
						'label'   => __( 'Cutoff time', 'slim-seo-schema' ),
						'show'    => true,
						'tooltip' => __( 'The time after which new orders are no longer processed on that same day, in ISO 8601 format. One day gets added to the handling time.', 'slim-seo-schema' ),
					],
					Helper::get_property( 'OpeningHoursSpecification', [
						'id'               => 'businessDays',
						'label'            => __( 'Business days', 'slim-seo-schema' ),
						'cloneItemHeading' => __( 'Business days', 'slim-seo-schema' ),
					] ),
				],
			],
			[
				'id'      => 'doesNotShip',
				'label'   => __( 'Does Not Ship?', 'slim-seo-schema' ),
				'type'    => 'DataList',
				'options' => [
					'true'  => __( 'Yes', 'slim-seo-schema' ),
					'false' => __( 'No', 'slim-seo-schema' ),
				],
			],
			[
				'id'      => 'shippingRate',
				'label'   => __( 'Shipping rate', 'slim-seo-schema' ),
				'type'    => 'Group',
				'tooltip' => __( 'Information about the cost of shipping to the specified destination', 'slim-seo-schema' ),
				'fields'  => [
					[
						'id'       => '@type',
						'std'      => 'MonetaryAmount',
						'type'     => 'Hidden',
						'required' => true,
					],
					[
						'id'    => 'currency',
						'label' => __( 'Currency', 'slim-seo-schema' ),
						'show'  => true,
					],
					[
						'id'    => 'value',
						'label' => __( 'Value', 'slim-seo-schema' ),
						'show'  => true,
					],
				],
			],
		],
	],
	[
		'id'      => 'validFrom',
		'label'   => __( 'Valid from', 'slim-seo-schema' ),
		'type'    => 'Date',
		'tooltip' => __( 'The date when the item becomes valid in ISO-8601 format', 'slim-seo-schema' ),
	],
	[
		'id'      => 'validThrough',
		'label'   => __( 'Valid through', 'slim-seo-schema' ),
		'tooltip' => __( 'The date after when the item is not valid. For example the end of an offer, salary period, or a period of opening hours. ', 'slim-seo-schema' ),
	],
];
