{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "5863975ec172b5536c4ad8144c99d530", "packages": [{"name": "elightup/plugin-search", "version": "dev-master", "source": {"type": "git", "url": "**************:elightup/plugin-search.git", "reference": "ba2b5ef035d3131fe85009dc98aa4dff3ef06a5c"}, "default-branch": true, "type": "library", "autoload": {"psr-4": {"eLightUp\\PluginSearch\\": "src/"}, "files": ["bootstrap.php"]}, "time": "2023-02-18T16:05:15+00:00"}, {"name": "elightup/plugin-updater", "version": "dev-master", "source": {"type": "git", "url": "**************:elightup/plugin-updater.git", "reference": "92440dca65305be266ea037382e5272971fc155a"}, "default-branch": true, "type": "library", "autoload": {"psr-4": {"eLightUp\\PluginUpdater\\": ["src/"]}}, "time": "2022-08-22T07:10:02+00:00"}, {"name": "elightup/twig", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/elightup/twig", "reference": "878f6cfa57ac080192f53922f93e5bdbeda314dd"}, "default-branch": true, "type": "library", "extra": {"mozart": {"dep_namespace": "eLightUp\\", "dep_directory": "/", "classmap_directory": "/", "classmap_prefix": "eLightUp_", "packages": ["twig/twig"], "delete_vendor_directories": true}}, "autoload": {"psr-4": {"eLightUp\\": "./"}}, "scripts": {"post-install-cmd": ["mozart compose", "composer dump-autoload"], "post-update-cmd": ["mozart compose", "composer dump-autoload"]}, "time": "2022-12-03T14:54:46+00:00"}], "packages-dev": [], "aliases": [], "minimum-stability": "dev", "stability-flags": {"elightup/plugin-updater": 20, "elightup/twig": 20, "elightup/plugin-search": 20}, "prefer-stable": true, "prefer-lowest": false, "platform": [], "platform-dev": [], "plugin-api-version": "2.3.0"}