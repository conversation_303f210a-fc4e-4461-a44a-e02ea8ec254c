<?php
namespace SlimSEOPro\Schema\Integrations\ACF\Fields;

class User extends Base {
	public function get_value() {
		$value = $this->field['value'];

		if ( ! $value ) {
			return null;
		}

		if ( is_array( $value ) ) {
			$value = $value['display_name'];
		} elseif ( is_numeric( $value ) ) {
			$user  = get_userdata( $value );
			$value = $user ? $user->display_name : null;
		} else {
			$value = $value->data->display_name ?? null;
		}

		return $value;
	}
}
