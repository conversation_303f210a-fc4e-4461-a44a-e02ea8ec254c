<?php
namespace SlimSEOPro\Schema\Renderer;

use SlimSEOPro\Schema\Api\MetaKeys;
use SlimSEOPro\Schema\Settings;
use SlimSEOPro\Schema\Support\Data as SupportData;
use SlimSEO\Breadcrumbs;
use SlimSEOPro\Schema\Support\Arr;

class Data {
	private $data = [];

	public function collect(): array {
		$this->data = array_merge(
			[ 'post' => $this->get_post_data() ],
			[ 'term' => $this->get_term_data() ],
			[ 'author' => $this->get_author_data() ],
			[ 'user' => $this->get_user_data() ],
			[ 'site' => $this->get_site_data() ],
			[ 'current' => $this->get_current_data() ],
			[ 'schemas' => $this->get_schema_links() ],
		);

		$this->data = apply_filters( 'slim_seo_schema_data', $this->data );

		// Truncate the post content and set word count.
		$post_content = Normalizer::normalize( Arr::get( $this->data, 'post.content', '' ) );
		Arr::set( $this->data, 'post.content', $post_content );
		Arr::set( $this->data, 'post.word_count', str_word_count( $post_content ) );

		return $this->data;
	}

	private function get_schema_links() {
		$schemas = Settings::get_schemas();
		if ( ! $schemas ) {
			return [];
		}
		$data = [];
		foreach ( $schemas as $schema ) {
			// Bypass breadcrumbs on homepage cause it's always empty.
			if ( is_front_page() && $schema['type'] === 'BreadcrumbList' ) {
				continue;
			}

			$id          = PropParser::get_id( $schema );
			$data[ $id ] = [
				'@id' => PropParser::get_id_value( $schema ),
			];
		}

		return $data;
	}

	private function get_post_data(): array {
		$post = is_singular() ? get_queried_object() : get_post();
		if ( empty( $post ) ) {
			return [];
		}

		$post_tax   = [];
		$taxonomies = SupportData::get_taxonomies();
		foreach ( $taxonomies as $taxonomy ) {
			$post_tax[ $this->normalize( $taxonomy['slug'] ) ] = $this->get_post_terms( $post, $taxonomy['slug'] );
		}

		return [
			'ID'            => $post->ID,
			'title'         => $post->post_title,
			'excerpt'       => $post->post_excerpt,
			'content'       => $post->post_content,
			'url'           => get_permalink( $post ),
			'slug'          => $post->post_name,
			'date'          => gmdate( 'c', strtotime( $post->post_date_gmt ) ),
			'modified_date' => gmdate( 'c', strtotime( $post->post_modified_gmt ) ),
			'thumbnail'     => get_the_post_thumbnail_url( $post->ID, 'full' ),
			'comment_count' => (int) $post->comment_count,
			'tags'          => $this->get_post_terms( $post, 'post_tag' ),
			'categories'    => $this->get_post_terms( $post, 'category' ),
			'custom_field'  => $this->get_custom_field_data(),
			'tax'           => $post_tax,
		];
	}

	private function get_term_data(): array {
		$term = get_queried_object();

		if ( ! ( is_category() || is_tag() || is_tax() ) || empty( $term ) ) {
			return [];
		}

		return [
			'ID'	      => $term->term_id,
			'name'        => $term->name,
			'slug'        => $term->slug,
			'taxonomy'    => $term->taxonomy,
			'description' => $term->description,
			'url'         => get_term_link( $term->term_id ),
		];
	}

	private function get_user_data() {
		return $this->get_user( get_current_user_id() );
	}

	private function get_author_data() {
		return $this->get_user( get_the_author_meta( 'ID' ) );
	}

	private function get_user( $user_id ) {
		$user = get_userdata( $user_id );
		if ( ! $user ) {
			return [];
		}
		return [
			'ID'           => $user->ID,
			'first_name'   => $user->first_name,
			'last_name'    => $user->last_name,
			'display_name' => $user->display_name,
			'login'        => $user->user_login,
			'nickname'     => $user->nickname,
			'email'        => $user->user_email,
			'url'          => $user->user_url,
			'nicename'     => $user->user_nicename,
			'description'  => $user->description,
			'posts_url'    => get_author_posts_url( $user->ID ),
			'avatar'       => get_avatar_url( $user->ID ),
		];
	}

	private function get_site_data() {
		return [
			'title'       => get_bloginfo( 'name' ),
			'description' => get_bloginfo( 'description' ),
			'url'         => home_url( '/' ),
			'language'    => get_locale(),
			'icon'        => get_site_icon_url(),
		];
	}

	private function get_post_terms( $post, $taxonomy ) {
		$terms = get_the_terms( $post, $taxonomy );
		return is_wp_error( $terms ) ? [] : wp_list_pluck( $terms, 'name' );
	}

	private function get_custom_field_data(): array {
		$post_id     = is_singular() ? get_queried_object_id() : get_the_ID();
		if ( ! $post_id ) {
			return [];
		}
		$meta_values = get_post_meta( $post_id );
		if ( ! is_array( $meta_values ) || empty( $meta_values ) ) {
			return [];
		}
		$meta_values = array_diff_key( $meta_values, array_flip( MetaKeys::EXCLUDED ) );
		$data        = [];
		foreach ( $meta_values as $key => $value ) {
			$data[ $key ] = get_post_meta( $post_id, $key, true );
		}
		return $data;
	}

	private function get_current_data(): array {
		global $wp;

		// phpcs:ignore WordPress.Security.ValidatedSanitizedInput
		$url = $_SERVER['REQUEST_URI'] ?? add_query_arg( [], $wp->request );
		$url = home_url( $url );
		$url = esc_url( wp_strip_all_tags( $url ) );
		$url = strtok( $url, '#' );
		$url = strtok( $url, '?' );

		$breadcrumbs = new Breadcrumbs;
		$breadcrumbs->setup_args();
		$breadcrumbs->parse();
		$links = $breadcrumbs->get_links();
		$list  = [];
		foreach ( $links as $i => $link ) {
			$list[] = [
				'@type'    => 'ListItem',
				'position' => ( $i + 1 ),
				'name'     => $link['text'],
				'item'     => $link['url'],
			];
		}

		return [
			'url'         => $url,
			'title'       => wp_get_document_title(),
			'breadcrumbs' => $list,
		];
	}
	private function normalize( $key ) {
		return str_replace( '-', '_', $key );
	}
}
