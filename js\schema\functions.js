import { lazy } from "@wordpress/element";

export const uniqueID = () => Math.random().toString( 36 ).substr( 2 );

let apiCache = {};
export const request = async ( apiName, params = {}, method = 'GET', cache = true ) => {
	const cacheKey = JSON.stringify( { apiName, params, method } );
	if ( cache && apiCache[ cacheKey ] ) {
		return apiCache[ cacheKey ];
	}
	let options = {
		method,
		headers: { 'X-WP-Nonce': SSSchema.nonce, 'Content-Type': 'application/json' },
	};
	let url = `${ SSSchema.rest }/slim-seo-schema/${ apiName }`;
	const query = ( new URLSearchParams( params ) ).toString();
	if ( 'POST' === method ) {
		options.body = JSON.stringify( params );
	} else if ( query ) {
		url += SSSchema.rest.includes( '?' ) ? `&${ query }` : `?${ query }`;
	}
	const result = await fetch( url, options ).then( response => response.json() );
	apiCache[ cacheKey ] = result;
	return result;
};

let typeCache = {};
export const getFieldType = name => lazy( () => {
	if ( !typeCache[ name ] ) {
		typeCache[ name ] = import( `./components/Fields/${ name }` );
	}
	return typeCache[ name ];
} );