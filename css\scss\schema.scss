#schema {
	--c-red: #e53e3e;
	--c-lighter: #f0f0f0;
	--c-light: #ccd0d4;
	--c-gray: #999;

	&.ss-tab-pane {
		padding: 0;
	}
}

.sss-none {
	padding: 16px;
	border-bottom: 1px solid var(--c-lighter);
}

.sss-schema-actions {
	display: flex;
	align-items: center;
	padding: 16px;

	.sss-dropdown {
		margin-left: 4px;
	}
}

.sss-schema__header {
	margin-bottom: 16px;
}

.sss-panel {
	flex: 1;
	background: #fff;
	border-bottom: 1px solid var(--c-lighter);

	.sss-panel:not(.sss-panel--no-border) {
		border: 1px solid var(--c-lighter);
	}

	&__title {
		font-weight: 600;
		min-width: 300px;

		input {
			border: 1px solid transparent;
			border-width: 0 0 1px 0;
			border-radius: 0;
			background: transparent;
			padding: 4px 0;
			line-height: 1;
			min-height: auto;
			width: 100%;

			&:hover,
			&:focus {
				border-color: var(--c-light);
				outline: none;
				box-shadow: none;
			}
		}
	}

	&__header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		cursor: pointer;
		padding: 8px 16px;

		.sss-panel--expanded>& {
			background: var(--c-lighter);
		}
	}

	&__body {
		transition: all 0.2s ease;
		padding: 0 16px;
		max-height: 0;
		overflow: hidden;

		.sss-panel--expanded>& {
			max-height: 999999px;
			padding: 16px;
			overflow: inherit;
		}
	}

	&--no-border {
		border: none;

		>.sss-panel__body {
			padding: 0;
		}
	}
}

.sss-type-select {
	width: 400px;
	max-width: 100%;
}

.sss-dropdown {
	.components-popover__content {
		min-width: 300px;
		max-width: 100%;
	}

	&__search {
		margin-bottom: 8px;

		input {
			width: 100%;
		}
	}

	&__title {
		margin: 18px 0 0;
		text-transform: uppercase;
		font-size: 11px;
		font-weight: 500;
		color: var(--c-gray);
	}

	&__items {
		margin: 4px 0 0;
	}

	&__item {
		cursor: pointer;
		margin: 0;
		padding: 4px 8px;
		border-radius: 4px;
		white-space: nowrap;
		text-overflow: ellipsis;

		&:hover {
			background: var(--c-lighter);
		}
	}

	&__no-results {
		width: 100%;
		margin-top: 16px;
	}
}

.sss-field--textarea {
	align-items: flex-start;

	textarea {
		width: 100%;
	}

	.sss-input {
		align-items: flex-start;
	}
}

.sss-modal-overlay {
	position: fixed;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	z-index: 10;
	background: rgba(0, 0, 0, 0.5);
}

.sss-modal-body {
	padding: 15px;
	position: fixed;
	top: 50%;
	left: 50%;
	z-index: 11;
	min-width: 330px;
	border-radius: 4px;
	box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
		0 2px 4px -1px rgba(0, 0, 0, 0.06);
	background: #ffffff;
	opacity: 1;
	transform: translate(-50%, -50%) scale(1);
	transform-origin: center;
	animation: zoom 0.25s;
}

@keyframes zoom {
	from {
		opacity: 0;
		transform: translate(-50%, -50%) scale(0.7);
	}
}

.sss-modal-heading {
	margin: 0 0 15px;
	padding-bottom: 8px;
	border-bottom: 1px solid #ddd;
	display: flex;
	justify-content: space-between;
	align-items: center;
	font-size: 14px;
}

.sss-modal__close {
	font-size: 16px;
	cursor: pointer;
}

.sss-tooltip-icon .dashicon {
	color: var(--c-light);
	font-size: 14px;
	position: relative;
	top: 2px;
}

.ssp-custom-group .ef-control__input:not(.child) {
	display: flex;
	flex-direction: column;
}

.ssp-custom-group .ef-control__input.child {
	margin-bottom: 4px;
	margin-left: 0;
}

@import 'general';
@import 'utils';
@import 'import-export';
@import '../../node_modules/@elightup/form/style/form.scss';
@import './fields/base';
@import './fields/radio';
@import './fields/react-select';
@import './fields/location';
@import './fields/custom';
@import './fields/image';
@import './fields/datetime';
@import './mobile';