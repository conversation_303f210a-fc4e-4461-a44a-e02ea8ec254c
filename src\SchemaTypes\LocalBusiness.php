<?php
namespace SlimSEOPro\Schema\SchemaTypes;

return [
	[
		'id'       => '@id',
		'type'     => 'Hidden',
		'std'      => '{{ site.url }}#{{ id }}',
		'required' => true,
	],
	[
		'id'   => 'googleDocs',
		'type' => 'GoogleDocs',
		'url'  => 'https://developers.google.com/search/docs/appearance/structured-data/local-business',
		'show' => true,
	],
	[
		'label'    => __( 'Type', 'slim-seo-schema' ),
		'id'       => '@type',
		'required' => true,
		'std'      => 'LocalBusiness',
		'type'     => 'Select',
		'options'  => [
			[
				'label'   => __( 'General', 'slim-seo-schema' ),
				'options' => [
					'LocalBusiness'            => __( 'Local business', 'slim-seo-schema' ),
					'AnimalShelter'            => __( 'Animal shelter', 'slim-seo-schema' ),
					'ArchiveOrganization'      => __( 'Archive organization', 'slim-seo-schema' ),
					'ChildCare'                => __( 'Child care', 'slim-seo-schema' ),
					'DryCleaningOrLaundry'     => __( 'Dry cleaning or laundry', 'slim-seo-schema' ),
					'EmploymentAgency'         => __( 'Employment agency', 'slim-seo-schema' ),
					'InternetCafe'             => __( 'Internet cafe', 'slim-seo-schema' ),
					'Library'                  => __( 'Library', 'slim-seo-schema' ),
					'ProfessionalService'      => __( 'Professional service', 'slim-seo-schema' ),
					'RadioStation'             => __( 'Radio station', 'slim-seo-schema' ),
					'RealEstateAgent'          => __( 'Real-estate agent', 'slim-seo-schema' ),
					'RecyclingCenter'          => __( 'Recycling center', 'slim-seo-schema' ),
					'SelfStorage'              => __( 'Self storage', 'slim-seo-schema' ),
					'ShoppingCenter'           => __( 'Shopping center', 'slim-seo-schema' ),
					'TelevisionStation'        => __( 'Television station', 'slim-seo-schema' ),
					'TouristInformationCenter' => __( 'Tourist information center', 'slim-seo-schema' ),
					'TravelAgency'             => __( 'Travel agency', 'slim-seo-schema' ),
				],
			],
			[
				'label'   => __( 'Automotive business', 'slim-seo-schema' ),
				'options' => [
					'AutoBodyShop'       => __( 'Auto body shop', 'slim-seo-schema' ),
					'AutoDealer'         => __( 'Auto dealer', 'slim-seo-schema' ),
					'AutoPartsStore'     => __( 'Auto parts store', 'slim-seo-schema' ),
					'AutoRental'         => __( 'Auto rental', 'slim-seo-schema' ),
					'AutoRepair'         => __( 'Auto repair', 'slim-seo-schema' ),
					'AutoWash'           => __( 'Auto wash', 'slim-seo-schema' ),
					'GasStation'         => __( 'Gas station', 'slim-seo-schema' ),
					'MotorcycleDealer'   => __( 'Motorcycle dealer', 'slim-seo-schema' ),
					'MotorcycleRepair'   => __( 'Motorcycle repair', 'slim-seo-schema' ),
					'AutomotiveBusiness' => __( 'Other', 'slim-seo-schema' ),
				],
			],
			[
				'label'   => __( 'Emergency service', 'slim-seo-schema' ),
				'options' => [
					'FireStation'      => __( 'Fire station', 'slim-seo-schema' ),
					'Hospital'         => __( 'Hospital', 'slim-seo-schema' ),
					'PoliceStation'    => __( 'Police station', 'slim-seo-schema' ),
					'EmergencyService' => __( 'Other', 'slim-seo-schema' ),
				],
			],
			[
				'label'   => __( 'Entertainment business', 'slim-seo-schema' ),
				'options' => [
					'AdultEntertainment'    => __( 'Adult entertainment', 'slim-seo-schema' ),
					'AmusementPark'         => __( 'Amusement park', 'slim-seo-schema' ),
					'ArtGallery'            => __( 'Art gallery', 'slim-seo-schema' ),
					'Casino'                => __( 'Casino', 'slim-seo-schema' ),
					'ComedyClub'            => __( 'Comedy club', 'slim-seo-schema' ),
					'MovieTheater'          => __( 'Movie theater', 'slim-seo-schema' ),
					'NightClub'             => __( 'Night club', 'slim-seo-schema' ),
					'EntertainmentBusiness' => __( 'Other', 'slim-seo-schema' ),
				],
			],
			[
				'label'   => __( 'Financial service', 'slim-seo-schema' ),
				'options' => [
					'AccountingService' => __( 'Accounting service', 'slim-seo-schema' ),
					'AutomatedTeller'   => __( 'Automated teller', 'slim-seo-schema' ),
					'BankOrCreditUnion' => __( 'Bank or credit union', 'slim-seo-schema' ),
					'InsuranceAgency'   => __( 'Insurance agency', 'slim-seo-schema' ),
					'FinancialService'  => __( 'Other', 'slim-seo-schema' ),
				],
			],
			[
				'label'   => __( 'Food establishment', 'slim-seo-schema' ),
				'options' => [
					'Bakery'             => __( 'Bakery', 'slim-seo-schema' ),
					'BarOrPub'           => __( 'Bar or pub', 'slim-seo-schema' ),
					'Brewery'            => __( 'Brewery', 'slim-seo-schema' ),
					'CafeOrCoffeeShop'   => __( 'Cafe or coffee shop', 'slim-seo-schema' ),
					'Distillery'         => __( 'Distillery', 'slim-seo-schema' ),
					'FastFoodRestaurant' => __( 'Fast-food restaurant', 'slim-seo-schema' ),
					'IceCreamShop'       => __( 'Ice-cream shop', 'slim-seo-schema' ),
					'Restaurant'         => __( 'Restaurant', 'slim-seo-schema' ),
					'Winery'             => __( 'Winery', 'slim-seo-schema' ),
					'FoodEstablishment'  => __( 'Other', 'slim-seo-schema' ),
				],
			],
			[
				'label'   => __( 'Government office', 'slim-seo-schema' ),
				'options' => [
					'PostOffice'       => __( 'Post office', 'slim-seo-schema' ),
					'GovernmentOffice' => __( 'Other', 'slim-seo-schema' ),
				],
			],
			[
				'label'   => __( 'Health and beauty business', 'slim-seo-schema' ),
				'options' => [
					'BeautySalon'             => __( 'Beauty salon', 'slim-seo-schema' ),
					'DaySpa'                  => __( 'Day spa', 'slim-seo-schema' ),
					'HairSalon'               => __( 'Hair salon', 'slim-seo-schema' ),
					'HealthClub'              => __( 'Health club', 'slim-seo-schema' ),
					'NailSalon'               => __( 'Nail salon', 'slim-seo-schema' ),
					'TattooParlor'            => __( 'Tattoo parlor', 'slim-seo-schema' ),
					'HealthAndBeautyBusiness' => __( 'Other', 'slim-seo-schema' ),
				],
			],
			[
				'label'   => __( 'Home and construction business', 'slim-seo-schema' ),
				'options' => [
					'Electrician'                 => __( 'Electrician', 'slim-seo-schema' ),
					'GeneralContractor'           => __( 'General contractor', 'slim-seo-schema' ),
					'HVACBusiness'                => __( 'HVAC business', 'slim-seo-schema' ),
					'HousePainter'                => __( 'House painter', 'slim-seo-schema' ),
					'Locksmith'                   => __( 'Locksmith', 'slim-seo-schema' ),
					'MovingCompany'               => __( 'Moving company', 'slim-seo-schema' ),
					'Plumber'                     => __( 'Plumber', 'slim-seo-schema' ),
					'RoofingContractor'           => __( 'Roofing contractor', 'slim-seo-schema' ),
					'HomeAndConstructionBusiness' => __( 'Other', 'slim-seo-schema' ),
				],
			],
			[
				'label'   => __( 'Legal service', 'slim-seo-schema' ),
				'options' => [
					'Attorney'     => __( 'Attorney', 'slim-seo-schema' ),
					'Notary'       => __( 'Notary', 'slim-seo-schema' ),
					'LegalService' => __( 'Other', 'slim-seo-schema' ),
				],
			],
			[
				'label'   => __( 'Lodging business', 'slim-seo-schema' ),
				'options' => [
					'BedAndBreakfast' => __( 'Bed and breakfast', 'slim-seo-schema' ),
					'Campground'      => __( 'Campground', 'slim-seo-schema' ),
					'Hostel'          => __( 'Hostel', 'slim-seo-schema' ),
					'Hotel'           => __( 'Hotel', 'slim-seo-schema' ),
					'Motel'           => __( 'Motel', 'slim-seo-schema' ),
					'Resort'          => __( 'Resort', 'slim-seo-schema' ),
					'LodgingBusiness' => __( 'Other', 'slim-seo-schema' ),
				],
			],
			[
				'label'   => __( 'Medical business', 'slim-seo-schema' ),
				'options' => [
					'CommunityHealth' => __( 'Community health', 'slim-seo-schema' ),
					'Dentist'         => __( 'Dentist', 'slim-seo-schema' ),
					'Dermatology'     => __( 'Dermatology', 'slim-seo-schema' ),
					'DietNutrition'   => __( 'Diet nutrition', 'slim-seo-schema' ),
					'Emergency'       => __( 'Emergency', 'slim-seo-schema' ),
					'Geriatric'       => __( 'Geriatric', 'slim-seo-schema' ),
					'Gynecologic'     => __( 'Gynecologic', 'slim-seo-schema' ),
					'MedicalClinic'   => __( 'Medical clinic', 'slim-seo-schema' ),
					'Midwifery'       => __( 'Midwifery', 'slim-seo-schema' ),
					'Nursing'         => __( 'Nursing', 'slim-seo-schema' ),
					'Obstetric'       => __( 'Obstetric', 'slim-seo-schema' ),
					'Oncologic'       => __( 'Oncologic', 'slim-seo-schema' ),
					'Optician'        => __( 'Optician', 'slim-seo-schema' ),
					'Optometric'      => __( 'Optometric', 'slim-seo-schema' ),
					'Otolaryngologic' => __( 'Otolaryngologic', 'slim-seo-schema' ),
					'Pediatric'       => __( 'Pediatric', 'slim-seo-schema' ),
					'Pharmacy'        => __( 'Pharmacy', 'slim-seo-schema' ),
					'Physician'       => __( 'Physician', 'slim-seo-schema' ),
					'Physiotherapy'   => __( 'Physiotherapy', 'slim-seo-schema' ),
					'PlasticSurgery'  => __( 'Plastic surgery', 'slim-seo-schema' ),
					'Podiatric'       => __( 'Podiatric', 'slim-seo-schema' ),
					'PrimaryCare'     => __( 'Primary care', 'slim-seo-schema' ),
					'Psychiatric'     => __( 'Psychiatric', 'slim-seo-schema' ),
					'PublicHealth'    => __( 'Public health', 'slim-seo-schema' ),
					'MedicalBusiness' => __( 'Other', 'slim-seo-schema' ),
				],
			],
			[
				'label'   => __( 'Sports activity location', 'slim-seo-schema' ),
				'options' => [
					'BowlingAlley'           => __( 'Bowling alley', 'slim-seo-schema' ),
					'ExerciseGym'            => __( 'Exercise gym', 'slim-seo-schema' ),
					'GolfCourse'             => __( 'Golf course', 'slim-seo-schema' ),
					'HealthClub'             => __( 'Health club', 'slim-seo-schema' ),
					'PublicSwimmingPool'     => __( 'Public swimming pool', 'slim-seo-schema' ),
					'SkiResort'              => __( 'SkiResort', 'slim-seo-schema' ),
					'SportsClub'             => __( 'Sports club', 'slim-seo-schema' ),
					'StadiumOrArena'         => __( 'Stadium or arena', 'slim-seo-schema' ),
					'TennisComplex'          => __( 'Tennis complex', 'slim-seo-schema' ),
					'SportsActivityLocation' => __( 'Other', 'slim-seo-schema' ),
				],
			],
			[
				'label'   => __( 'Store', 'slim-seo-schema' ),
				'options' => [
					'AutoPartsStore'       => __( 'Auto parts store', 'slim-seo-schema' ),
					'BikeStore'            => __( 'Bike store', 'slim-seo-schema' ),
					'BookStore'            => __( 'Book store', 'slim-seo-schema' ),
					'ClothingStore'        => __( 'Clothing store', 'slim-seo-schema' ),
					'ComputerStore'        => __( 'Computer store', 'slim-seo-schema' ),
					'ConvenienceStore'     => __( 'Convenience store', 'slim-seo-schema' ),
					'DepartmentStore'      => __( 'Department store', 'slim-seo-schema' ),
					'ElectronicsStore'     => __( 'Electronics store', 'slim-seo-schema' ),
					'Florist'              => __( 'Florist', 'slim-seo-schema' ),
					'FurnitureStore'       => __( 'Furniture store', 'slim-seo-schema' ),
					'GardenStore'          => __( 'Garden store', 'slim-seo-schema' ),
					'GroceryStore'         => __( 'Grocery store', 'slim-seo-schema' ),
					'HardwareStore'        => __( 'Hardware store', 'slim-seo-schema' ),
					'HobbyShop'            => __( 'Hobby shop', 'slim-seo-schema' ),
					'HomeGoodsStore'       => __( 'Home goods store', 'slim-seo-schema' ),
					'JewelryStore'         => __( 'Jewelry store', 'slim-seo-schema' ),
					'LiquorStore'          => __( 'Liquor store', 'slim-seo-schema' ),
					'MensClothingStore'    => __( 'Mens clothing store', 'slim-seo-schema' ),
					'MobilePhoneStore'     => __( 'Mobile phone store', 'slim-seo-schema' ),
					'MovieRentalStore'     => __( 'Movie rental store', 'slim-seo-schema' ),
					'MusicStore'           => __( 'Music store', 'slim-seo-schema' ),
					'OfficeEquipmentStore' => __( 'Office equipment store', 'slim-seo-schema' ),
					'OutletStore'          => __( 'Outlet store', 'slim-seo-schema' ),
					'PawnShop'             => __( 'Pawn shop', 'slim-seo-schema' ),
					'PetStore'             => __( 'Pet store', 'slim-seo-schema' ),
					'ShoeStore'            => __( 'Shoe store', 'slim-seo-schema' ),
					'SportingGoodsStore'   => __( 'Sporting goods store', 'slim-seo-schema' ),
					'TireShop'             => __( 'Tire shop', 'slim-seo-schema' ),
					'ToyStore'             => __( 'Toy store', 'slim-seo-schema' ),
					'WholesaleStore'       => __( 'Wholesale store', 'slim-seo-schema' ),
					'Store'                => __( 'Other', 'slim-seo-schema' ),
				],
			],
		],
	],
	[
		'id'        => 'additionalType',
		'label'     => __( 'Additional type', 'slim-seo-schema' ),
		'cloneable' => true,
		'tooltip'   => __( 'An additional type for the item, typically used for adding more specific types from external vocabularies.', 'slim-seo-schema' ),
	],
	Helper::get_property( 'name', [
		'required' => true,
		'tooltip'  => __( 'The name of the organization', 'slim-seo-schema' ),
		'std'      => '{{ site.title }}',
	] ),
	Helper::get_property( 'Person', [
		'id'        => 'alumni',
		'label'     => __( 'Alumni', 'slim-seo-schema' ),
		'tooltip'   => __( 'Alumni of the organization.', 'slim-seo-schema' ),
		'cloneable' => true,
	] ),
	[
		'id'      => 'areaServed',
		'label'   => __( 'Area served', 'slim-seo-schema' ),
		'tooltip' => __( 'The geographic area where a service or offered item is provided.', 'slim-seo-schema' ),
	],
	[
		'id'      => 'award',
		'label'   => __( 'Award', 'slim-seo-schema' ),
		'tooltip' => __( 'An award won by or for this organization.', 'slim-seo-schema' ),
	],
	Helper::get_property( 'address', [ 'required' => true ] ),
	[
		'id'      => 'acceptsReservations',
		'label'   => __( 'Accepts reservations', 'slim-seo-schema' ),
		'tooltip' => __( 'Indicates whether a organization accepts reservations.', 'slim-seo-schema' ),
		'type'    => 'DataList',
		'std'     => 'True',
		'options' => [
			'True'  => __( 'True', 'slim-seo-schema' ),
			'False' => __( 'False', 'slim-seo-schema' ),
		],
	],
	[
		'id'      => 'hasMap',
		'label'   => __( 'Has map', 'slim-seo-schema' ),
		'tooltip' => __( 'A URL to a map of the place.', 'slim-seo-schema' ),
	],
	Helper::get_property( 'aggregateRating' ),
	[
		'id'               => 'contactPoint',
		'label'            => __( 'Contact points', 'slim-seo-schema' ),
		'tooltip'          => __( 'Contact points for a person or organization.', 'slim-seo-schema' ),
		'type'             => 'Group',
		'cloneable'        => true,
		'cloneItemHeading' => __( 'Contact point', 'slim-seo-schema' ),
		'fields'           => [
			[
				'id'       => '@type',
				'std'      => 'ContactPoint',
				'type'     => 'Hidden',
				'required' => true,
			],
			[
				'id'      => 'contactOption',
				'label'   => __( 'Contact option', 'slim-seo-schema' ),
				'tooltip' => __( 'An option available on this contact point.', 'slim-seo-schema' ),
				'type'    => 'DataList',
				'options' => [
					'HearingImpairedSupported' => __( 'Toll-free number', 'slim-seo-schema' ),
					'TollFree'                 => __( 'Support for hearing-impaired callers', 'slim-seo-schema' ),
				],
			],
			[
				'label'   => __( 'Contact type', 'slim-seo-schema' ),
				'id'      => 'contactType',
				'type'    => 'Text',
				'show'    => true,
				'tooltip' => __( 'Used to specify the kind of contact point. For exp. a sales contact point, a PR contact point,...', 'slim-seo-schema' ),
			],
			[
				'id'       => 'email',
				'label'    => __( 'Email', 'slim-seo-schema' ),
				'tooltip'  => __( 'Email address.', 'slim-seo-schema' ),
				'required' => true,
			],
			[
				'id'      => 'telephone',
				'label'   => __( 'Telephone', 'slim-seo-schema' ),
				'tooltip' => __( 'The telephone number.', 'slim-seo-schema' ),
				'show'    => true,
			],
			[
				'id'      => 'faxNumber',
				'label'   => __( 'Fax number', 'slim-seo-schema' ),
				'tooltip' => __( 'The fax number.', 'slim-seo-schema' ),
			],
			Helper::get_property( 'OpeningHoursSpecification', [
				'id'               => 'hoursAvailable',
				'label'            => __( 'Hours available', 'slim-seo-schema' ),
				'tooltip'          => __( 'The hours during which this service or contact is available.', 'slim-seo-schema' ),
				'cloneItemHeading' => __( 'Opening hours', 'slim-seo-schema' ),
				'show'             => true,
			] ),
			[
				'id'      => 'productSupported',
				'label'   => __( 'Product supported', 'slim-seo-schema' ),
				'tooltip' => __( 'The product or service this support contact point is related to.', 'slim-seo-schema' ),
			],
			[
				'id'      => 'areaServed',
				'label'   => __( 'Area served', 'slim-seo-schema' ),
				'tooltip' => __( 'The geographic area where a service or offered item is provided', 'slim-seo-schema' ),
			],
		],
	],
	[
		'id'      => 'currenciesAccepted',
		'label'   => __( 'Currencies accepted', 'slim-seo-schema' ),
		'tooltip' => __( 'The currency accepted. Use standard formats: ISO 4217 currency format e.g. "USD"; Ticker symbol for cryptocurrencies e.g. "BTC"; well known names for Local Exchange Tradings Systems (LETS) and other currency types e.g. "Ithaca HOUR".', 'slim-seo-schema' ),
	],
	[
		'label'            => __( 'Brand', 'slim-seo-schema' ),
		'id'               => 'brand',
		'tooltip'          => __( 'The brand(s) associated with a product or service, or the brand(s) maintained by an organization or business person.', 'slim-seo-schema' ),
		'type'             => 'Group',
		'cloneable'        => true,
		'cloneItemHeading' => __( 'Brand', 'slim-seo-schema' ),
		'fields'           => [
			[
				'id'       => '@type',
				'std'      => 'Brand',
				'type'     => 'Hidden',
				'required' => true,
			],
			[
				'id'       => 'name',
				'label'    => __( 'Name', 'slim-seo-schema' ),
				'std'      => '{{ site.title }}',
				'required' => true,
			],
			[
				'label'   => __( 'Logo', 'slim-seo-schema' ),
				'id'      => 'logo',
				'tooltip' => __( 'Link to logo associated with the brand.', 'slim-seo-schema' ),
				'type'    => 'Image',
				'show'    => true,
			],
			Helper::get_property( 'url', [
				'show'    => true,
				'tooltip' => __( 'The URL of the brand.', 'slim-seo-schema' ),
			] ),
		],
	],
	[
		'id'               => 'department',
		'label'            => __( 'Departments', 'slim-seo-schema' ),
		'cloneable'        => true,
		'cloneItemHeading' => __( 'Department', 'slim-seo-schema' ),
		'tooltip'          => __( 'Departments of the organization', 'slim-seo-schema' ),
		'std'              => [ '{{ schemas.organization }}' ],
	],
	[
		'id'          => 'parentOrganization',
		'label'       => __( 'Parent organization', 'slim-seo-schema' ),
		'tooltip'     => __( 'The larger organization that this organization is a sub organization of, if any.', 'slim-seo-schema' ),
		'description' => __( 'Please create an Organization schema and link to this property via a dynamic variable', 'slim-seo-schema' ),
	],
	[
		'id'      => 'duns',
		'label'   => __( 'Duns', 'slim-seo-schema' ),
		'tooltip' => __( 'The Dun & Bradstreet DUNS number for identifying an organization or business person.', 'slim-seo-schema' ),
	],
	Helper::get_property( 'Person', [
		'id'               => 'employee',
		'label'            => __( 'Employees', 'slim-seo-schema' ),
		'tooltip'          => __( 'Someone working for this organization.', 'slim-seo-schema' ),
		'cloneable'        => true,
		'cloneItemHeading' => __( 'Employee', 'slim-seo-schema' ),
	] ),
	[
		'id'      => 'email',
		'label'   => __( 'Email', 'slim-seo-schema' ),
		'tooltip' => __( 'The email address of the organization', 'slim-seo-schema' ),
		'show'    => true,
	],
	[
		'id'      => 'faxNumber',
		'label'   => __( 'Fax number', 'slim-seo-schema' ),
		'tooltip' => __( 'The fax number of the organization', 'slim-seo-schema' ),
	],
	Helper::get_property( 'Person', [
		'id'               => 'founder',
		'label'            => __( 'Founder', 'slim-seo-schema' ),
		'tooltip'          => __( 'A person who founded this organization.', 'slim-seo-schema' ),
		'cloneable'        => true,
		'cloneItemHeading' => __( 'Employee', 'slim-seo-schema' ),
	] ),
	[
		'id'      => 'foundingDate',
		'label'   => __( 'Founding date', 'slim-seo-schema' ),
		'tooltip' => __( 'The date that this organization was founded.', 'slim-seo-schema' ),
		'type'    => 'Date',
	],
	[
		'id'      => 'foundingLocation',
		'label'   => __( 'Founding location', 'slim-seo-schema' ),
		'type'    => 'Group',
		'tooltip' => __( 'The place where the organization was founded.', 'slim-seo-schema' ),
		'fields'  => [
			[
				'id'       => '@type',
				'std'      => 'Place',
				'type'     => 'Hidden',
				'required' => true,
			],
			Helper::get_property( 'name', [
				'label'    => __( 'Name', 'slim-seo-schema' ),
				'tooltip'  => __( 'The name of the item', 'slim-seo-schema' ),
				'required' => true,
			] ),
			Helper::get_property( 'address', [
				'label'    => '',
				'required' => true,
				'tooltip'  => __( 'The physical address where the organization was founded.', 'slim-seo-schema' ),
			] ),
			[
				'id'      => 'url',
				'label'   => __( 'URL', 'slim-seo-schema' ),
				'tooltip' => __( 'URL of the item', 'slim-seo-schema' ),
				'show'    => true,
			],
		],
	],
	Helper::get_property( 'Person', [
		'id'               => 'funder',
		'label'            => __( 'Funder', 'slim-seo-schema' ),
		'tooltip'          => __( 'A person or organization that supports (sponsors) something through some kind of financial contribution.', 'slim-seo-schema' ),
		'cloneable'        => true,
		'cloneItemHeading' => __( 'Funder', 'slim-seo-schema' ),
	] ),
	Helper::get_property( 'image' ),
	[
		'id'      => 'globalLocationNumber',
		'label'   => __( 'Global location number', 'slim-seo-schema' ),
		'tooltip' => __( 'The Global Location Number (GLN, sometimes also referred to as International Location Number or ILN) of the respective organization, person, or place.', 'slim-seo-schema' ),
	],
	[
		'id'      => 'geo',
		'label'   => __( 'Geolocation', 'slim-seo-schema' ),
		'type'    => 'Group',
		'tooltip' => __( 'Geographic coordinates of the business', 'slim-seo-schema' ),
		'fields'  => [
			[
				'id'       => '@type',
				'std'      => 'GeoCoordinates',
				'type'     => 'Hidden',
				'required' => true,
			],
			[
				'id'      => 'latitude',
				'label'   => __( 'Latitude', 'slim-seo-schema' ),
				'tooltip' => __( 'The latitude of the business location. The precision must be at least 5 decimal places.', 'slim-seo-schema' ),
				'show'    => true,
			],
			[
				'id'      => 'longitude',
				'label'   => __( 'Longitude', 'slim-seo-schema' ),
				'tooltip' => __( 'The longitude of the business location. The precision must be at least 5 decimal places.', 'slim-seo-schema' ),
				'show'    => true,
			],
		],
	],
	[
		'id'               => 'hasPOS',
		'label'            => __( 'Has POS', 'slim-seo-schema' ),
		'type'             => 'Group',
		'tooltip'          => __( 'Points-of-Sales operated by the organization or person.', 'slim-seo-schema' ),
		'cloneable'        => true,
		'cloneItemHeading' => __( 'POS', 'slim-seo-schema' ),
		'fields'           => [
			[
				'id'       => '@type',
				'std'      => 'Place',
				'type'     => 'Hidden',
				'required' => true,
			],
			Helper::get_property( 'name', [
				'label'    => __( 'Name', 'slim-seo-schema' ),
				'tooltip'  => __( 'The name of the item.', 'slim-seo-schema' ),
				'required' => true,
			] ),
			Helper::get_property( 'address', [
				'label'    => '',
				'required' => true,
				'tooltip'  => __( 'The physical address where students go to take the program.', 'slim-seo-schema' ),
			] ),
			[
				'id'      => 'url',
				'label'   => __( 'URL', 'slim-seo-schema' ),
				'tooltip' => __( 'URL of the item.', 'slim-seo-schema' ),
			],
		],
	],
	[
		'label'   => __( 'ISIC V4', 'slim-seo-schema' ),
		'id'      => 'isicV4',
		'tooltip' => __( 'The International Standard of Industrial Classification of All Economic Activities (ISIC), Revision 4 code for a particular organization, business person, or place.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'ISO 6523 Code', 'slim-seo-schema' ),
		'id'      => 'iso6523Code',
		'tooltip' => __( 'An organization identifier as defined in ISO 6523(-1).', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Keywords', 'slim-seo-schema' ),
		'id'      => 'keywords',
		'tooltip' => __( 'Keywords or tags used to describe some item. Multiple textual entries in a keywords list are typically delimited by commas.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Knows language', 'slim-seo-schema' ),
		'id'      => 'knowsLanguage',
		'tooltip' => __( 'Indicate a known language. Use language codes from the IETF BCP 47 standard.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Legal name', 'slim-seo-schema' ),
		'id'      => 'legalName',
		'tooltip' => __( 'The official name of the organization, e.g. the registered company name.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Lei Code', 'slim-seo-schema' ),
		'id'      => 'leiCode',
		'tooltip' => __( 'An organization identifier that uniquely identifies a legal entity as defined in ISO 17442.', 'slim-seo-schema' ),
	],
	[
		'id'               => 'location',
		'label'            => __( 'Location', 'slim-seo-schema' ),
		'tooltip'          => __( 'The location of, for example, where an event is happening, where an organization is located, or where an action takes place.', 'slim-seo-schema' ),
		'type'             => 'Group',
		'cloneable'        => true,
		'cloneItemHeading' => __( 'Location', 'slim-seo-schema' ),
		'fields'           => [
			[
				'id'        => '@type',
				'label'     => __( 'Type', 'slim-seo-schema' ),
				'type'      => 'DataList',
				'std'       => 'Place',
				'required'  => true,
				'dependant' => true,
				'options'   => [
					'Place'           => __( 'Physical location', 'slim-seo-schema' ),
					'VirtualLocation' => __( 'Online', 'slim-seo-schema' ),
				],
			],
			[
				'label'      => __( 'Name', 'slim-seo-schema' ),
				'id'         => 'name',
				'required'   => true,
				'dependency' => '[@type]:Place',
			],
			Helper::get_property( 'address', [
				'label'      => '',
				'dependency' => '[@type]:Place',
				'show'       => true,
			] ),
			[
				'id'         => 'url',
				'label'      => __( 'URL', 'slim-seo-schema' ),
				'required'   => true,
				'dependency' => '[@type]:VirtualLocation',
			],
		],
	],
	[
		'id'      => 'logo',
		'label'   => __( 'Logo URL', 'slim-seo-schema' ),
		'show'    => true,
		'tooltip' => __( 'The URL of the logo of the organization.', 'slim-seo-schema' ),
		'type'    => 'Image',
		'std'     => '{{ site.icon }}',
	],
	[
		'id'      => 'menu',
		'label'   => __( 'Menu', 'slim-seo-schema' ),
		'tooltip' => __( 'For food establishments, the fully-qualified URL of the menu', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'NAICS', 'slim-seo-schema' ),
		'id'      => 'naics',
		'tooltip' => __( 'The North American Industry Classification System (NAICS) code for a particular organization or business person.', 'slim-seo-schema' ),
	],
	[
		'id'      => 'numberOfEmployees',
		'label'   => __( 'Number of employees', 'slim-seo-schema' ),
		'type'    => 'Group',
		'tooltip' => __( 'The number of employees in an organization e.g. business.', 'slim-seo-schema' ),
		'fields'  => [
			[
				'id'       => '@type',
				'std'      => 'QuantitativeValue',
				'type'     => 'Hidden',
				'required' => true,
			],
			[
				'id'   => 'value',
				'show' => true,
			],
		],
	],
	Helper::get_property( 'OpeningHoursSpecification', [
		'id'               => 'openingHoursSpecification',
		'label'            => __( 'Opening hours', 'slim-seo-schema' ),
		'tooltip'          => __( 'Hours during which the business location is open', 'slim-seo-schema' ),
		'cloneItemHeading' => __( 'Opening hours', 'slim-seo-schema' ),
		'show'             => true,
	] ),
	[
		'label'            => __( 'Owns', 'slim-seo-schema' ),
		'id'               => 'owns',
		'tooltip'          => __( 'Products owned by the organization or person.', 'slim-seo-schema' ),
		'cloneable'        => true,
		'cloneItemHeading' => __( 'Product', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Payment accepted', 'slim-seo-schema' ),
		'id'      => 'paymentAccepted',
		'tooltip' => __( 'Cash, Credit Card, Cryptocurrency, Local Exchange Tradings System, etc.', 'slim-seo-schema' ),
	],
	[
		'id'      => 'priceRange',
		'label'   => __( 'Price range', 'slim-seo-schema' ),
		'tooltip' => __( 'The relative price range of a business, commonly specified by either a numerical range (for example, "$10-15") or a normalized number of currency signs (for example, "$$$"). This field must be shorter than 100 characters. If it\'s longer than 100 characters, Google won\'t show a price range for the business.', 'slim-seo-schema' ),
	],
	Helper::get_property( 'Review' ),
	[
		'id'      => 'servesCuisine',
		'label'   => __( 'Serves cuisine', 'slim-seo-schema' ),
		'tooltip' => __( 'The type of cuisine the restaurant serves', 'slim-seo-schema' ),
	],
	[
		'id'        => 'sameAs',
		'label'     => __( 'Same as', 'slim-seo-schema' ),
		'tooltip'   => __( 'URL of a reference Web page that unambiguously indicates the item\'s identity. E.g. the URL of the item\'s Wikipedia page, Wikidata entry, social media profiles or official website.', 'slim-seo-schema' ),
		'show'      => true,
		'cloneable' => true,
	],
	[
		'label'   => __( 'Slogan', 'slim-seo-schema' ),
		'id'      => 'slogan',
		'show'    => true,
		'tooltip' => __( 'A slogan or motto associated with the organization or person.', 'slim-seo-schema' ),
	],
	Helper::get_property( 'Person', [
		'id'               => 'sponsor',
		'label'            => __( 'Sponsor', 'slim-seo-schema' ),
		'tooltip'          => __( 'A person or organization that supports a thing through a pledge, promise, or financial contribution. e.g. a sponsor of a Medical Study or a corporate sponsor of an event.', 'slim-seo-schema' ),
		'cloneable'        => true,
		'cloneItemHeading' => __( 'Sponsor', 'slim-seo-schema' ),
	] ),
	[
		'label'   => __( 'Tax ID', 'slim-seo-schema' ),
		'id'      => 'taxID',
		'tooltip' => __( 'The Tax / Fiscal ID of the organization or person, for exp. the TIN in the US or the CIF/NIF in Spain.', 'slim-seo-schema' ),
	],
	[
		'id'      => 'telephone',
		'label'   => __( 'Telephone', 'slim-seo-schema' ),
		'tooltip' => __( 'The telephone number of the organization. Be sure to include the country code and area code in the phone number.', 'slim-seo-schema' ),
		'show'    => true,
	],
	Helper::get_property( 'url', [
		'tooltip' => __( 'The fully-qualified URL of the specific business location. The URL must be a working link.', 'slim-seo-schema' ),
		'std'     => '{{ site.url }} ',
		'show'    => true,
	] ),
	[
		'label'   => __( 'Vat ID', 'slim-seo-schema' ),
		'id'      => 'vatID',
		'tooltip' => __( 'The Value-added Tax ID of the organization', 'slim-seo-schema' ),
	],
];
