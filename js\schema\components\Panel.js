const { useState, useReducer } = wp.element;
import clsx from 'clsx';
import DeleteButton from './Button/DeleteButton';
import Toggle from './Button/ToggleButton';

export function Panel( { title, defaultExpanded = false, className, children, required, id, deleteProp } ) {
	const [ expanded, toggle ] = useReducer( value => !value, defaultExpanded );
	const panelClass = clsx( 'sss-panel', className, {
		'sss-panel--expanded': expanded,
	} );
	const toggleClass = `dashicons-${ expanded ? 'arrow-up-alt2' : 'arrow-down-alt2' }`;

	return !title
		? children
		: <div className={ panelClass }>
			{
				title && <div className='sss-panel__header' onClick={ toggle }>
					<div className="sss-panel__title">{ title }</div>
					<div className="sss-panel__actions">
						{ !required && <DeleteButton deleteProp={ deleteProp } id={ id } type="panel" /> }
						<Toggle className={ toggleClass } />
					</div>
				</div>
			}
			<div className="sss-panel__body">
				{ children }
			</div>
		</div>;
}

