import { Dashicon } from "@wordpress/components";
import { __ } from "@wordpress/i18n";

export default function DeleteButton( { type = 'field', id, deleteProp } ) {
	const handleDelete = e => {
		const confirmDelete = confirm( __( 'Are you sure you want to delete?' ) );
		if ( !confirmDelete ) {
			return;
		}
		e.stopPropagation();
		deleteProp( id );
	};

	return <Dashicon
		className={ `sss-action sss-action--delete--${ type }` }
		icon={ type === 'field' ? 'dismiss' : 'trash' }
		onClick={ handleDelete }
	/>;
}
