import { Control } from "@elightup/form";
import { Button } from "@wordpress/components";
import { memo, useRef } from "@wordpress/element";
import DeleteButton from '../Button/DeleteButton';
import PropInserter from "./PropInserter";

const Image = ( { className, property, deleteProp } ) => {
	const inputRef = useRef();
	const { id, label, required, std, tooltip } = property;

	const openMediaPopup = e => {
		e.preventDefault();

		let frame = wp.media( {
			multiple: false,
			title: SSSchema.mediaPopupTitle
		} );

		frame.open();
		frame.off( 'select' );

		frame.on( 'select', () => {
			const url = frame.state().get( 'selection' ).first().toJSON().url;
			inputRef.current.value += url;
		} );
	};

	return (
		<Control className={ className } label={ label } id={ id } required={ required } tooltip={ tooltip }>
			<div className="sss-input-wrapper">
				<input type="text" id={ id } name={ id } defaultValue={ std } ref={ inputRef } />
				<Button icon="format-image" onClick={ openMediaPopup } className="sss-insert-image" />
				<PropInserter inputRef={ inputRef } />
			</div>
			{ required || <DeleteButton id={ id } deleteProp={ deleteProp } /> }
		</Control>
	);
};

export default memo( Image, ( prevProps, nextProps ) => prevProps.property.id === nextProps.property.id );
