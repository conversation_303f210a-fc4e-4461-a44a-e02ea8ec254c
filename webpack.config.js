const path = require( 'path' );
const webpack = require( 'webpack' );
const TerserPlugin = require( 'terser-webpack-plugin' );

const commonModules = {
	rules: [
		{
			test: /\.js/,
			exclude: /node_modules/,
			use: {
				loader: 'babel-loader',
				options: {
					plugins: [ '@babel/plugin-transform-react-jsx' ],
				},
			},
		},
		{
			test: /\.css/,
			use: [ 'style-loader', 'css-loader' ],
		},
	],
};

const externals = {
	react: 'React',
	'react-dom': 'ReactDOM',
	'@wordpress/i18n': 'wp.i18n',
	'@wordpress/element': 'wp.element',
	'@wordpress/components': 'wp.components',
	'@wordpress/hooks': 'wp.hooks',
	'jquery': 'jQuery',
};

const plugins = [
	new webpack.optimize.LimitChunkCountPlugin( {
		maxChunks: 1,
	} ),
];

const optimization = {
	minimize: true,
	minimizer: [
		new TerserPlugin( {
			extractComments: false,
			terserOptions: {
				format: {
					comments: false,
				},
			},
		} ),
	],
};

const schemas = {
	entry: './js/schema/App.js',
	output: {
		filename: 'schema.js',
		path: path.resolve( __dirname, './js/' ),
	},
	externals,
	plugins,
	optimization,
	module: commonModules,
};

const post = {
	entry: './js/post/App.js',
	output: {
		filename: 'post.js',
		path: path.resolve( __dirname, './js/' ),
	},
	externals,
	plugins,
	optimization,
	module: commonModules,
};

module.exports = [ schemas, post ];
