import { Dropdown } from "@wordpress/components";
import { RawHTML, useContext, useEffect, useState } from "@wordpress/element";
import { __ } from "@wordpress/i18n";
import { useImmer } from "use-immer";
import { SchemaLinkContext } from "../contexts/SchemaLinkContext";
import { request, uniqueID } from "../functions";
import Inserter from "./Inserter";
import Schema from "./Schema";

const Schemas = () => {
	const [ items, setItems ] = useState( [] );
	const [ schemas, setSchemas ] = useImmer( {} );
	const { addSchemaLink, removeSchemaLink } = useContext( SchemaLinkContext );

	useEffect( () => {
		request( 'data', { type: 'schemas' } ).then( setItems );
	}, [] );
	useEffect( () => {
		request( 'schemas' ).then( data => data && setSchemas( data ) );
	}, [] );

	const addSchema = ( e, onToggle ) => setSchemas( draft => {
		onToggle();
		const type = e.target.dataset.value,
			label = e.target.childNodes[ 0 ].nodeValue,
			id = uniqueID(),
			newSchema = { type, fields: { _label: label } };

		draft[ id ] = newSchema;
		addSchemaLink( id, newSchema );
	} );

	const deleteSchema = id => setSchemas( draft => {
		delete draft[ id ];
		removeSchemaLink( id );
	} );

	return (
		<>
			{
				Object.keys( schemas ).length > 0
					? Object.entries( schemas ).map( ( [ id, schema ] ) => <Schema schema={ schema } deleteProp={ deleteSchema } id={ id } key={ id } /> )
					: <RawHTML className="sss-none">{ __( 'There are no schemas here. Click the <strong>+ Add Schema</strong> to add a new schema.', 'slim-seo-schema' ) }</RawHTML>
			}
			<div className="sss-schema-actions">
				<button type="submit" name="submit" className="button-primary" value="1">{ __( 'Save Changes', 'slim-seo-schema' ) }</button>
				<Dropdown
					className="sss-dropdown"
					position="bottom right"
					renderToggle={ ( { onToggle } ) => (
						<button type="button" className="button-secondary" onClick={ onToggle }>{ __( '+ Add Schema', 'slim-seo-schema' ) }</button>
					) }
					renderContent={ ( { onToggle } ) => (
						<Inserter items={ items } group={ true } hasSearch={ true } onSelect={ e => addSchema( e, onToggle ) } />
					) }
				/>
			</div>
		</>
	);
};

export default Schemas;