.sss-field--grouplocation {
	align-items: flex-start;
}
.sss-input--location {
	display: block;
}

.sss-location-group {
	margin-bottom: 12px;
}

.sss-location-group__and {
	font-weight: 600;
	text-transform: uppercase;
	margin-top: 12px;
}

.sss-location-rule {
	display: flex;
	justify-content: space-between;
	margin-bottom: 6px;
	grid-gap: 6px;
}

.sss-location-rule__name,
.sss-location-rule__value {
	flex: 1;
}

.sss-location-rule__add {
	text-transform: uppercase;
}

.sss-location-rule__remove {
	padding: 0;
	border: 0;
	background: none;
	cursor: pointer;
	color: #ccc;
	transition: color 0.25s;
	display: inline-flex;
	align-items: center;
}

.sss-location-rule__remove:hover {
	color: #dc3232;
}

.sss-location-rule__remove svg {
	fill: currentColor;
}
