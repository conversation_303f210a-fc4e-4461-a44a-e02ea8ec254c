name: Deploy to production site.
on:
  workflow_dispatch:
    branch:
      - master
  push:
    tags:
      - "*"
env:
  name: slim-seo-schema
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Install SSH key
        uses: shimataro/ssh-key-action@v2
        with:
          key: ${{ secrets.SSH_KEY }}
          known_hosts: ${{ secrets.SSH_KNOWN_HOSTS }}
      - name: Checkout
        uses: actions/checkout@v2
      - name: Install PHP dependencies
        run: composer install
      - name: Copying files
        run: |
          mkdir -p $name
          rm -rf $name/*
          echo $name >> "./.distignore"
          rsync -rc --exclude-from="./.distignore" . $name
      - name: Generating zip file, upload and notify Slack
        run: zip -r $name.zip "./$name"
      - name: Upload file to server
        run: rsync -e "ssh -o StrictHostKeyChecking=no" $name.zip ${{ secrets.SSH_USERNAME }}@${{ secrets.SERVER_IP }}:${{ secrets.DOWNLOADS_DIR }}
      - name: Notify Slack
        run: |
          tag="${GITHUB_REF#refs/tags/}"
          tag="${tag#v}"
          curl -X POST --data-urlencode "payload={\"channel\": \"#slim-seo\", \"username\": \"Bot\", \"text\": \"New version $tag for $name has been just released!\", \"icon_emoji\": \":tada:\"}" ${{ secrets.SLACK_WEBHOOK }}

