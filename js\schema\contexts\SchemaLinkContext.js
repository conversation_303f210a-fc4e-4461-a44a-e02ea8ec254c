import { createContext, useEffect } from "@wordpress/element";
import dotProp from 'dot-prop';
import { useImmer } from "use-immer";
import { request } from "../functions";

export const SchemaLinkContext = createContext( {} );

export const SchemaLinkProvider = ( { children } ) => {
	const [ schemaLinks, setSchemaLinks ] = useImmer( {} );

	useEffect( () => {
		request( 'schemas' ).then( data => {
			if ( !data ) {
				return;
			}
			setSchemaLinks( draft => {
				Object.entries( data ).forEach( ( [ id, schema ] ) => {
					draft[ id ] = dotProp.get( schema, 'fields._label', schema.type );
				} );
			} );
		} );
	}, [] );

	const addSchemaLink = ( id, schema ) => setSchemaLinks( draft => {
		draft[ id ] = dotProp.get( schema, 'fields._label', schema.type );
	} );

	const updateSchemaLinkLabel = ( id, label ) => setSchemaLinks( draft => {
		draft[ id ] = label;
	} );

	const removeSchemaLink = id => setSchemaLinks( draft => {
		delete draft[ id ];
	} );

	return (
		<SchemaLinkContext.Provider value={ { schemaLinks, addSchemaLink, removeSchemaLink, updateSchemaLinkLabel } }>
			{ children }
		</SchemaLinkContext.Provider>
	);
};