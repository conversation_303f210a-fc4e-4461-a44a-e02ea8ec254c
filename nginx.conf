user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
  worker_connections 1024;
}

http {
  include /etc/nginx/mime.types;

  # Enable Gzip compression for text-based content
  gzip on;
  gzip_types text/plain text/css application/json application/javascript text/xml application/xml+rss application/atom+xml image/svg+xml;
  gzip_proxied any;
  gzip_min_length 1024;

  server {
      listen 80;
      server_name localhost;

      location / {
          root /usr/share/nginx/html;
          # redirecting any non-existent route to index.html.
          try_files $uri $uri/ /index.html;

          # Set cache headers for static files
          location ~* \.(?:js|css|woff2?|ttf|otf|eot|ico|gif|jpe?g|png|svg|webp)$ {
              # Set long cache headers for assets, with immutable flag for cache-busting files
              add_header Cache-Control "public, max-age=31536000, immutable";
          }

          # Specific shorter caching for HTML to ensure freshness
          location ~* \.html$ {
              add_header Cache-Control "public, max-age=3600";
          }
      }
  }
}
