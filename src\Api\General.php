<?php
namespace SlimSEOPro\Schema\Api;

use SlimSEOPro\Schema\Settings;
use WP_REST_Server;

class General extends Base {
	public function register_routes() {
		register_rest_route( 'slim-seo-schema', 'schemas', [
			'methods'             => WP_REST_Server::READABLE,
			'callback'            => [ $this, 'get_schemas' ],
			'permission_callback' => [ $this, 'has_permission' ],
		] );
	}

	public function get_schemas(): array {
		return Settings::get_schemas();
	}
}
