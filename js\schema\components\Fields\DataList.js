import { Control } from "@elightup/form";
import { memo, useEffect, useRef } from "@wordpress/element";
import DeleteButton from "../Button/DeleteButton";
import PropInserter from "./PropInserter";

const DataList = ( { className, property, setProperties, deleteProp } ) => {
	const inputRef = useRef();
	const inputDataListRef = useRef();
	const { id, label, required, std, tooltip, options } = property;
	const listId = id.replace( '[fields]', '' );

	const items = Array.isArray( options )
		? options
		: Object.entries( options ).reduce( ( prev, [ value, label ] ) => [ ...prev, { value, label } ], [] );

	const setFields = value => {
		if ( !property.dependant ) {
			return;
		}
		setProperties( prev => prev.map( item => {
			if ( !item.dependency ) {
				return item;
			}
			const dependency = item.dependency.split( ':' )[ 1 ];
			const hidden = dependency !== value;

			return { ...item, hidden };
		} ) );
	};

	useEffect( () => {
		setFields( std );
	}, [] );

	const updateValue = e => {
		inputRef.current.value = items.reduce( ( prev, { value, label } ) => prev.replace( label, value ), e.target.value );
		setFields( inputRef.current.value );
	};

	return (
		<Control className={ className } label={ label } id={ id } required={ required } tooltip={ tooltip }>
			<input type="hidden" ref={ inputRef } name={ id } defaultValue={ std } />

			<div className="sss-input-wrapper">
				<input
					type="text"
					list={ listId }
					ref={ inputDataListRef }
					onChange={ updateValue }
					defaultValue={ items.reduce( ( prev, { value, label } ) => String( prev ).replace( value, label ), std ) }
				/>
				<PropInserter inputRef={ inputRef } extraInputRef={ inputDataListRef } />
			</div>

			<datalist id={ listId }>
				{ items.map( item => <option key={ item.value }>{ item.label }</option> ) }
			</datalist>

			{ required || <DeleteButton id={ id } deleteProp={ deleteProp } /> }
		</Control>
	);
};

export default memo( DataList, ( prevProps, nextProps ) => prevProps.property.id === nextProps.property.id );