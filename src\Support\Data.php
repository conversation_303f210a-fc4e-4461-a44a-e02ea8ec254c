<?php
namespace SlimSEOPro\Schema\Support;

class Data {
	private static $schemas = [];

	public static function get_schema_specs( $type ) {
		// Fix previous wrong name.
		$type = $type === 'Video' ? 'VideoObject' : $type;

		if ( empty( self::$schemas[ $type ] ) ) {
			self::$schemas[ $type ] = require_once dirname( __DIR__ ) . "/SchemaTypes/$type.php";
		}

		return self::$schemas[ $type ];
	}

	public static function get_post_types() {
		$post_types = get_post_types( [ 'public' => true ], 'objects' );
		unset( $post_types['attachment'] );
		$post_types = array_map( function( $post_type ) {
			return [
				'slug' => $post_type->name,
				'name' => $post_type->labels->singular_name,
			];
		}, $post_types );

		return array_values( $post_types );
	}

	public static function get_taxonomies() {
		$unsupported = [
			'wp_theme',
			'wp_template_part_area',
			'link_category',
			'nav_menu',
			'post_format',
			'mb-views-category',
		];
		$taxonomies  = get_taxonomies( [], 'objects' );
		$taxonomies  = array_diff_key( $taxonomies, array_flip( $unsupported ) );
		$taxonomies  = array_map( function( $taxonomy ) {
			return [
				'slug' => $taxonomy->name,
				'name' => $taxonomy->label,
			];
		}, $taxonomies );

		return array_values( $taxonomies );
	}
}
