import { render, useEffect } from "@wordpress/element";
import Schemas from "./components/Schemas";
import { SchemaLinkProvider } from "./contexts/SchemaLinkContext";

const App = () => {
	useEffect( () => {
		// Don't submit form when press Enter.
		jQuery( '.wrap' ).on( 'keypress keydown keyup', 'input', function( e ) {
			if ( e.keyCode == 13 ) {
				e.preventDefault();
			}
		} );
	}, [] );

	return (
		<SchemaLinkProvider>
			<Schemas />
		</SchemaLinkProvider>
	);
};

render( <App />, document.getElementById( 'schema' ) );
