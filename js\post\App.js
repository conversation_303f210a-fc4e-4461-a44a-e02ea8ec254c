import { render, useState } from "@wordpress/element";
import SchemaType from "../schema/components/SchemaType";
import { SchemaLinkProvider } from "../schema/contexts/SchemaLinkContext";
import Type from "./Type";

const App = () => {
	const [ schema, setSchema ] = useState( SSSchema.schema );
	const onChange = e => setSchema( { type: e.target.value, fields: {} } );

	return (
		<SchemaLinkProvider>
			<Type value={ schema.type } onChange={ onChange } />
			{
				schema.type && <SchemaType schemaID="post" schema={ schema } />
			}
		</SchemaLinkProvider>
	);
};

render( <App />, document.getElementById( 'sss-post' ) );
