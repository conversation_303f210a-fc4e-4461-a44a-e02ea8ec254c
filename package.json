{"scripts": {"watch:css": "sass -s compressed -w --no-source-map css/scss/schema.scss css/schema.css", "watch:js": "webpack --watch --mode development", "build:css": "sass -s compressed --no-source-map css/scss/schema.scss css/schema.css", "build:js": "webpack --mode production", "build": "npm run build:css && npm run build:js", "esbuild": "node esbuild", "start": "webpack --mode development"}, "devDependencies": {"@babel/core": "^7.11.6", "@babel/preset-env": "^7.18.2", "@babel/preset-react": "^7.17.12", "babel-loader": "^8.1.0", "esbuild": "^0.15.10", "esbuild-plugin-external-global": "^1.0.1", "sass": "^1.51.0"}, "dependencies": {"@elightup/form": "file:../../../../form", "@wordpress/components": "^19.12.0", "@wordpress/element": "^4.8.0", "@wordpress/i18n": "^4.10.0", "clsx": "^1.1.1", "dot-prop": "^6.0.1", "immer": "^9.0.12", "react-select": "^5.2.2", "react-tabs": "^3.2.1", "slugify": "^1.6.5", "use-immer": "^0.7.0", "webpack": "^5.74.0", "webpack-cli": "^4.10.0"}, "babel": {"presets": ["@babel/preset-env", "@babel/preset-react"]}}