{"name": "react-schema-generator", "version": "1.0.0", "description": "Standalone React application for Schema.org structured data generation", "type": "module", "scripts": {"dev": "vite --host", "dev:check": "tsc && vite --host", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --fix", "format": "prettier --write src/**/*.{js,jsx,ts,tsx,css} --config ./.prettierrc", "test": "vitest", "test:ui": "vitest --ui", "prepare": "husky"}, "keywords": ["react", "schema", "structured-data", "seo", "json-ld"], "author": "Schema Generator Team", "license": "MIT", "dependencies": {"@hookform/resolvers": "^3.10.0", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.3", "@radix-ui/react-dialog": "^1.1.3", "@radix-ui/react-dropdown-menu": "^2.1.3", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-select": "^2.1.3", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "immer": "^10.1.1", "lucide-react": "^0.539.0", "nanoid": "^5.0.8", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.0", "react-select": "^5.8.2", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "use-immer": "^0.11.0", "zod": "^3.25.76", "zustand": "^5.0.2"}, "devDependencies": {"@eslint/js": "^9.33.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "@types/node": "^24.2.1", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@typescript-eslint/eslint-plugin": "^8.19.0", "@typescript-eslint/parser": "^8.19.0", "@vitejs/plugin-react": "^5.0.0", "autoprefixer": "^10.4.20", "eslint": "^9.17.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^16.3.0", "jsdom": "^26.1.0", "lint-staged": "^16.1.5", "postcss": "^8.5.1", "prettier": "^3.6.2", "tailwindcss": "^3.4.17", "typescript": "^5.7.2", "vite": "^7.1.2", "vitest": "^3.2.4"}}