import { memo, Suspense } from "@wordpress/element";
import { __ } from "@wordpress/i18n";
import clsx from 'clsx';
import { getFieldType } from "../../functions";

const Field = ( { property, deleteProp, setProperties } ) => {
	const { type = 'Text', dependency, parentID } = property;
	const FieldType = getFieldType( type );
	const className = clsx( 'sss-field', `sss-field--${ type.toLowerCase() }`, dependency && `dep:${ parentID }${ dependency }` );
	return (
		<Suspense fallback={ __( 'Loading fields... Please wait', 'slim-seo-schema' ) } >
			<FieldType className={ className } property={ property } deleteProp={ deleteProp } setProperties={ setProperties } />
		</Suspense>
	);
};

export default memo( Field, ( prev, next ) => prev.property.id === next.property.id && prev.property.type === next.property.type );
