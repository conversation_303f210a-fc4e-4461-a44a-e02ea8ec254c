<?php
namespace SlimSEOPro\Schema\Integrations\ACF;

class ACF {
	private $variables;

	public function __construct() {
		add_action( 'init', [ $this, 'init' ] );
	}

	public function init() {
		if ( ! class_exists( 'ACF' ) ) {
			return;
		}

		add_filter( 'slim_seo_schema_variables', [ $this, 'add_variables' ] );
		add_filter( 'slim_seo_schema_data', [ $this, 'add_data' ] );
	}

	public function add_variables( $variables ) {
		$this->variables = $variables;

		$field_groups = acf_get_field_groups();
		array_walk( $field_groups, [ $this, 'add_group' ] );

		return $this->variables;
	}

	public function add_data( array $data ) {
		$post          = is_singular() ? get_queried_object() : get_post();
		$field_objects = get_field_objects( $post->ID );

		// Add more fields from option page
		if ( function_exists( 'acf_get_options_pages' ) && ! empty( acf_get_options_pages() ) ) {
			$field_objects = array_merge( get_field_objects( 'option' ), $field_objects );
		}

		if ( ! empty( $field_objects ) ) {
			$data['acf'] = new Renderer( $field_objects );
		}

		return $data;
	}

	private function add_group( array $field_group ) {
		$this->variables[] = [
			// Translators: %s - field group title.
			'label'   => sprintf( __( '[ACF] %s', 'slim-seo-schema' ), $field_group['title'] ),
			'options' => $this->add_fields( acf_get_fields( $field_group['key'] ), 'acf' ),
		];
	}

	private function add_fields( array $fields, string $base_id = '', string $indent = '' ) {
		$options    = [];
		$fields     = array_filter( $fields, [ $this, 'has_value' ] );
		$sub_indent = $indent . str_repeat( '&nbsp;', 5 );

		foreach ( $fields as $field ) {
			$field_name     = $field['name'];
			$id             = "$base_id.{$field_name}";
			$label          = $field['label'];
			$options[ $id ] = "{$indent}{$label}";

			if ( ! empty( $field['sub_fields'] ) ) {
				$options = array_merge( $options, $this->add_fields( $field['sub_fields'], $id, $sub_indent ) );
			}

			if ( ! empty( $field['layouts'] ) ) {
				$options = array_merge( $options, $this->add_fields( $field['layouts'], $id, $sub_indent ) );
			}
		}

		return $options;
	}

	private function has_value( array $field ) {
		return ! in_array( $field['type'] ?? '', [ 'google_map', 'message', 'accordion', 'tab' ], true );
	}
}
