import { Control } from "@elightup/form";
import { memo, useRef } from "@wordpress/element";
import DeleteButton from '../Button/DeleteButton';
import PropInserter from "./PropInserter";

const Textarea = ( { className, property, deleteProp } ) => {
	const inputRef = useRef();
	const { id, label, required, std, tooltip, hasInsert = true, rows = 2 } = property;

	return (
		<Control className={ className } label={ label } id={ id } required={ required } tooltip={ tooltip }>
			<div className="sss-input-wrapper">
				<textarea ref={ inputRef } defaultValue={ std } id={ id } name={ id } rows={ rows } />
				{ hasInsert && <PropInserter inputRef={ inputRef } /> }
			</div>
			{ required || <DeleteButton id={ id } deleteProp={ deleteProp } /> }
		</Control>
	);
};

export default memo( Textarea, ( prevProps, nextProps ) => prevProps.property.id === nextProps.property.id );
