<?php
namespace SlimSEOPro\Schema\SchemaTypes;

return [
	[
		'id'   => 'googleDocs',
		'type' => 'GoogleDocs',
		'url'  => 'https://developers.google.com/search/docs/advanced/structured-data/job-training',
		'show' => true,
	],
	Helper::get_property( 'name', [
		'required' => true,
		'tooltip'  => __( 'The name of the program.', 'slim-seo-schema' ),
	] ),
	[
		'id'       => 'applicationDeadline',
		'label'    => __( 'Application deadline', 'slim-seo-schema' ),
		'required' => true,
		'tooltip'  => __( 'The date at which the program will stop accepting applications in ISO-8601 format. If applications are rolling, specify the latest possible date the program will accept an application. If the program is offered more than once per calendar year, specify multiple dates in a list.', 'slim-seo-schema' ),
		'type'     => 'Date',
	],
	[
		'id'      => 'applicationStartDate',
		'label'   => __( 'Application start date', 'slim-seo-schema' ),
		'tooltip' => __( 'The date when the program begins collecting applications in ISO-8601 format. If applications are rolling, specify the property for each date that applications for a term begin. If applications are accepted multiple times in a year, specify the earliest possible date.', 'slim-seo-schema' ),
		'type'    => 'Date',
	],
	[
		'id'      => 'dayOfWeek',
		'label'   => __( 'Day of week', 'slim-seo-schema' ),
		'tooltip' => __( 'The day of the week when the program is offered.', 'slim-seo-schema' ),
		'type'    => 'DataList',
		'options' => [
			'https://schema.org/Monday'    => __( 'Monday', 'slim-seo-schema' ),
			'https://schema.org/Tuesday'   => __( 'Tuesday', 'slim-seo-schema' ),
			'https://schema.org/Wednesday' => __( 'Wednesday', 'slim-seo-schema' ),
			'https://schema.org/Thursday'  => __( 'Thursday', 'slim-seo-schema' ),
			'https://schema.org/Friday'    => __( 'Friday', 'slim-seo-schema' ),
			'https://schema.org/Saturday'  => __( 'Saturday', 'slim-seo-schema' ),
			'https://schema.org/Sunday'    => __( 'Sunday', 'slim-seo-schema' ),
		],
	],
	Helper::get_property( 'description', [
		'tooltip' => __( 'The description of the program.', 'slim-seo-schema' ),
	] ),
	[
		'id'      => 'endDate',
		'label'   => __( 'End Date', 'slim-seo-schema' ),
		'tooltip' => __( 'The date when the term officially ends, where students are no longer required to show up for classes or exams, in ISO-8601 format. If there are multiple end dates, specify the property for each date that the program could end.', 'slim-seo-schema' ),
		'type'    => 'Date',
	],
	[
		'id'      => 'educationalProgramMode',
		'label'   => __( 'Educational program mode', 'slim-seo-schema' ),
		'type'    => 'DataList',
		'std'     => 'IN_PERSON',
		'tooltip' => __( 'The format in which course content is conveyed, and whether the student can be in person or remote.', 'slim-seo-schema' ),
		'options' => [
			'IN_PERSON' => __( 'In Person', 'slim-seo-schema' ),
			'ONLINE'    => __( 'Online', 'slim-seo-schema' ),
			'HYBRID'    => __( 'Hybrid', 'slim-seo-schema' ),
		],
	],
	[
		'id'      => 'financialAidEligible',
		'label'   => __( 'Financial aid eligible', 'slim-seo-schema' ),
		'type'    => 'DataList',
		'std'     => 'PUBLIC_AID',
		'tooltip' => __( 'The different types of aid for which the program is eligible.', 'slim-seo-schema' ),
		'options' => [
			'PUBLIC_AID'   => __( 'Public Aid', 'slim-seo-schema' ),
			'PROVIDER_AID' => __( 'Provider Aid', 'slim-seo-schema' ),
		],
	],
	[
		'id'        => 'identifier',
		'label'     => __( 'Identifier', 'slim-seo-schema' ),
		'tooltip'   => __( 'The type of identifier.', 'slim-seo-schema' ),
		'type'      => 'Group',
		'cloneable' => true,
		'required'  => true,
		'fields'    => [
			[
				'id'       => '@type',
				'std'      => 'PropertyValue',
				'type'     => 'Hidden',
				'required' => true,
			],
			[
				'id'       => 'propertyID',
				'label'    => __( 'Property ID', 'slim-seo-schema' ),
				'type'     => 'DataList',
				'required' => true,
				'std'      => 'CIP2010',
				'tooltip'  => __( 'CIP2010: the 6 digit CIP code, as published in the 2010 specification from the US Department of Education. ProgramID: a unique program identifier, as used by the institution (if applicable).', 'slim-seo-schema' ),
				'options'  => [
					'CIP2010'   => __( 'CIP2010', 'slim-seo-schema' ),
					'ProgramID' => __( 'ProgramID', 'slim-seo-schema' ),
				],
			],
			[
				'id'       => 'value',
				'label'    => __( 'Value', 'slim-seo-schema' ),
				'tooltip'  => __( 'The value must correspond to the specified propertyID.', 'slim-seo-schema' ),
				'required' => true,
			],
		],
	],
	[
		'id'      => 'maximumEnrollment',
		'label'   => __( 'Maximum enrollment', 'slim-seo-schema' ),
		'tooltip' => __( 'The maximum capacity of students that can be enrolled in the program per time it is offered.', 'slim-seo-schema' ),
	],
	[
		'id'      => 'numberOfCredits',
		'label'   => __( 'Number of credits', 'slim-seo-schema' ),
		'tooltip' => __( 'The number of credits earned by completing this program.', 'slim-seo-schema' ),
	],
	[
		'id'       => 'occupationalCategory',
		'label'    => __( 'Occupational category', 'slim-seo-schema' ),
		'required' => true,
		'tooltip'  => __( 'The occupation towards which training program attendees will be working. For multiple occupations, specify values in an array. Use the BLS SOC-6 code as published in the 2010 SOC guide', 'slim-seo-schema' ),
	],
	[
		'id'      => 'occupationalCredentialAwarded',
		'label'   => 'Occupational credential awarded',
		'tooltip' => __( 'The credential provided upon completion of the program. For exp. Associate\'s Degree, Certificate.', 'slim-seo-schema' ),
		'type'    => 'Group',
		'fields'  => [
			[
				'id'       => '@type',
				'std'      => 'EducationalOccupationalCredential',
				'type'     => 'Hidden',
				'required' => true,
			],
			[
				'id'       => 'credentialCategory',
				'required' => true,
			],
		],
	],
	[
		'id'               => 'offers',
		'label'            => __( 'Offers', 'slim-seo-schema' ),
		'tooltip'          => __( 'The estimated cost for the program, if applicable', 'slim-seo-schema' ),
		'type'             => 'Group',
		'cloneable'        => true,
		'cloneItemHeading' => __( 'Offer', 'slim-seo-schema' ),
		'required'         => true,
		'fields'           => [
			[
				'id'       => '@type',
				'std'      => 'Offer',
				'type'     => 'Hidden',
				'required' => true,
			],
			[
				'id'      => 'category',
				'label'   => __( 'Category', 'slim-seo-schema' ),
				'type'    => 'DataList',
				'tooltip' => __( 'The category of the costs that are related to the program.', 'slim-seo-schema' ),
				'show'    => true,
				'options' => [
					'Total Cost'              => __( 'Total Cost', 'slim-seo-schema' ),
					'Tuition'                 => __( 'Tuition', 'slim-seo-schema' ),
					'In-state'                => __( 'In-state', 'slim-seo-schema' ),
					'Out-of-state'            => __( 'Out-of-state', 'slim-seo-schema' ),
					'In-district'             => __( 'In-district', 'slim-seo-schema' ),
					'Out-of-district'         => __( 'Out-of-district', 'slim-seo-schema' ),
					'CostPerCredit'           => __( 'CostPerCredit', 'slim-seo-schema' ),
					'CostPerTerm'             => __( 'CostPerTerm', 'slim-seo-schema' ),
					'Program Fees'            => __( 'Program Fees', 'slim-seo-schema' ),
					'Books and Supplies Fees' => __( 'Books and Supplies Fees', 'slim-seo-schema' ),
					'Uniform Fees'            => __( 'Uniform Fees', 'slim-seo-schema' ),
					'Activities Fees'         => __( 'Activities Fees', 'slim-seo-schema' ),
					'Technology Fees'         => __( 'Technology Fees', 'slim-seo-schema' ),
					'Other Fees'              => __( 'Other Fees', 'slim-seo-schema' ),
				],
			],
			[
				'id'       => 'priceSpecification',
				'type'     => 'Group',
				'required' => true,
				'fields'   => [
					[
						'id'       => '@type',
						'std'      => 'PriceSpecification',
						'type'     => 'Hidden',
						'required' => true,
					],
					[
						'id'       => 'price',
						'label'    => __( 'Price', 'slim-seo-schema' ),
						'tooltip'  => __( 'The price amount for the specified offer.', 'slim-seo-schema' ),
						'required' => true,
					],
					[
						'id'       => 'priceCurrency',
						'label'    => __( 'Price currency', 'slim-seo-schema' ),
						'tooltip'  => __( 'The currency of the price for the specified offer.', 'slim-seo-schema' ),
						'required' => true,
					],
				],
			],
		],
	],
	[
		'id'       => 'provider',
		'label'    => 'Provider',
		'tooltip'  => __( 'The educational organization providing the program.', 'slim-seo-schema' ),
		'type'     => 'Group',
		'required' => true,
		'fields'   => [
			[
				'id'       => '@type',
				'std'      => 'EducationalOrganization',
				'type'     => 'Hidden',
				'required' => true,
			],
			Helper::get_property( 'name', [
				'show' => true,
			] ),
			Helper::get_property( 'address', [
				'required'         => true,
				'cloneable'        => true,
				'cloneItemHeading' => __( 'Address', 'slim-seo-schema' ),
				'tooltip'          => __( 'The physical address where students go to take the program.', 'slim-seo-schema' ),
			] ),
			[
				'id'       => 'contactPoint',
				'label'    => __( 'Telephone', 'slim-seo-schema' ),
				'tooltip'  => __( 'The phone number that a prospective enrollee can call for more information.', 'slim-seo-schema' ),
				'type'     => 'Group',
				'required' => true,
				'fields'   => [
					[
						'id'       => '@type',
						'std'      => 'ContactPoint',
						'type'     => 'Hidden',
						'required' => true,
					],
					[
						'id'       => 'contactType',
						'std'      => 'Admissions',
						'type'     => 'Hidden',
						'required' => true,
					],
					[
						'id'       => 'telephone',
						'required' => true,
					],
				],
			],
		],
	],
	[
		'id'      => 'programPrerequisites',
		'label'   => 'Program prerequisites',
		'tooltip' => __( 'The prerequisites for attending the program.', 'slim-seo-schema' ),
		'type'    => 'Group',
		'fields'  => [
			[
				'id'       => '@type',
				'std'      => 'EducationalOccupationalCredential',
				'type'     => 'Hidden',
				'required' => true,
			],
			[
				'id'       => 'credentialCategory',
				'required' => true,
			],
		],
	],
	[
		'id'      => 'startDate',
		'label'   => __( 'Start date', 'slim-seo-schema' ),
		'tooltip' => __( 'The start date of the program, formatted in ISO-8601 format. If the program is offered more than once per calendar year, specify the property multiple times.', 'slim-seo-schema' ),
		'type'    => 'Date',
	],
	[
		'id'      => 'termDuration',
		'label'   => __( 'Term Duration', 'slim-seo-schema' ),
		'tooltip' => __( 'The time it takes to complete a term, expressed in ISO-8601 format. For example, a quarter could be 3 months (P3M), a semester could be 4 months (P4M).', 'slim-seo-schema' ),
	],
	[
		'id'      => 'termsPerYear',
		'label'   => __( 'Terms per year', 'slim-seo-schema' ),
		'tooltip' => __( 'The number of times that terms of study are offered per year.', 'slim-seo-schema' ),
	],
	[
		'id'      => 'timeOfDay',
		'label'   => __( 'Time of day', 'slim-seo-schema' ),
		'tooltip' => __( 'The time of day when the program runs.', 'slim-seo-schema' ),
	],
	[
		'id'      => 'typicalCreditsPerTerm',
		'label'   => __( 'Typical credits per term', 'slim-seo-schema' ),
		'tooltip' => __( 'The number of credits a full-time student is expected to take.', 'slim-seo-schema' ),
	],
	[
		'id'       => 'timeToComplete',
		'label'    => __( 'Time to complete', 'slim-seo-schema' ),
		'tooltip'  => __( 'The time it takes for a full-time student to complete the program in weeks, months, or years, expressed in ISO-8601 format.', 'slim-seo-schema' ),
		'required' => true,
	],
];
