import { render, useReducer } from "@wordpress/element";
import { __ } from "@wordpress/i18n";
import Upload from "./components/Upload";

const App = () => {
	const [ upload, toggle ] = useReducer( upload => !upload, false );

	return (
		<>
			<h3>{ __( 'Schema Import and Export', 'slim-seo-schema' ) }</h3>
			<p>{ __( 'This tool helps you to export existing schemas to JSON file for backup and import them on other sites or restore on this site.', 'slim-seo-schema' ) }</p>
			<div className="sss-ie-buttons">
				<a className="button" href={ SSSchemaIE.exportUrl } >{ __( 'Export', 'slim-seo-schema' ) }</a>
				<button type="button" className="button" onClick={ toggle }>{ __( 'Import', 'slim-seo-schema' ) }</button>
			</div>
			{ upload && <Upload /> }
		</>
	);
};

render( <App />, document.getElementById( 'ssschema-import-export' ) );
