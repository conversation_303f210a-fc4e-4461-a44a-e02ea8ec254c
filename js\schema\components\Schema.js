import { memo, useContext } from "@wordpress/element";
import { __ } from "@wordpress/i18n";
import dotProp from 'dot-prop';
import { Tab, TabList, TabPanel, Tabs } from 'react-tabs';
import { SchemaLinkContext } from "../contexts/SchemaLinkContext";
import Location from './Location';
import { Panel } from './Panel';
import SchemaType from './SchemaType';

const Schema = ( { schema, id, deleteProp } ) => (
	<Panel className="sss-schema" title={ <Title id={ id } schema={ schema } /> } defaultExpanded={ false } deleteProp={ deleteProp } id={ id }>
		<Tabs forceRenderTabPanel={ true }>
			<TabList>
				<Tab>{ schema.type === 'CustomJsonLd' ? __( 'Code', 'slim-seo-schema' ) : __( 'Properties', 'slim-seo-schema' ) }</Tab>
				<Tab>{ __( 'Location', 'slim-seo-schema' ) }</Tab>
			</TabList>
			<TabPanel>
				<SchemaType schemaID={ id } schema={ schema } />
			</TabPanel>
			<TabPanel className="react-tabs__tab-panel og-tab-panel--settings">
				<Location schemaID={ id } location={ dotProp.get( schema, 'location' ) } />
			</TabPanel>
		</Tabs>
	</Panel>
);

const Title = ( { id, schema } ) => {
	const { updateSchemaLinkLabel } = useContext( SchemaLinkContext );
	return (
		<input
			type="text"
			name={ `schemas[${ id }][fields][_label]` }
			defaultValue={ dotProp.get( schema, 'fields._label', schema.type ) }
			onClick={ e => e.stopPropagation() }
			onChange={ e => updateSchemaLinkLabel( id, e.target.value ) }
		/>
	);
};

export default Schema;
