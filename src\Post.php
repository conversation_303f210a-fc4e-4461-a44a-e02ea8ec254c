<?php
namespace SlimSEOPro\Schema;

class Post {
	public function __construct() {
		add_action( 'admin_print_styles-post-new.php', [ $this, 'enqueue' ] );
		add_action( 'admin_print_styles-post.php', [ $this, 'enqueue' ] );

		add_action( 'add_meta_boxes', [ $this, 'add_meta_box' ] );
		add_action( 'save_post', [ $this, 'save' ] );
	}

	public function enqueue() {
		global $post;
		$post_types = $this->get_post_types();
		if ( ! in_array( $post->post_type, $post_types, true ) ) {
			return;
		}

		wp_enqueue_style( 'slim-seo-schema', SLIM_SEO_SCHEMA_URL . 'css/schema.css', [ 'wp-components' ], SLIM_SEO_SCHEMA_VER );
		wp_enqueue_script( 'slim-seo-schema', SLIM_SEO_SCHEMA_URL . 'js/post.js', [ 'wp-element', 'wp-components', 'wp-i18n', 'wp-hooks' ], SLIM_SEO_SCHEMA_VER, true );

		$localized_data = [
			'rest'            => untrailingslashit( rest_url() ),
			'nonce'           => wp_create_nonce( 'wp_rest' ),
			'mediaPopupTitle' => __( 'Select An Image', 'slim-seo-schema' ),
			'schema'          => get_post_meta( $post->ID, 'slim_seo_schema', true ) ?: [],
		];
		wp_localize_script( 'slim-seo-schema', 'SSSchema', $localized_data );

		do_action( 'slim_seo_schema_enqueue' );
		do_action( 'slim_seo_schema_enqueue_post' );
	}

	public function add_meta_box() {
		$context  = apply_filters( 'slim_seo_meta_box_context', 'normal' );
		$priority = apply_filters( 'slim_seo_meta_box_priority', 'high' );

		$post_types = $this->get_post_types();
		foreach ( $post_types as $post_type ) {
			add_meta_box( 'schema', __( 'Schema', 'slim-seo-schema' ), [ $this, 'render' ], $post_type, $context, $priority );
		}
	}

	private function get_post_types() {
		$post_types = get_post_types( [ 'public' => true ] );
		unset( $post_types['attachment'] );
		$post_types = apply_filters( 'slim_seo_meta_box_post_types', $post_types );

		return $post_types;
	}

	public function save( $post_id ) {
		// Verify nonce.
		$nonce = isset( $_POST['sss_nonce'] ) ? (string) $_POST['sss_nonce'] : '';
		if ( ! wp_verify_nonce( $nonce, 'save-schema' ) ) {
			return;
		}

		// Save data to the post, not revisions.
		$parent  = wp_is_post_revision( $post_id );
		$post_id = $parent ? $parent : $post_id;

		// Get only one schema.
		$schema = isset( $_POST['schemas'] ) ? wp_unslash( $_POST['schemas'] ) : [];
		$schema = isset( $schema['post'] ) ? $schema['post'] : [];

		if ( empty( $schema ) ) {
			delete_post_meta( $post_id, 'slim_seo_schema' );
		} else {
			update_post_meta( $post_id, 'slim_seo_schema', $schema );
		}
	}

	public function render() {
		wp_nonce_field( 'save-schema', 'sss_nonce' );
		?>
		<div id="sss-post"></div>
		<?php
	}
}
