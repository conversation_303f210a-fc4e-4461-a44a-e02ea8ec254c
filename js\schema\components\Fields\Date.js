import { Control } from "@elightup/form";
import { DateTimePicker, Dropdown } from "@wordpress/components";
import { memo, useRef } from "@wordpress/element";
import DeleteButton from '../Button/DeleteButton';
import PropInserter from "./PropInserter";

export default memo( ( { className, property, deleteProp } ) => {
	const inputRef = useRef();
	const { id, label, required, std, tooltip } = property;

	return (
		<Control className={ className } label={ label } id={ id } required={ required } tooltip={ tooltip }>
			<div className="sss-input-wrapper">
				<Dropdown
					className="sss-datetime"
					position="bottom left"
					renderToggle={ ( { onToggle } ) => <input type='text' id={ id } name={ id } defaultValue={ std } ref={ inputRef } onFocus={ onToggle } /> }
					renderContent={ ( { onToggle } ) => <DateTimePicker
						onChange={ date => inputRef.current.value = date.replace( 'T', ' ' ).replace( /\..*$/, '' ) }
					/> }
				/>
				<PropInserter inputRef={ inputRef } />
			</div>
			{ required || <DeleteButton id={ id } deleteProp={ deleteProp } /> }
		</Control>
	);
}, ( prevProps, nextProps ) => prevProps.property.id === nextProps.property.id );
