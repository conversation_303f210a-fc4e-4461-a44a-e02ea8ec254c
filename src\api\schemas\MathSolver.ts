import { commonProperties } from './commonProperties'

export default [
  {
    id: 'googleDocs',
    type: 'GoogleDocs',
    url: 'https://developers.google.com/search/docs/appearance/structured-data/math-solvers',
    show: false,
  },
  commonProperties.context,
  commonProperties.type('MathSolver'),
  {
    ...commonProperties.name,
    show: true,
    tooltip: 'The name of the item.',
  },
  {
    id: 'inLanguage',
    label: 'In language',
    tooltip: 'The language(s) that are supported by your math problem solving site.',
    std: 'en',
    show: true,
  },
  {
    id: 'potentialAction',
    label: 'Potential action',
    type: 'Group',
    required: true,
    cloneable: true,
    cloneItemHeading: 'Action',
    tooltip: 'The action that leads to a mathematical explanation of a math expression.',
    fields: [
      {
        id: '@type',
        std: 'SolveMathAction',
        type: 'Hidden',
        required: true,
      },
      {
        id: 'eduQuestionType',
        label: 'Question type',
        tooltip: 'The problem type(s) that are capable of being solved by the target property.',
        type: 'Select',
        show: true,
        options: {
          'Absolute Value Equation': 'Absolute value equations',
          'Algebra': 'Algebra',
          'Arc Length': 'Arc length problems',
          'Arithmetic': 'Arithmetic problems',
          'Biquadratic Equation': 'Biquadratic equations',
          'Calculus': 'Calculus',
          'Characteristic Polynomial': 'Characteristic polynomial',
          'Circle': 'Circle related problems',
          'Derivative': 'Derivative',
          'Differential Equation': 'Differential equation problems',
          'Distance': 'Distance problems',
          'Eigenvalue': 'Eigenvalue problems',
          'Eigenvector': 'Eigenvector problems',
          'Ellipse': 'Ellipse problems',
          'Exponential Equation': 'Exponential equations',
          'Function': 'Function',
          'Function Composition': 'Function composition',
          'Geometry': 'Geometry',
          'Hyperbola': 'Hyperbola problems',
          'Inflection Point': 'Inflection point',
          'Integral': 'Integral',
          'Intercept': 'Line intercept problems',
          'Limit': 'Limit problems',
          'Line Equation': 'Line equation problems',
          'Linear Algebra': 'Linear algebra',
          'Linear Equation': 'Linear equations',
          'Linear Inequality': 'Linear inequalities',
          'Logarithmic Equation': 'Logarithmic equations',
          'Logarithmic Inequality': 'Logarithmic inequalities',
          'Matrix': 'Matrix',
          'Midpoint': 'Midpoint problems',
          'Parabola': 'Parabola problems',
          'Parallel': 'Parallel line problems',
          'Perpendicular': 'Perpendicular problems',
          'Polynomial Equation': 'Polynomial equations',
          'Polynomial Expression': 'Polynomial expressions',
          'Polynomial Inequality': 'Polynomial inequalities',
          'Quadratic Equation': 'Quadratic equations',
          'Quadratic Expression': 'Quadratic expressions',
          'Quadratic Inequality': 'Quadratic inequalities',
          'Radical Equation': 'Radical equations',
          'Radical Inequality': 'Radical inequalities',
          'Rational Equation': 'Rational equations',
          'Rational Expression': 'Rational expressions',
          'Rational Inequality': 'Rational inequalities',
          'Slope': 'Slope problems',
          'Statistics': 'Statistics problems',
          'System of Equations': 'System of equations problems',
          'Trigonometry': 'Trigonometry',
        },
      },
      {
        id: 'mathExpression-input',
        label: 'Math expression input',
        required: true,
        tooltip: 'A mathematical expression (for exp. x^2-3x=0) that may be simplified, transformed, or solved for a specific variable.',
      },
      {
        id: 'target',
        label: 'Target',
        tooltip: 'The URL template that describes the target of the action.',
        std: '{{ site.url }}/solve?q={mathExpression-input}',
      },
    ],
  },
]
