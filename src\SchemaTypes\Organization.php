<?php
namespace SlimSEOPro\Schema\SchemaTypes;

return [
	[
		'id'       => '@id',
		'type'     => 'Hidden',
		'std'      => '{{ site.url }}#{{ id }}',
		'required' => true,
	],
	[
		'id'   => 'schemaDocs',
		'type' => 'SchemaDocs',
		'url'  => 'https://schema.org/Organization',
		'show' => true,
	],
	[
		'label'    => __( 'Type', 'slim-seo-schema' ),
		'id'       => '@type',
		'required' => true,
		'std'      => 'Organization',
		'type'     => 'Select',
		'options'  => [
			'Organization'             => __( 'General organization', 'slim-seo-schema' ),
			'Airline'                  => __( 'Airline', 'slim-seo-schema' ),
			'Consortium'               => __( 'Consortium', 'slim-seo-schema' ),
			'Corporation'              => __( 'Corporation', 'slim-seo-schema' ),
			'EducationalOrganization'  => __( 'Educational organization', 'slim-seo-schema' ),
			'FundingScheme'            => __( 'Funding scheme', 'slim-seo-schema' ),
			'GovernmentOrganization'   => __( 'Government organization', 'slim-seo-schema' ),
			'LibrarySystem'            => __( 'Library system', 'slim-seo-schema' ),
			'MedicalOrganization'      => __( 'Medical organization', 'slim-seo-schema' ),
			'NGO'                      => __( 'NGO', 'slim-seo-schema' ),
			'NewsMediaOrganization'    => __( 'News media organization', 'slim-seo-schema' ),
			'OnlineBusiness'           => __( 'Online business', 'slim-seo-schema' ),
			'PerformingGroup'          => __( 'Performing group', 'slim-seo-schema' ),
			'PoliticalParty'           => __( 'Political party', 'slim-seo-schema' ),
			'Project'                  => __( 'Project', 'slim-seo-schema' ),
			'ResearchOrganization'     => __( 'Research organization', 'slim-seo-schema' ),
			'SearchRescueOrganization' => __( 'Search rescue organization', 'slim-seo-schema' ),
			'SportsOrganization'       => __( 'Sports organization', 'slim-seo-schema' ),
			'WorkersUnion'             => __( 'Workers union', 'slim-seo-schema' ),
		],
	],
	Helper::get_property( 'name', [
		'required' => true,
		'tooltip'  => __( 'The name of the organization', 'slim-seo-schema' ),
		'std'      => '{{ site.title }}',
	] ),
	Helper::get_property( 'Person', [
		'id'        => 'alumni',
		'label'     => __( 'Alumni', 'slim-seo-schema' ),
		'tooltip'   => __( 'Alumni of the organization.', 'slim-seo-schema' ),
		'cloneable' => true,
	] ),
	[
		'id'      => 'areaServed',
		'label'   => __( 'Area served', 'slim-seo-schema' ),
		'tooltip' => __( 'The geographic area where a service or offered item is provided.', 'slim-seo-schema' ),
	],
	[
		'id'      => 'award',
		'label'   => __( 'Award', 'slim-seo-schema' ),
		'tooltip' => __( 'An award won by or for this organization.', 'slim-seo-schema' ),
	],
	Helper::get_property( 'address' ),
	[
		'id'      => 'acceptsReservations',
		'label'   => __( 'Accepts reservations', 'slim-seo-schema' ),
		'tooltip' => __( 'Indicates whether a organization accepts reservations.', 'slim-seo-schema' ),
		'type'    => 'DataList',
		'std'     => 'True',
		'options' => [
			'True'  => __( 'True', 'slim-seo-schema' ),
			'False' => __( 'False', 'slim-seo-schema' ),
		],
	],
	Helper::get_property( 'aggregateRating' ),
	[
		'id'               => 'contactPoint',
		'label'            => __( 'Contact points', 'slim-seo-schema' ),
		'tooltip'          => __( 'Contact points for a person or organization.', 'slim-seo-schema' ),
		'type'             => 'Group',
		'cloneable'        => true,
		'cloneItemHeading' => __( 'Contact point', 'slim-seo-schema' ),
		'fields'           => [
			[
				'id'       => '@type',
				'std'      => 'ContactPoint',
				'type'     => 'Hidden',
				'required' => true,
			],
			[
				'id'      => 'contactOption',
				'label'   => __( 'Contact option', 'slim-seo-schema' ),
				'tooltip' => __( 'An option available on this contact point.', 'slim-seo-schema' ),
				'type'    => 'DataList',
				'options' => [
					'HearingImpairedSupported' => __( 'Toll-free number', 'slim-seo-schema' ),
					'TollFree'                 => __( 'Support for hearing-impaired callers', 'slim-seo-schema' ),
				],
			],
			[
				'label'   => __( 'Contact type', 'slim-seo-schema' ),
				'id'      => 'contactType',
				'type'    => 'Text',
				'show'    => true,
				'tooltip' => __( 'Used to specify the kind of contact point. For exp. a sales contact point, a PR contact point,...', 'slim-seo-schema' ),
			],
			[
				'id'       => 'email',
				'label'    => __( 'Email', 'slim-seo-schema' ),
				'tooltip'  => __( 'Email address.', 'slim-seo-schema' ),
				'required' => true,
			],
			[
				'id'      => 'telephone',
				'label'   => __( 'Telephone', 'slim-seo-schema' ),
				'tooltip' => __( 'The telephone number.', 'slim-seo-schema' ),
				'show'    => true,
			],
			[
				'id'      => 'faxNumber',
				'label'   => __( 'Fax number', 'slim-seo-schema' ),
				'tooltip' => __( 'The fax number.', 'slim-seo-schema' ),
			],
			Helper::get_property( 'OpeningHoursSpecification', [
				'id'               => 'hoursAvailable',
				'label'            => __( 'Hours available', 'slim-seo-schema' ),
				'tooltip'          => __( 'The hours during which this service or contact is available.', 'slim-seo-schema' ),
				'cloneItemHeading' => __( 'Opening hours', 'slim-seo-schema' ),
				'show'             => true,
			] ),
			[
				'id'      => 'productSupported',
				'label'   => __( 'Product supported', 'slim-seo-schema' ),
				'tooltip' => __( 'The product or service this support contact point is related to.', 'slim-seo-schema' ),
			],
			[
				'id'      => 'areaServed',
				'label'   => __( 'Area served', 'slim-seo-schema' ),
				'tooltip' => __( 'The geographic area where a service or offered item is provided', 'slim-seo-schema' ),
			],
		],
	],
	[
		'id'      => 'currenciesAccepted',
		'label'   => __( 'Currencies accepted', 'slim-seo-schema' ),
		'tooltip' => __( 'The currency accepted. Use standard formats: ISO 4217 currency format e.g. "USD"; Ticker symbol for cryptocurrencies e.g. "BTC"; well known names for Local Exchange Tradings Systems (LETS) and other currency types e.g. "Ithaca HOUR".', 'slim-seo-schema' ),
	],
	[
		'label'            => __( 'Brand', 'slim-seo-schema' ),
		'id'               => 'brand',
		'tooltip'          => __( 'The brand(s) associated with a product or service, or the brand(s) maintained by an organization or business person.', 'slim-seo-schema' ),
		'type'             => 'Group',
		'cloneable'        => true,
		'cloneItemHeading' => __( 'Brand', 'slim-seo-schema' ),
		'fields'           => [
			[
				'id'       => '@type',
				'std'      => 'Brand',
				'type'     => 'Hidden',
				'required' => true,
			],
			[
				'id'       => 'name',
				'label'    => __( 'Name', 'slim-seo-schema' ),
				'std'      => '{{ site.title }}',
				'required' => true,
			],
			[
				'label'   => __( 'Logo', 'slim-seo-schema' ),
				'id'      => 'logo',
				'tooltip' => __( 'Link to logo associated with the brand.', 'slim-seo-schema' ),
				'type'    => 'Image',
				'show'    => true,
			],
			Helper::get_property( 'url', [
				'show'    => true,
				'tooltip' => __( 'The URL of the brand.', 'slim-seo-schema' ),
			] ),
		],
	],
	[
		'id'               => 'department',
		'label'            => __( 'Departments', 'slim-seo-schema' ),
		'cloneable'        => true,
		'cloneItemHeading' => __( 'Department', 'slim-seo-schema' ),
		'tooltip'          => __( 'Departments of the organization', 'slim-seo-schema' ),
	],
	[
		'id'          => 'parentOrganization',
		'label'       => __( 'Parent organization', 'slim-seo-schema' ),
		'tooltip'     => __( 'The larger organization that this organization is a sub organization of, if any.', 'slim-seo-schema' ),
		'description' => __( 'Please create an Organization schema and link to this property via a dynamic variable', 'slim-seo-schema' ),
	],
	[
		'id'      => 'duns',
		'label'   => __( 'Duns', 'slim-seo-schema' ),
		'tooltip' => __( 'The Dun & Bradstreet DUNS number for identifying an organization or business person.', 'slim-seo-schema' ),
	],
	Helper::get_property( 'Person', [
		'id'               => 'employee',
		'label'            => __( 'Employees', 'slim-seo-schema' ),
		'tooltip'          => __( 'Someone working for this organization.', 'slim-seo-schema' ),
		'cloneable'        => true,
		'cloneItemHeading' => __( 'Employee', 'slim-seo-schema' ),
	] ),
	[
		'id'      => 'email',
		'label'   => __( 'Email', 'slim-seo-schema' ),
		'tooltip' => __( 'The email address of the organization', 'slim-seo-schema' ),
		'show'    => true,
	],
	[
		'id'      => 'faxNumber',
		'label'   => __( 'Fax number', 'slim-seo-schema' ),
		'tooltip' => __( 'The fax number of the organization', 'slim-seo-schema' ),
	],
	Helper::get_property( 'Person', [
		'id'               => 'founder',
		'label'            => __( 'Founder', 'slim-seo-schema' ),
		'tooltip'          => __( 'A person who founded this organization.', 'slim-seo-schema' ),
		'cloneable'        => true,
		'cloneItemHeading' => __( 'Employee', 'slim-seo-schema' ),
	] ),
	[
		'id'      => 'foundingDate',
		'label'   => __( 'Founding date', 'slim-seo-schema' ),
		'tooltip' => __( 'The date that this organization was founded.', 'slim-seo-schema' ),
		'type'    => 'Date',
	],
	[
		'id'      => 'foundingLocation',
		'label'   => __( 'Founding location', 'slim-seo-schema' ),
		'type'    => 'Group',
		'tooltip' => __( 'The place where the organization was founded.', 'slim-seo-schema' ),
		'fields'  => [
			[
				'id'       => '@type',
				'std'      => 'Place',
				'type'     => 'Hidden',
				'required' => true,
			],
			Helper::get_property( 'name', [
				'label'    => __( 'Name', 'slim-seo-schema' ),
				'tooltip'  => __( 'The name of the item', 'slim-seo-schema' ),
				'required' => true,
			] ),
			Helper::get_property( 'address', [
				'label'    => '',
				'required' => true,
				'tooltip'  => __( 'The physical address where the organization was founded.', 'slim-seo-schema' ),
			] ),
			[
				'id'      => 'url',
				'label'   => __( 'URL', 'slim-seo-schema' ),
				'tooltip' => __( 'URL of the item', 'slim-seo-schema' ),
				'show'    => true,
			],
		],
	],
	Helper::get_property( 'Person', [
		'id'               => 'funder',
		'label'            => __( 'Funder', 'slim-seo-schema' ),
		'tooltip'          => __( 'A person or organization that supports (sponsors) something through some kind of financial contribution.', 'slim-seo-schema' ),
		'cloneable'        => true,
		'cloneItemHeading' => __( 'Funder', 'slim-seo-schema' ),
	] ),
	Helper::get_property( 'image' ),
	[
		'id'      => 'globalLocationNumber',
		'label'   => __( 'Global location number', 'slim-seo-schema' ),
		'tooltip' => __( 'The Global Location Number (GLN, sometimes also referred to as International Location Number or ILN) of the respective organization, person, or place.', 'slim-seo-schema' ),
	],
	[
		'id'      => 'geo',
		'label'   => __( 'Geolocation', 'slim-seo-schema' ),
		'type'    => 'Group',
		'tooltip' => __( 'Geographic coordinates of the business', 'slim-seo-schema' ),
		'fields'  => [
			[
				'id'       => '@type',
				'std'      => 'GeoCoordinates',
				'type'     => 'Hidden',
				'required' => true,
			],
			[
				'id'      => 'latitude',
				'label'   => __( 'Latitude', 'slim-seo-schema' ),
				'tooltip' => __( 'The latitude of the business location. The precision must be at least 5 decimal places.', 'slim-seo-schema' ),
				'show'    => true,
			],
			[
				'id'      => 'longitude',
				'label'   => __( 'Longitude', 'slim-seo-schema' ),
				'tooltip' => __( 'The longitude of the business location. The precision must be at least 5 decimal places.', 'slim-seo-schema' ),
				'show'    => true,
			],
		],
	],
	[
		'id'               => 'hasPOS',
		'label'            => __( 'Has POS', 'slim-seo-schema' ),
		'type'             => 'Group',
		'tooltip'          => __( 'Points-of-Sales operated by the organization or person.', 'slim-seo-schema' ),
		'cloneable'        => true,
		'cloneItemHeading' => __( 'POS', 'slim-seo-schema' ),
		'fields'           => [
			[
				'id'       => '@type',
				'std'      => 'Place',
				'type'     => 'Hidden',
				'required' => true,
			],
			Helper::get_property( 'name', [
				'label'    => __( 'Name', 'slim-seo-schema' ),
				'tooltip'  => __( 'The name of the item.', 'slim-seo-schema' ),
				'required' => true,
			] ),
			Helper::get_property( 'address', [
				'label'    => '',
				'required' => true,
				'tooltip'  => __( 'The physical address where students go to take the program.', 'slim-seo-schema' ),
			] ),
			[
				'id'      => 'url',
				'label'   => __( 'URL', 'slim-seo-schema' ),
				'tooltip' => __( 'URL of the item.', 'slim-seo-schema' ),
			],
		],
	],
	[
		'label'   => __( 'ISIC V4', 'slim-seo-schema' ),
		'id'      => 'isicV4',
		'tooltip' => __( 'The International Standard of Industrial Classification of All Economic Activities (ISIC), Revision 4 code for a particular organization, business person, or place.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'ISO 6523 Code', 'slim-seo-schema' ),
		'id'      => 'iso6523Code',
		'tooltip' => __( 'An organization identifier as defined in ISO 6523(-1).', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Keywords', 'slim-seo-schema' ),
		'id'      => 'keywords',
		'tooltip' => __( 'Keywords or tags used to describe some item. Multiple textual entries in a keywords list are typically delimited by commas.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Knows language', 'slim-seo-schema' ),
		'id'      => 'knowsLanguage',
		'tooltip' => __( 'Indicate a known language. Use language codes from the IETF BCP 47 standard.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Legal name', 'slim-seo-schema' ),
		'id'      => 'legalName',
		'tooltip' => __( 'The official name of the organization, e.g. the registered company name.', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Lei Code', 'slim-seo-schema' ),
		'id'      => 'leiCode',
		'tooltip' => __( 'An organization identifier that uniquely identifies a legal entity as defined in ISO 17442.', 'slim-seo-schema' ),
	],
	[
		'id'               => 'location',
		'label'            => __( 'Location', 'slim-seo-schema' ),
		'tooltip'          => __( 'The location of, for example, where an event is happening, where an organization is located, or where an action takes place.', 'slim-seo-schema' ),
		'type'             => 'Group',
		'cloneable'        => true,
		'cloneItemHeading' => __( 'Location', 'slim-seo-schema' ),
		'fields'           => [
			[
				'id'        => '@type',
				'label'     => __( 'Type', 'slim-seo-schema' ),
				'type'      => 'DataList',
				'std'       => 'Place',
				'required'  => true,
				'dependant' => true,
				'options'   => [
					'Place'           => __( 'Physical location', 'slim-seo-schema' ),
					'VirtualLocation' => __( 'Online', 'slim-seo-schema' ),
				],
			],
			[
				'label'      => __( 'Name', 'slim-seo-schema' ),
				'id'         => 'name',
				'required'   => true,
				'dependency' => '[@type]:Place',
			],
			Helper::get_property( 'address', [
				'label'      => '',
				'dependency' => '[@type]:Place',
				'show'       => true,
			] ),
			[
				'id'         => 'url',
				'label'      => __( 'URL', 'slim-seo-schema' ),
				'required'   => true,
				'dependency' => '[@type]:VirtualLocation',
			],
		],
	],
	[
		'id'      => 'logo',
		'label'   => __( 'Logo URL', 'slim-seo-schema' ),
		'show'    => true,
		'tooltip' => __( 'The URL of the logo of the organization.', 'slim-seo-schema' ),
		'type'    => 'Image',
		'std'     => '{{ site.icon }}',
	],
	[
		'id'      => 'menu',
		'label'   => __( 'Menu', 'slim-seo-schema' ),
		'tooltip' => __( 'For food establishments, the fully-qualified URL of the menu', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'NAICS', 'slim-seo-schema' ),
		'id'      => 'naics',
		'tooltip' => __( 'The North American Industry Classification System (NAICS) code for a particular organization or business person.', 'slim-seo-schema' ),
	],
	[
		'id'      => 'numberOfEmployees',
		'label'   => __( 'Number of employees', 'slim-seo-schema' ),
		'type'    => 'Group',
		'tooltip' => __( 'The number of employees in an organization e.g. business.', 'slim-seo-schema' ),
		'fields'  => [
			[
				'id'       => '@type',
				'std'      => 'QuantitativeValue',
				'type'     => 'Hidden',
				'required' => true,
			],
			[
				'id'   => 'value',
				'show' => true,
			],
		],
	],
	Helper::get_property( 'OpeningHoursSpecification', [
		'id'               => 'openingHoursSpecification',
		'label'            => __( 'Opening hours', 'slim-seo-schema' ),
		'tooltip'          => __( 'Hours during which the business location is open', 'slim-seo-schema' ),
		'cloneItemHeading' => __( 'Opening hours', 'slim-seo-schema' ),
	] ),
	[
		'label'            => __( 'Owns', 'slim-seo-schema' ),
		'id'               => 'owns',
		'tooltip'          => __( 'Products owned by the organization or person.', 'slim-seo-schema' ),
		'cloneable'        => true,
		'cloneItemHeading' => __( 'Product', 'slim-seo-schema' ),
	],
	[
		'label'   => __( 'Payment accepted', 'slim-seo-schema' ),
		'id'      => 'paymentAccepted',
		'tooltip' => __( 'Cash, Credit Card, Cryptocurrency, Local Exchange Tradings System, etc.', 'slim-seo-schema' ),
	],
	[
		'id'      => 'priceRange',
		'label'   => __( 'Price range', 'slim-seo-schema' ),
		'tooltip' => __( 'The relative price range of a business, commonly specified by either a numerical range (for example, "$10-15") or a normalized number of currency signs (for example, "$$$"). This field must be shorter than 100 characters. If it\'s longer than 100 characters, Google won\'t show a price range for the business.', 'slim-seo-schema' ),
	],
	Helper::get_property( 'Review' ),
	[
		'id'      => 'servesCuisine',
		'label'   => __( 'Serves cuisine', 'slim-seo-schema' ),
		'tooltip' => __( 'The type of cuisine the restaurant serves', 'slim-seo-schema' ),
	],
	[
		'id'        => 'sameAs',
		'label'     => __( 'Same as', 'slim-seo-schema' ),
		'tooltip'   => __( 'URL of a reference Web page that unambiguously indicates the item\'s identity. E.g. the URL of the item\'s Wikipedia page, Wikidata entry, social media profiles or official website.', 'slim-seo-schema' ),
		'show'      => true,
		'cloneable' => true,
	],
	[
		'label'   => __( 'Slogan', 'slim-seo-schema' ),
		'id'      => 'slogan',
		'show'    => true,
		'tooltip' => __( 'A slogan or motto associated with the organization or person.', 'slim-seo-schema' ),
	],
	Helper::get_property( 'Person', [
		'id'               => 'sponsor',
		'label'            => __( 'Sponsor', 'slim-seo-schema' ),
		'tooltip'          => __( 'A person or organization that supports a thing through a pledge, promise, or financial contribution. e.g. a sponsor of a Medical Study or a corporate sponsor of an event.', 'slim-seo-schema' ),
		'cloneable'        => true,
		'cloneItemHeading' => __( 'Sponsor', 'slim-seo-schema' ),
	] ),
	[
		'label'   => __( 'Tax ID', 'slim-seo-schema' ),
		'id'      => 'taxID',
		'tooltip' => __( 'The Tax / Fiscal ID of the organization or person, for exp. the TIN in the US or the CIF/NIF in Spain.', 'slim-seo-schema' ),
	],
	[
		'id'      => 'telephone',
		'label'   => __( 'Telephone', 'slim-seo-schema' ),
		'tooltip' => __( 'The telephone number of the organization. Be sure to include the country code and area code in the phone number.', 'slim-seo-schema' ),
		'show'    => true,
	],
	Helper::get_property( 'url', [
		'tooltip' => __( 'The fully-qualified URL of the specific business location. The URL must be a working link.', 'slim-seo-schema' ),
		'std'     => '{{ site.url }} ',
		'show'    => true,
	] ),
	[
		'label'   => __( 'Vat ID', 'slim-seo-schema' ),
		'id'      => 'vatID',
		'tooltip' => __( 'The Value-added Tax ID of the organization', 'slim-seo-schema' ),
	],
];
