<?php
namespace SlimSEOPro\Schema;

use SlimSEO\Settings\Page;
use SlimSEO\Updater\Tab;
use eLightUp\PluginUpdater\Manager;
use SlimSEO\Updater\Settings as UpdaterSettings;

new Activator;
new Api\General;
new Api\SchemaTypes;
new Api\Data;
new Api\MetaKeys;
new Api\Location;
new Api\Import;
new Integrations\MetaBox\MetaBox;
new Integrations\ACF\ACF;
new Integrations\WooCommerce;

if ( is_admin() ) {
	Page::setup();
	new Settings;
	new Post;
	new ImportExport;

	// Updater.
	if ( defined( 'SLIM_SEO_PRO_VER' ) ) {
		return;
	}
	Tab::setup();
	$manager           = new Manager( [
		'api_url'            => 'https://wpslimseo.com/index.php',
		'my_account_url'     => 'https://wpslimseo.com/my-account/',
		'buy_url'            => 'https://wpslimseo.com/slim-seo-schema/',
		'slug'               => 'slim-seo-schema',
		'settings_page'      => admin_url( 'options-general.php?page=slim-seo#license' ),
		'settings_page_slug' => 'slim-seo',
	] );
	$settings          = new UpdaterSettings( $manager, $manager->checker, $manager->option );
	$manager->settings = $settings;
	$manager->setup();
} else {
	new Integrations\Oxygen;
	new Integrations\Bricks;
	new Integrations\ZionBuilder;

	if ( defined( 'WPSEO_FILE' ) ) {
		new Renderer\Yoast;
	} elseif ( defined( 'SLIM_SEO_VER' ) ) {
		new Renderer\SlimSEO;
	} else {
		new Renderer\Standalone;
	}
}
