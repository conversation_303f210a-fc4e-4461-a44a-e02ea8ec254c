.sss-field--custom {
	.ef-control__input {
		display: block;

		.button {
			margin-left: 0;
		}
	}
}

.sss-custom.sss-custom.sss-custom {
	margin-left: 0;
}

.sss-custom {
	display: flex;
	align-items: center;
	justify-content: space-between;
	grid-gap: 6px;
	margin-bottom: 12px;

	&__remove {
		padding: 0;
		border: none;
		background: none;
		visibility: hidden;
		transition: unset;
		width: 16px;
		height: 16px;
	}

	&:hover>.sss-custom__remove {
		visibility: visible;
		cursor: pointer;
	}

	.dashicon {
		width: 16px;
		height: 16px;
		font-size: 16px;
		color: var(--c-gray);
	}

	.dashicon:hover {
		color: var(--c-red);
	}

	input {
		min-width: 0; // To make the input not exceed parent's width (see conditional logic in a sub-field of nested groups).
		flex: 1;
	}
}