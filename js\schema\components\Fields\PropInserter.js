import { Button, Dropdown } from "@wordpress/components";
import { useContext, useEffect, useState } from "@wordpress/element";
import { __ } from "@wordpress/i18n";
import Select from "react-select";
import slugify from "slugify";
import { SchemaLinkContext } from "../../contexts/SchemaLinkContext";
import { request } from "../../functions";
import Inserter from "../Inserter";

const PropInserter = ( { inputRef, extraInputRef } ) => {
	const [ showModal, setShowModal ] = useState( false );

	const handleSelectItem = ( e, onToggle ) => {
		onToggle();
		if ( e.target.dataset.value === 'post.custom_field' ) {
			setShowModal( true );
			return;
		}

		setValue( `{{ ${ e.target.dataset.value } }}` );
	};

	const setValue = value => {
		inputRef.current.value += value;
		if ( extraInputRef ) {
			extraInputRef.current.value += value;
		}
	};

	return <>
		<Dropdown
			className="sss-dropdown sss-inserter"
			position="bottom left"
			renderToggle={ ( { onToggle } ) => <Button icon="ellipsis" onClick={ onToggle } /> }
			renderContent={ ( { onToggle } ) => <VariableInserter onSelect={ e => handleSelectItem( e, onToggle ) } /> }
		/>
		{ showModal && <Modal setShowModal={ setShowModal } setValue={ setValue } /> }
	</>;
};

const VariableInserter = ( { onSelect } ) => {
	const [ items, setItems ] = useState( [] );
	const { schemaLinks } = useContext( SchemaLinkContext );

	let schemaLinksOptions = {};
	Object.values( schemaLinks ).forEach( label => {
		let id = slugify( label, { lower: true, replacement: '_' } );
		id = id.replace( '.', '_' );
		schemaLinksOptions[ `schemas.${ id }` ] = label;
	} );

	useEffect( () => {
		request( 'data', { type: 'variables' } ).then( data => {
			// Do not change "data" directly as it'll be cached in promises.
			const newData = [ ...data ];
			newData.push( {
				label: __( 'Schema', 'slim-seo-schema' ),
				options: schemaLinksOptions
			} );
			setItems( newData );
		} );
	}, [] );

	return <Inserter items={ items } group={ true } hasSearch={ true } onSelect={ onSelect } />;
};

const Modal = ( { setShowModal, setValue } ) => {
	const [ options, setOptions ] = useState( [] );
	useEffect( () => {
		request( 'meta_keys' ).then( setOptions );
	}, [] );
	const hideModal = () => setShowModal( false );
	const onSelect = item => {
		setValue( `{{ post.custom_field.${ item.value } }}` );
		setShowModal( false );
	};

	return <>
		<div className="sss-modal-overlay" onClick={ hideModal }></div>
		<div className="sss-modal-body">
			<h3 className="sss-modal-heading">
				{ __( 'Select a custom field', 'slim-seo-schema' ) }
				<span className="sss-modal__close" onClick={ hideModal }>&times;</span>
			</h3>
			<Select
				classNamePrefix="react-select"
				options={ options }
				defaultOptions
				onChange={ onSelect }
			/>
		</div>
	</>;
};

export default PropInserter;
