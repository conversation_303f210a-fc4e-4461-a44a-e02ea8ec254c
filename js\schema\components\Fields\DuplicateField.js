import { Control } from "@elightup/form";
import { memo, Suspense, useEffect, useState } from "@wordpress/element";
import { __ } from "@wordpress/i18n";
import clsx from 'clsx';
import { getFieldType, uniqueID } from '../../functions';

const DuplicateField = ( { property } ) => {
	const [ list, setList ] = useState( {} );
	const { id, label, required, std, tooltip } = property;

	useEffect( () => {
		if ( !property.std ) {
			return;
		}
		let fields = {};
		Object.entries( property.std ).forEach( ( [ id, std ] ) => {
			const fieldID = `${ property.id }[${ id }]`;
			fields = { ...fields, [ fieldID ]: { ...property, id: fieldID, std } };
		} );
		setList( { ...list, ...fields } );
		property.std = '';
	}, [] );

	const handleAdd = item => {
		const id = `${ item.id }[${ uniqueID() }]`;
		setList( list => {
			return { ...list, [ id ]: { ...item, id, std: item.fieldStd } };
		} );
	};

	const handleDelete = id => {
		setList( prev => {
			const itemsArray = Object.entries( prev ).filter( ( [ itemID, value ] ) => itemID !== id );
			return Object.fromEntries( itemsArray );
		} );
	};

	return (
		<Control className="sss-field sss-field--duplicate" label={ label } required={ required } id={ id } tooltip={ tooltip }>
			{
				Object.entries( list ).map( ( [ id, item ] ) => <Item key={ id } item={ item } deleteProp={ handleDelete } /> )
			}
			<button type="button" onClick={ () => handleAdd( property ) } className="button" >{ __( '+ Add new', 'slim-seo-schema' ) }</button>
		</Control>
	);
};

const Item = memo( function( { item, deleteProp } ) {
	const { type = 'Text', dependency, parentID } = item;
	const FieldType = getFieldType( type );
	if ( type !== 'Group' ) {
		item = { ...item, label: '' };
	}
	const classNames = clsx( 'sss-field', `sss-field--${ type.toLowerCase() }`, dependency && `dep:${ dependency }` );
	return (
		<Suspense fallback={ __( 'Loading fields... Please wait', 'slim-seo-schema' ) } >
			<FieldType className={ classNames } deleteProp={ deleteProp } property={ item } />
		</Suspense>
	);
}, ( prevProps, nextProps ) => prevProps.item.id === nextProps.item.id );

export default DuplicateField;
