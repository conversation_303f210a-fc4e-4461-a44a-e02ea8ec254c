<?php
namespace SlimSEOPro\Schema\SchemaTypes;

return [
	[
		'id'   => 'googleDocs',
		'type' => 'GoogleDocs',
		'url'  => 'https://developers.google.com/search/docs/appearance/structured-data/review-snippet',
		'show' => true,
	],
	[
		'id'        => '@type',
		'label'     => __( 'Type', 'slim-seo-schema' ),
		'std'       => 'Review',
		'required'  => true,
		'dependant' => true,
		'type'      => 'DataList',
		'options'   => [
			'Review'          => __( 'Review', 'slim-seo-schema' ),
			'AggregateRating' => __( 'Aggregate rating', 'slim-seo-schema' ),
		],
	],
	[
		'id'          => 'author',
		'label'       => __( 'Author', 'slim-seo-schema' ),
		'tooltip'     => __( 'The author of the review. Must be a valid name and should not exceed 100 characters.', 'slim-seo-schema' ),
		'description' => __( 'Please create a Person or an Organization schema and link to this property via a dynamic variable', 'slim-seo-schema' ),
		'std'         => '{{ schemas.person }}',
		'required'    => true,
	],
	Helper::get_property( 'name', [
		'show'    => true,
		'tooltip' => __( 'The title of the review author', 'slim-seo-schema' ),
	] ),
	[
		'id'          => 'itemReviewed',
		'required'    => true,
		'label'       => __( 'Item reviewed', 'slim-seo-schema' ),
		'tooltip'     => __( 'The item that is being reviewed.', 'slim-seo-schema' ),
		'description' => __( 'Please create a schema and link to this property via a dynamic variable', 'slim-seo-schema' ),
	],
	[
		'id'      => 'positiveNotes',
		'type'    => 'Group',
		'label'   => __( 'Positive notes (Pros)', 'slim-seo-schema' ),
		'tooltip' => __( 'A list of positive statements about the product, listed in a specific order', 'slim-seo-schema' ),
		'show'    => true,
		'fields'  => [
			[
				'id'       => '@type',
				'std'      => 'ItemList',
				'type'     => 'Hidden',
				'required' => true,
			],
			[
				'id'        => 'itemListElement',
				'type'      => 'Group',
				'cloneable' => true,
				'show'      => true,
				'fields'    => [
					[
						'id'       => '@type',
						'std'      => 'ListItem',
						'type'     => 'Hidden',
						'required' => true,
					],
					Helper::get_property( 'name', [
						'std'      => '',
						'tooltip'  => __( 'The key statement of the review.', 'slim-seo-schema' ),
						'required' => true,
					] ),
					[
						'id'      => 'position',
						'label'   => __( 'Position', 'slim-seo-schema' ),
						'show'    => true,
						'tooltip' => __( 'The position (order) of the statement in the list.', 'slim-seo-schema' ),
					],
				],
			],
		],
	],
	[
		'id'      => 'negativeNotes',
		'type'    => 'Group',
		'label'   => __( 'Negative notes (Cons)', 'slim-seo-schema' ),
		'tooltip' => __( 'A list of negative statements about the product, listed in a specific order', 'slim-seo-schema' ),
		'show'    => true,
		'fields'  => [
			[
				'id'       => '@type',
				'std'      => 'ItemList',
				'type'     => 'Hidden',
				'required' => true,
			],
			[
				'id'        => 'itemListElement',
				'type'      => 'Group',
				'cloneable' => true,
				'show'      => true,
				'fields'    => [
					[
						'id'       => '@type',
						'std'      => 'ListItem',
						'type'     => 'Hidden',
						'required' => true,
					],
					Helper::get_property( 'name', [
						'std'      => '',
						'tooltip'  => __( 'The key statement of the review.', 'slim-seo-schema' ),
						'required' => true,
					] ),
					[
						'id'      => 'position',
						'label'   => __( 'Position', 'slim-seo-schema' ),
						'show'    => true,
						'tooltip' => __( 'The position (order) of the statement in the list.', 'slim-seo-schema' ),
					],
				],
			],
		],
	],
	[
		'id'         => 'reviewRating',
		'label'      => __( 'Rating', 'slim-seo-schema' ),
		'dependency' => '[@type]:Review',
		'type'       => 'Group',
		'show'       => true,
		'fields'     => [
			[
				'id'       => '@type',
				'std'      => 'Rating',
				'type'     => 'Hidden',
				'required' => true,
			],
			[
				'id'       => 'ratingValue',
				'label'    => __( 'Rating value', 'slim-seo-schema' ),
				'required' => true,
				'std'      => 5,
			],
			[
				'id'      => 'bestRating',
				'label'   => __( 'Best rating', 'slim-seo-schema' ),
				'type'    => 'DataList',
				'std'     => 5,
				'options' => [
					1 => 1,
					2 => 2,
					3 => 3,
					4 => 4,
					5 => 5,
				],
			],
			[
				'id'      => 'worstRating',
				'label'   => __( 'Worst rating', 'slim-seo-schema' ),
				'type'    => 'DataList',
				'std'     => 1,
				'options' => [
					1 => 1,
					2 => 2,
					3 => 3,
					4 => 4,
					5 => 5,
				],
			],
		],
	],
	[
		'id'         => 'ratingCount',
		'label'      => __( 'Rating count', 'slim-seo-schema' ),
		'dependency' => '[@type]:AggregateRating',
		'tooltip'    => __( 'The total number of ratings for the item on your site.', 'slim-seo-schema' ),
		'required'   => true,
	],
	[
		'id'         => 'ratingValue',
		'dependency' => '[@type]:AggregateRating',
		'label'      => __( 'Rating value', 'slim-seo-schema' ),
		'tooltip'    => __( 'A numerical quality rating for the organization, either a number, fraction, or percentage (for exp. "4", "60%", or "6 / 10").', 'slim-seo-schema' ),
		'required'   => true,
	],
	[
		'id'         => 'reviewCount',
		'dependency' => '[@type]:AggregateRating',
		'label'      => __( 'Review count', 'slim-seo-schema' ),
		'tooltip'    => __( 'Specifies the number of people who provided a review with or without an accompanying rating. At least one of ratingCount or reviewCount is required.', 'slim-seo-schema' ),
		'required'   => true,
	],
	[
		'id'      => 'reviewBody',
		'label'   => __( 'Review body', 'slim-seo-schema' ),
		'show'    => true,
		'tooltip' => __( 'The actual body of the review.', 'slim-seo-schema' ),
	],
	[
		'id'         => 'bestRating',
		'label'      => __( 'Best rating', 'slim-seo-schema' ),
		'dependency' => '[@type]:AggregateRating',
		'type'       => 'DataList',
		'tooltip'    => __( 'The highest value allowed in this rating system.', 'slim-seo-schema' ),
		'std'        => 5,
		'options'    => [
			1 => 1,
			2 => 2,
			3 => 3,
			4 => 4,
			5 => 5,
		],
	],
	[
		'id'         => 'worstRating',
		'label'      => __( 'Worst rating', 'slim-seo-schema' ),
		'dependency' => '[@type]:AggregateRating',
		'type'       => 'DataList',
		'tooltip'    => __( 'The lowest value allowed in this rating system.', 'slim-seo-schema' ),
		'std'        => 1,
		'options'    => [
			1 => 1,
			2 => 2,
			3 => 3,
			4 => 4,
			5 => 5,
		],
	],
	[
		'id'    => 'datePublished',
		'label' => __( 'Date published', 'slim-seo-schema' ),
		'show'  => true,
		'std'   => '{{ post.date }}',
		'type'  => 'Date',
	],
];
