<?php
namespace SlimSEOPro\Schema\Renderer;

use SlimSEOPro\Schema\Location\Validator;
use SlimSEOPro\Schema\Settings;
use SlimSEOPro\Schema\Support\Data;
use SlimSEO\Breadcrumbs;
use SlimSEOPro\Schema\Support\Arr;

abstract class Base {
	private $data = [];

	public function get_graph(): array {
		$schemas = $this->get_schemas();
		if ( ! $schemas ) {
			return [];
		}
		$this->get_data();

		return $this->render( $schemas );
	}

	private function get_schemas() {
		$schemas = [];
		if ( is_singular() ) {
			$schemas = $this->get_from_post();
		}
		if ( empty( $schemas ) ) {
			$schemas = $this->get_from_settings();
		}

		$links = new Links( $schemas );
		return $links->get_active();
	}

	private function get_from_post() {
		$schema = get_post_meta( get_the_ID(), 'slim_seo_schema', true );
		return empty( $schema ) ? [] : [ $schema ];
	}

	private function get_from_settings() {
		$schemas = Settings::get_schemas();
		return array_filter( $schemas, [ $this, 'validate' ] );
	}

	private function render( array $schemas ): array {
		$rendered = [];

		foreach ( $schemas as $schema ) {
			$renderer   = new Schema( $schema['type'], $schema['fields'], $this->data );
			$rendered[] = $renderer->render();
		}

		return array_filter( $rendered );
	}

	private function validate( $schema ) {
		if ( empty( $schema['location'] ) ) {
			return false;
		}
		$validator = new Validator( $schema['location'] );
		return $validator->validate();
	}

	private function get_data() {
		$this->data = array_merge(
			[ 'schemas' => $this->get_schema_links() ],
			[ 'post' => $this->get_post_data() ],
			[ 'author' => $this->get_author_data() ],
			[ 'user' => $this->get_user_data() ],
			[ 'site' => $this->get_site_data() ],
			[ 'current' => $this->get_current_data() ],
		);
		$this->data = apply_filters( 'slim_seo_schema_data', $this->data );

		// Truncate the post content and set word count.
		$post_content = Normalizer::normalize( Arr::get( $this->data, 'post.content', '' ) );
		Arr::set( $this->data, 'post.content', $this->truncate( $post_content ) );
		Arr::set( $this->data, 'post.word_count', str_word_count( $post_content ) );
	}

	private function get_schema_links() {
		$schemas = Settings::get_schemas();
		if ( ! $schemas ) {
			return [];
		}
		$data = [];
		foreach ( $schemas as $schema ) {
			// Bypass breadcrumbs on homepage cause it's always empty.
			if ( is_front_page() && $schema['type'] === 'BreadcrumbList' ) {
				continue;
			}

			$id          = PropParser::get_id( $schema );
			$data[ $id ] = [
				'@id' => PropParser::get_id_value( $id ),
			];
		}

		return $data;
	}

	private function get_post_data(): array {
		$post = is_singular() ? get_queried_object() : get_post();
		if ( empty( $post ) ) {
			return [];
		}

		$post_tax   = [];
		$taxonomies = Data::get_taxonomies();
		foreach ( $taxonomies as $taxonomy ) {
			$post_tax[ $taxonomy['slug'] ] = $this->get_post_terms( $post, $taxonomy['slug'] );
		}

		return [
			'ID'            => $post->ID,
			'title'         => $post->post_title,
			'excerpt'       => $post->post_excerpt,
			'content'       => $post->post_content,
			'url'           => get_permalink( $post ),
			'slug'          => $post->post_name,
			'date'          => gmdate( 'c', strtotime( $post->post_date_gmt ) ),
			'modified_date' => gmdate( 'c', strtotime( $post->post_modified_gmt ) ),
			'thumbnail'     => get_the_post_thumbnail_url( $post->ID, 'full' ),
			'comment_count' => (int) $post->comment_count,
			'tags'          => $this->get_post_terms( $post, 'post_tag' ),
			'categories'    => $this->get_post_terms( $post, 'category' ),
			'custom_field'  => $this->get_custom_field_data(),
			'tax'           => $post_tax,
		];
	}

	private function truncate( string $text, int $limit = 160 ): string {
		return mb_substr( $text, 0, $limit );
	}

	private function get_user_data() {
		return $this->get_user( get_current_user_id() );
	}

	private function get_author_data() {
		return $this->get_user( get_the_author_meta( 'ID' ) );
	}

	private function get_user( $user_id ) {
		$user = get_userdata( $user_id );
		if ( ! $user ) {
			return [];
		}
		return [
			'ID'           => $user->ID,
			'first_name'   => $user->first_name,
			'last_name'    => $user->last_name,
			'display_name' => $user->display_name,
			'login'        => $user->user_login,
			'nickname'     => $user->nickname,
			'email'        => $user->user_email,
			'url'          => $user->user_url,
			'nicename'     => $user->user_nicename,
			'description'  => $user->description,
			'posts_url'    => get_author_posts_url( $user->ID ),
			'avatar'       => get_avatar_url( $user->ID ),
		];
	}

	private function get_site_data() {
		return [
			'title'       => get_bloginfo( 'name' ),
			'description' => get_bloginfo( 'description' ),
			'url'         => home_url( '/' ),
			'language'    => get_locale(),
			'icon'        => get_site_icon_url(),
		];
	}

	private function get_post_terms( $post, $taxonomy ) {
		$terms = get_the_terms( $post, $taxonomy );
		return is_wp_error( $terms ) ? [] : wp_list_pluck( $terms, 'name' );
	}

	private function get_custom_field_data() {
		$post        = is_singular() ? get_queried_object() : get_post();
		$meta_values = get_post_meta( $post->ID );
		$data        = [];
		foreach ( $meta_values as $key => $value ) {
			$data[ $key ] = reset( $value );
		}
		return $data;
	}

	private function get_current_data(): array {
		global $wp;

		$url = add_query_arg( [], $wp->request );
		$url = home_url( $url );
		$url = strtok( $url, '#' );
		$url = strtok( $url, '?' );

		$breadcrumbs = new Breadcrumbs;
		$breadcrumbs->parse();
		$links = $breadcrumbs->get_links();
		$list  = [];
		foreach ( $links as $i => $link ) {
			$list[] = [
				'@type'    => 'ListItem',
				'position' => ( $i + 1 ),
				'name'     => $link['text'],
				'item'     => $link['url'],
			];
		}

		return [
			'url'         => $url,
			'title'       => wp_get_document_title(),
			'breadcrumbs' => $list,
		];
	}
}
