{"name": "elightup/slim-seo-schema", "type": "wordpress-plugin", "repositories": [{"type": "git", "url": "**************:elightup/plugin-updater.git"}, {"type": "git", "url": "https://github.com/elightup/twig"}, {"type": "git", "url": "**************:elightup/plugin-search.git"}], "autoload": {"psr-4": {"SlimSEOPro\\Schema\\": "src/", "SlimSEO\\": "lib"}}, "minimum-stability": "dev", "prefer-stable": true, "config": {"prepend-autoloader": false}, "extra": {"dev-files": {"/": [".git", ".github/", ".gitattributes", ".giti<PERSON>re", ".editoconfig", "LICENSE", "*.md", "composer.*", "phpcs.xml", "test/", "tests/"], "elightup/plugin-updater": ["plugin-updater.php"], "elightup/plugin-search": ["plugin-search.php"], "elightup/*": [".git/", "*.json", "twig.php", "vendor/"]}}, "require": {"elightup/plugin-updater": "dev-master", "elightup/twig": "dev-master", "elightup/plugin-search": "dev-master"}}