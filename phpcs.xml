<?xml version="1.0"?>
<ruleset name="eLightUp WordPress Coding Standards">
	<!-- See https://github.com/squizlabs/PHP_CodeSniffer/wiki/Annotated-ruleset.xml -->
	<!-- See https://github.com/WordPress/WordPress-Coding-Standards -->
	<!-- See https://github.com/PHPCompatibility/PHPCompatibilityWP -->

	<!--
	#############################################################################
	COMMAND LINE ARGUMENTS
	https://github.com/squizlabs/PHP_CodeSniffer/wiki/Annotated-ruleset.xml
	#############################################################################
	-->

	<!-- Pass some flags to PHPCS:
		 p flag: Show progress of the run.
		 s flag: Show sniff codes in all reports.
	-->
	<arg value="ps"/>


	<!-- Check up to 8 files simultaneously. -->
	<arg name="parallel" value="8"/>

	<!-- Check PHP files only. JavaScript and CSS files are checked separately using the @wordpress/scripts package. -->
	<arg name="extensions" value="php"/>

	<!-- Check all files in this directory and the directories below it. -->
	<file>.</file>

	<!-- Exclude patterns. -->
	<exclude-pattern>/vendor/*</exclude-pattern>
	<exclude-pattern>/tests/*</exclude-pattern>
	<exclude-pattern>/.github/*</exclude-pattern>

	<rule ref="WordPress">
		<!-- Use PSR-4 naming -->
		<exclude name="WordPress.Files.FileName.NotHyphenatedLowercase" />
		<exclude name="WordPress.Files.FileName.InvalidClassFileName" />

		<!-- Only comment when neccessary -->
		<exclude name="Squiz.Commenting.FileComment.Missing" />
		<exclude name="Squiz.Commenting.FileComment.MissingPackageTag" />
		<exclude name="Squiz.Commenting.ClassComment.Missing" />
		<exclude name="Squiz.Commenting.FunctionComment.Missing" />
		<exclude name="Squiz.Commenting.FunctionComment.MissingParamTag" />
		<exclude name="Squiz.Commenting.VariableComment.Missing" />
		<exclude name="Squiz.Commenting.InlineComment.InvalidEndChar" />

		<!-- Write shorter -->
		<exclude name="Universal.Arrays.DisallowShortArraySyntax.Found" />
		<exclude name="Generic.Arrays.DisallowShortArraySyntax.Found" />
		<exclude name="Generic.PHP.DisallowShortOpenTag.EchoFound" />
		<exclude name="Generic.Commenting.DocComment.SpacingBeforeTags" />
		<exclude name="Squiz.PHP.EmbeddedPhp.NoSemicolon" />
		<exclude name="WordPress.Classes.ClassInstantiation.MissingParenthesis" />
		<exclude name="Universal.Operators.DisallowShortTernary.Found" />
		<exclude name="WordPress.PHP.DisallowShortTernary.Found" />
		<exclude name="PSR12.Classes.ClassInstantiation.MissingParentheses" />

		<!-- Optional -->
		<exclude name="PEAR.Functions.FunctionCallSignature.ContentAfterOpenBracket" />
		<exclude name="PEAR.Functions.FunctionCallSignature.CloseBracketLine" />
		<exclude name="PEAR.Functions.FunctionCallSignature.MultipleArguments" />
		<exclude name="WordPress.PHP.YodaConditions.NotYoda" />
	</rule>

	<!-- Set the minimum supported WP version. This is used by several sniffs.
		 The minimum version set here should be in line with the minimum WP version
		 as set in the "Requires at least" tag in the readme.txt file. -->
	<config name="minimum_supported_wp_version" value="4.5"/>

	<!--
	#############################################################################
	USE THE PHPCompatibility RULESET
	#############################################################################
	-->
	<config name="testVersion" value="7.3-"/>
	<rule ref="PHPCompatibilityWP"/>
</ruleset>