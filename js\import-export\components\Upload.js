import { useState } from "@wordpress/element";
import { __ } from "@wordpress/i18n";

const Upload = () => {
	const [ file, setFile ] = useState();
	const [ loading, setLoading ] = useState( false );

	const handleChange = e => setFile( e.target.files[ 0 ] );

	const submit = () => {
		setLoading( true );

		const options = {
			method: 'POST',
			body: file,
			headers: { 'X-WP-Nonce': SSSchema.nonce }
		};

		fetch( `${ SSSchema.rest }/slim-seo-schema/import`, options )
			.then( response => response.json() )
			.then( response => {
				if ( response ) {
					location.reload();
				} else {
					alert( __( 'Invalid data format. Please try again.', 'slim-seo-schema' ) );
				}
			} );
	};

	return (
		<div className="sss-ie-upload">
			<input type="file" accept="*.json" onChange={ handleChange } />
			<button type="button" className="button-primary" onClick={ submit } disabled={ !file || loading } >
				{
					loading ? __( 'Submitting...', 'slim-seo-schema' ) : __( 'Submit', 'slim-seo-schema' )
				}
			</button>
		</div>
	);
};

export default Upload;