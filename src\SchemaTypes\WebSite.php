<?php
namespace SlimSEOPro\Schema\SchemaTypes;

return [
	[
		'id'       => '@id',
		'type'     => 'Hidden',
		'std'      => '{{ site.url }}#{{ id }}',
		'required' => true,
	],
	Helper::get_property( 'name', [
		'required' => true,
		'std'      => '{{ site.title }}',
		'tooltip'  => __( 'The name of the website.', 'slim-seo-schema' ),
	] ),
	[
		'id'      => 'author',
		'label'   => __( 'Author', 'slim-seo-schema' ),
		'tooltip' => __( 'The author of the website.', 'slim-seo-schema' ),
		'std'     => '{{ schemas.person }}',
	],
	[
		'id'      => 'about',
		'label'   => __( 'About', 'slim-seo-schema' ),
		'tooltip' => __( 'The subject matter of the content.', 'slim-seo-schema' ),
		'type'    => 'Group',
		'fields'  => [
			[
				'id'       => '@type',
				'std'      => 'Thing',
				'type'     => 'Hidden',
				'required' => true,
			],
			[
				'id'       => 'name',
				'required' => true,
			],
			Helper::get_property( 'description', [
				'tooltip' => __( 'A description of the item.', 'slim-seo-schema' ),
			] ),
			Helper::get_property( 'url', [
				'tooltip' => __( 'URL of the item.', 'slim-seo-schema' ),
			] ),
		],
	],
	[
		'id'      => 'abstract',
		'label'   => __( 'Abstract', 'slim-seo-schema' ),
		'tooltip' => __( 'An abstract is a short description that summarizes the website.', 'slim-seo-schema' ),
	],
	[
		'id'      => 'accessMode',
		'label'   => __( 'Access mode', 'slim-seo-schema' ),
		'tooltip' => __( 'The human sensory perceptual system or cognitive faculty through which a person may process or perceive information.', 'slim-seo-schema' ),
		'type'    => 'DataList',
		'options' => [
			'auditory'        => __( 'Auditory', 'slim-seo-schema' ),
			'chartOnVisual'   => __( 'Chart on visual', 'slim-seo-schema' ),
			'chemOnVisual'    => __( 'Chem on visual', 'slim-seo-schema' ),
			'colorDependent'  => __( 'Color dependent', 'slim-seo-schema' ),
			'diagramOnVisual' => __( 'Diagram on visual', 'slim-seo-schema' ),
			'mathOnVisual'    => __( 'Math on visual', 'slim-seo-schema' ),
			'musicOnVisual'   => __( 'Music on visual', 'slim-seo-schema' ),
			'tactile'         => __( 'Tactile', 'slim-seo-schema' ),
			'textOnVisual'    => __( 'Text on visual', 'slim-seo-schema' ),
			'textual'         => __( 'Textual', 'slim-seo-schema' ),
			'visual'          => __( 'Visual', 'slim-seo-schema' ),
		],
	],
	[
		'id'      => 'accessModeSufficient',
		'type'    => 'Group',
		'label'   => __( 'Access mode sufficient', 'slim-seo-schema' ),
		'tooltip' => __( 'A list of single or combined accessModes that are sufficient to understand all the intellectual content of a resource. Values should be drawn from the approved vocabulary.', 'slim-seo-schema' ),
		'fields'  => [
			[
				'id'       => '@type',
				'std'      => 'ItemList',
				'type'     => 'Hidden',
				'required' => true,
			],
			[
				'id'        => 'itemListElement',
				'type'      => 'Group',
				'cloneable' => true,
				'show'      => true,
				'fields'    => [
					[
						'id'       => '@type',
						'std'      => 'ListItem',
						'type'     => 'Hidden',
						'required' => true,
					],
					[
						'id'       => 'name',
						'label'    => __( 'Name', 'slim-seo-schema' ),
						'tooltip'  => __( 'The key statement of the review.', 'slim-seo-schema' ),
						'type'     => 'DataList',
						'required' => true,
						'options'  => [
							'auditory' => __( 'Auditory', 'slim-seo-schema' ),
							'tactile'  => __( 'Tactile', 'slim-seo-schema' ),
							'textual'  => __( 'Textual', 'slim-seo-schema' ),
							'visual'   => __( 'Visual', 'slim-seo-schema' ),
						],
					],
					[
						'id'      => 'position',
						'label'   => __( 'Position', 'slim-seo-schema' ),
						'show'    => true,
						'tooltip' => __( 'The position (order) of the statement in the list.', 'slim-seo-schema' ),
					],
				],
			],
		],
	],
	[
		'id'      => 'accessibilityAPI',
		'label'   => __( 'Accessibility API', 'slim-seo-schema' ),
		'tooltip' => __( 'Indicates that the resource is compatible with the referenced accessibility API.', 'slim-seo-schema' ),
		'type'    => 'DataList',
		'options' => [
			'AndroidAccessibility' => __( 'Android Accessibility', 'slim-seo-schema' ),
			'ATK'                  => __( 'ATK', 'slim-seo-schema' ),
			'AT-SPI'               => __( 'AT-SPI', 'slim-seo-schema' ),
			'FuchsiaAccessibility' => __( 'Fuchsia Accessibility', 'slim-seo-schema' ),
			'iAccessible2'         => __( 'iAccessible2', 'slim-seo-schema' ),
			'JavaAccessibility'    => __( 'Java Accessibility', 'slim-seo-schema' ),
			'MSAA'                 => __( 'MSAA', 'slim-seo-schema' ),
			'NSAccessibility'      => __( 'NS Accessibility', 'slim-seo-schema' ),
			'UIAccessibility'      => __( 'UI Accessibility', 'slim-seo-schema' ),
			'UIAutomation'         => __( 'UI Automation', 'slim-seo-schema' ),
		],
	],
	[
		'id'      => 'accessibilityControl',
		'label'   => __( 'Accessibility control', 'slim-seo-schema' ),
		'tooltip' => __( 'Identifies input methods that are sufficient to fully control the described resource.', 'slim-seo-schema' ),
		'type'    => 'DataList',
		'options' => [
			'fullKeyboardControl' => __( 'Full Keyboard Control', 'slim-seo-schema' ),
			'fullMouseControl'    => __( 'Full Mouse Control', 'slim-seo-schema' ),
			'fullSwitchControl'   => __( 'Full Switch Control', 'slim-seo-schema' ),
			'fullTouchControl'    => __( 'Full Touch Control', 'slim-seo-schema' ),
			'fullVideoControl'    => __( 'Full Video Control', 'slim-seo-schema' ),
			'fullVoiceControl'    => __( 'Full Voice Control', 'slim-seo-schema' ),
		],
	],
	[
		'id'      => 'accessibilityFeature',
		'label'   => __( 'Accessibility feature', 'slim-seo-schema' ),
		'tooltip' => __( 'Content features of the resource, such as accessible media, alternatives and supported enhancements for accessibility.', 'slim-seo-schema' ),
		'type'    => 'DataList',
		'options' => [
			[
				'label'   => __( 'Structure and Navigation Terms', 'slim-seo-schema' ),
				'options' => [
					'annotations'          => __( 'Annotations', 'slim-seo-schema' ),
					'ARIA'                 => __( 'ARIA', 'slim-seo-schema' ),
					'index'                => __( 'Index', 'slim-seo-schema' ),
					'pageBreakMarkers'     => __( 'Page Break Markers', 'slim-seo-schema' ),
					'pageNavigation'       => __( 'Page Navigation', 'slim-seo-schema' ),
					'readingOrder'         => __( 'Reading Order', 'slim-seo-schema' ),
					'structuralNavigation' => __( 'Structural Navigation', 'slim-seo-schema' ),
					'tableOfContents'      => __( 'Table Of Contents', 'slim-seo-schema' ),
					'taggedPDF'            => __( 'Tagged PDF', 'slim-seo-schema' ),
				],
			],
			[
				'label'   => __( 'Adaptation Terms', 'slim-seo-schema' ),
				'options' => [
					'alternativeText'  => __( 'Alternative Text', 'slim-seo-schema' ),
					'audioDescription' => __( 'Audio Description', 'slim-seo-schema' ),
					'closedCaptions'   => __( 'Closed Captions', 'slim-seo-schema' ),
					'describedMath'    => __( 'Described Math', 'slim-seo-schema' ),
					'longDescription'  => __( 'Long Description', 'slim-seo-schema' ),
					'rubyAnnotations'  => __( 'Ruby Annotations', 'slim-seo-schema' ),
					'signLanguage'     => __( 'Sign Language', 'slim-seo-schema' ),
					'transcript'       => __( 'Transcript', 'slim-seo-schema' ),
				],
			],
			[
				'label'   => __( 'Rendering Control Terms', 'slim-seo-schema' ),
				'options' => [
					'displayTransformability' => __( 'Display Transformability', 'slim-seo-schema' ),
					'synchronizedAudioText'   => __( 'Synchronized Audio Text', 'slim-seo-schema' ),
					'timingControl'           => __( 'Timing Control', 'slim-seo-schema' ),
					'unlocked'                => __( 'Unlocked', 'slim-seo-schema' ),
				],
			],
			[
				'label'   => __( 'Specialized Markup Terms', 'slim-seo-schema' ),
				'options' => [
					'ChemML'    => __( 'ChemML', 'slim-seo-schema' ),
					'latex'     => __( 'Latex', 'slim-seo-schema' ),
					'MathML'    => __( 'MathML', 'slim-seo-schema' ),
					'ttsMarkup' => __( 'TTS Markup', 'slim-seo-schema' ),
				],
			],
			[
				'label'   => __( 'Clarity Terms', 'slim-seo-schema' ),
				'options' => [
					'highContrastAudio'   => __( 'High Contrast Audio', 'slim-seo-schema' ),
					'highContrastDisplay' => __( 'High Contrast Display', 'slim-seo-schema' ),
					'largePrint'          => __( 'Large Print', 'slim-seo-schema' ),
				],
			],
			[
				'label'   => __( 'Tactile Terms', 'slim-seo-schema' ),
				'options' => [
					'braille'        => __( 'Braille', 'slim-seo-schema' ),
					'tactileGraphic' => __( 'Tactile Graphic', 'slim-seo-schema' ),
					'tactileObject'  => __( 'Tactile Object', 'slim-seo-schema' ),
				],
			],
		],
	],
	[
		'id'      => 'accessibilityHazard',
		'label'   => __( 'Accessibility hazard', 'slim-seo-schema' ),
		'tooltip' => __( 'A characteristic of the described resource that is physiologically dangerous to some users. Related to WCAG 2.0 guideline 2.3.', 'slim-seo-schema' ),
		'type'    => 'DataList',
		'options' => [
			'flashing'                 => __( 'Flashing', 'slim-seo-schema' ),
			'noFlashingHazard'         => __( 'No Flashing Hazard', 'slim-seo-schema' ),
			'motionSimulation'         => __( 'Motion Simulation', 'slim-seo-schema' ),
			'noMotionSimulationHazard' => __( 'No Motion SimulationHazard', 'slim-seo-schema' ),
			'sound'                    => __( 'Sound', 'slim-seo-schema' ),
			'noSoundHazard'            => __( 'No Sound Hazard', 'slim-seo-schema' ),
			'unknown'                  => __( 'Unknown', 'slim-seo-schema' ),
			'none'                     => __( 'None', 'slim-seo-schema' ),
		],
	],
	Helper::get_property( 'url', [
		'id'      => 'archivedAt',
		'label'   => __( 'Archived at', 'slim-seo-schema' ),
		'tooltip' => __( 'Indicates a page or other link involved in archival of the website.', 'slim-seo-schema' ),
	] ),
	[
		'id'      => 'contentLocation',
		'label'   => __( 'Content location', 'slim-seo-schema' ),
		'type'    => 'Group',
		'tooltip' => __( 'The location depicted or described in the content. For example, the location in a photograph or painting.', 'slim-seo-schema' ),
		'fields'  => [
			[
				'id'       => '@type',
				'std'      => 'Place',
				'type'     => 'Hidden',
				'required' => true,
			],
			Helper::get_property( 'address', [
				'label'            => '',
				'required'         => true,
				'cloneable'        => true,
				'cloneItemHeading' => __( 'Address', 'slim-seo-schema' ),
			] ),
		],
	],
	[
		'id'          => 'contributor',
		'label'       => __( 'Contributor', 'slim-seo-schema' ),
		'std'         => '{{ schemas.person }}',
		'tooltip'     => __( 'A secondary contributor to the page', 'slim-seo-schema' ),
		'description' => __( 'Please create a Person or an Organization schema and link to this property via a dynamic variable', 'slim-seo-schema' ),
	],
	[
		'id'          => 'copyrightHolder',
		'label'       => __( 'Copyright holder', 'slim-seo-schema' ),
		'std'         => '{{ schemas.person }}',
		'tooltip'     => __( 'The party holding the legal copyright to the page.', 'slim-seo-schema' ),
		'description' => __( 'Please create a Person or an Organization schema and link to this property via a dynamic variable', 'slim-seo-schema' ),
	],
	[
		'id'      => 'copyrightYear',
		'label'   => __( 'Copyright year', 'slim-seo-schema' ),
		'tooltip' => __( 'The year during which the claimed copyright for the website was first asserted.', 'slim-seo-schema' ),
	],
	Helper::get_property( 'countryOfOrigin' ),
	Helper::get_property( 'dateCreated', [
		'tooltip' => __( 'Date the website was created, in ISO 8601 format.', 'slim-seo-schema' ),
		'std'     => '{{ post.date }}',
	] ),
	Helper::get_property( 'datePublished', [
		'tooltip' => __( 'Date of first broadcast/publication, in ISO 8601 format', 'slim-seo-schema' ),
		'std'     => '{{ post.date }}',
	] ),
	[
		'id'      => 'description',
		'show'    => true,
		'label'   => __( 'Description', 'slim-seo-schema' ),
		'tooltip' => __( 'A description of the webpage.', 'slim-seo-schema' ),
		'std'     => '{{ site.description }}',
	],
	[
		'id'      => 'expires',
		'label'   => __( 'Expires', 'slim-seo-schema' ),
		'tooltip' => __( 'Date the content expires and is no longer useful or available, in ISO 8601 format. Don\'t supply this information if the website does not expire.', 'slim-seo-schema' ),
	],
	[
		'id'      => 'inLanguage',
		'label'   => __( 'In language', 'slim-seo-schema' ),
		'tooltip' => __( 'The language of the content or performance or used in an action. Please use one of the language codes from the IETF BCP 47 standard.', 'slim-seo-schema' ),
		'std'     => 'en',
	],
	[
		'label'   => __( 'Keywords', 'slim-seo-schema' ),
		'id'      => 'keywords',
		'tooltip' => __( 'Keywords or tags used to describe the website. Multiple textual entries in a keywords list are typically delimited by commas.', 'slim-seo-schema' ),
	],
	[
		'id'    => 'potentialAction',
		'label' => __( 'Potential action', 'slim-seo-schema' ),
		'std'   => '{{ schemas.searchaction }}',
		'show'  => true,
	],
	[
		'id'      => 'publisher',
		'label'   => __( 'Publisher', 'slim-seo-schema' ),
		'std'     => '{{ schemas.organization }}',
		'tooltip' => __( 'The publisher of the website.', 'slim-seo-schema' ),
		'show'    => true,
	],
	[
		'label'   => __( 'Issn', 'slim-seo-schema' ),
		'id'      => 'issn',
		'type'    => 'Text',
		'tooltip' => __( 'The International Standard Serial Number (ISSN) that identifies this serial publication. You can repeat this property to identify different formats of, or the linking ISSN (ISSN-L) for, this serial publication.', 'slim-seo-schema' ),
	],
	[
		'id'      => 'thumbnailUrl',
		'label'   => __( 'Thumbnail URL', 'slim-seo-schema' ),
		'tooltip' => __( 'A thumbnail image relevant to the website.', 'slim-seo-schema' ),
	],
	[
		'id'      => 'version',
		'label'   => __( 'Version', 'slim-seo-schema' ),
		'tooltip' => __( 'The version number of the website.', 'slim-seo-schema' ),
	],
	Helper::get_property( 'url', [
		'required' => true,
		'std'      => '{{ site.url }}',
	] ),
];
