(()=>{var x=Object.create;var u=Object.defineProperty;var y=Object.getOwnPropertyDescriptor;var N=Object.getOwnPropertyNames;var v=Object.getPrototypeOf,C=Object.prototype.hasOwnProperty;var b=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports);var I=(e,t,o,m)=>{if(t&&typeof t=="object"||typeof t=="function")for(let s of N(t))!C.call(e,s)&&s!==o&&u(e,s,{get:()=>t[s],enumerable:!(m=y(t,s))||m.enumerable});return e};var n=(e,t,o)=>(o=e!=null?x(v(e)):{},I(t||!e||!e.__esModule?u(o,"default",{value:e,enumerable:!0}):o,e));var p=b((U,f)=>{f.exports=wp.element});var h=b((j,S)=>{S.exports=wp.i18n});var r=n(p()),a=n(h());var d=n(p()),i=n(h()),E=()=>{let[e,t]=(0,d.useState)(),[o,m]=(0,d.useState)(!1);return React.createElement("div",{className:"sss-ie-upload"},React.createElement("input",{type:"file",accept:"*.json",onChange:l=>t(l.target.files[0])}),React.createElement("button",{type:"button",className:"button-primary",onClick:()=>{m(!0);let l={method:"POST",body:e,headers:{"X-WP-Nonce":SSSchema.nonce}};fetch(`${SSSchema.rest}/slim-seo-schema/import`,l).then(c=>c.json()).then(c=>{c?location.reload():alert((0,i.__)("Invalid data format. Please try again.","slim-seo-schema"))})},disabled:!e||o},o?(0,i.__)("Submitting...","slim-seo-schema"):(0,i.__)("Submit","slim-seo-schema")))},g=E;var _=()=>{let[e,t]=(0,r.useReducer)(o=>!o,!1);return React.createElement(React.Fragment,null,React.createElement("h3",null,(0,a.__)("Schema Import and Export","slim-seo-schema")),React.createElement("p",null,(0,a.__)("This tool helps you to export existing schemas to JSON file for backup and import them on other sites or restore on this site.","slim-seo-schema")),React.createElement("div",{className:"sss-ie-buttons"},React.createElement("a",{className:"button",href:SSSchemaIE.exportUrl},(0,a.__)("Export","slim-seo-schema")),React.createElement("button",{type:"button",className:"button",onClick:t},(0,a.__)("Import","slim-seo-schema"))),e&&React.createElement(g,null))};(0,r.render)(React.createElement(_,null),document.getElementById("ssschema-import-export"));})();
