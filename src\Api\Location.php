<?php
namespace SlimSEOPro\Schema\Api;

use WP_REST_Server;
use WP_REST_Request;

class Location extends Base {
	public function register_routes() {
		$params = [
			'method'              => WP_REST_Server::READABLE,
			'permission_callback' => [ $this, 'has_permission' ],
			'args'                => [
				'name'     => [
					'sanitize_callback' => 'sanitize_text_field',
				],
				'term'     => [
					'sanitize_callback' => 'sanitize_text_field',
				],
				'selected' => [
					'sanitize_callback' => 'sanitize_text_field',
				],
			],
		];
		register_rest_route( 'slim-seo-schema', 'terms', array_merge( $params, [
			'callback' => [ $this, 'get_terms' ],
		] ) );
		register_rest_route( 'slim-seo-schema', 'posts', array_merge( $params, [
			'callback' => [ $this, 'get_posts' ],
		] ) );
	}

	public function get_terms( WP_REST_Request $request ) {
		$search_term        = $request->get_param( 'term' );
		$name               = $request->get_param( 'name' );
		list( , $taxonomy ) = explode( ':', $name );

		$field = [
			'query_args' => [
				'taxonomy'   => $taxonomy,
				'name__like' => $search_term,
				'orderby'    => 'name',
				'number'     => 10,
			],
		];
		$data  = $this->query_terms( null, $field );
		$data  = array_values( $data );

		return apply_filters( 'slim_seo_schema_location_terms', $data, $request );
	}

	public function get_posts( WP_REST_Request $request ) {
		$search_term       = $request->get_param( 'term' );
		$name              = $request->get_param( 'name' );
		list( $post_type ) = explode( ':', $name );

		$field = [
			'query_args' => [
				's'              => $search_term,
				'post_type'      => $post_type,
				'post_status'    => 'any',
				'posts_per_page' => 10,
				'orderby'        => 'post_title',
				'order'          => 'ASC',
			],
		];
		$data  = $this->query_posts( null, $field );
		$data  = array_values( $data );

		return apply_filters( 'slim_seo_schema_location_posts', $data, $request );

	}

	/**
	 * Query posts for field options.
	 *
	 * @param  array $meta  Saved meta value.
	 * @param  array $field Field settings.
	 * @return array        Field options array.
	 */
	private function query_posts( $meta, $field ) {
		$args = wp_parse_args(
			$field['query_args'],
			array(
				'no_found_rows'          => true,
				'update_post_meta_cache' => false,
				'update_post_term_cache' => false,
			)
		);

		// Query only selected items.
		if ( ! empty( $field['ajax'] ) && ! empty( $meta ) ) {
			$args['posts_per_page'] = count( $meta );
			$args['post__in']       = $meta;
		}

		// Get from cache to prevent same queries.
		$last_changed = wp_cache_get_last_changed( 'posts' );
		$key          = md5( serialize( $args ) );
		$cache_key    = "$key:$last_changed";
		$options      = wp_cache_get( $cache_key, 'meta-box-post-field' );

		if ( false !== $options ) {
			return $options;
		}

		$query   = new \WP_Query( $args );
		$options = array();
		foreach ( $query->posts as $post ) {
			$label                = $post->post_title ? $post->post_title : __( '(No title)', 'meta-box' );
			$options[ $post->ID ] = array(
				'value'  => $post->ID,
				'label'  => $label,
				'parent' => $post->post_parent,
			);
		}

		// Cache the query.
		wp_cache_set( $cache_key, $options, 'meta-box-post-field' );

		return $options;
	}

	/**
	 * Query terms for field options.
	 *
	 * @param  array $meta  Saved meta value.
	 * @param  array $field Field settings.
	 * @return array        Field options array.
	 */
	private function query_terms( $meta, $field ) {
		$args = wp_parse_args(
			$field['query_args'],
			array(
				'hide_empty'             => false,
				'count'                  => false,
				'update_term_meta_cache' => false,
			)
		);

		// Query only selected items.
		if ( ! empty( $field['ajax'] ) && ! empty( $meta ) ) {
			$args['include'] = $meta;
		}

		$terms = get_terms( $args );
		if ( ! is_array( $terms ) ) {
			return array();
		}
		$options = array();
		foreach ( $terms as $term ) {
			$label                     = $term->name ? $term->name : __( '(No title)', 'meta-box' );
			$options[ $term->term_id ] = array(
				'value'  => $term->term_id,
				'label'  => $label,
				'parent' => $term->parent,
			);
		}
		return $options;
	}
}
