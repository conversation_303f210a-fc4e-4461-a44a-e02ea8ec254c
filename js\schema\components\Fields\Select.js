import { Select as S } from "@elightup/form";
import { memo, useEffect } from "@wordpress/element";

const Select = ( { className, property, setProperties } ) => {
	const { id, label, required, std, tooltip, options } = property;

	const setFields = value => {
		if ( !property.dependant ) {
			return;
		}
		setProperties( prev => prev.map( item => {
			if ( !item.dependency ) {
				return item;
			}
			const dependency = item.dependency.split( ':' )[ 1 ];
			const hidden = dependency !== value;

			return { ...item, hidden };
		} ) );
	};

	useEffect( () => {
		setFields( std );
	}, [] );

	return (
		<S
			className={ className }
			label={ label }
			id={ id }
			name={ id }
			value={ std }
			required={ required }
			options={ options }
			tooltip={ tooltip }
			onChange={ e => setFields( e.target.value ) }
		/>
	);
};

export default memo( Select, ( prevProps, nextProps ) => prevProps.property.id === nextProps.property.id );
