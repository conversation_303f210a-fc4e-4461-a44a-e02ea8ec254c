<?php
namespace SlimSEOPro\Schema\Integrations;

use DateTime;
use WP_REST_Request;

class WooCommerce {
	public function __construct() {
		add_action( 'init', [ $this, 'init' ] );
	}

	public function init() {
		if ( ! function_exists( 'WC' ) ) {
			return;
		}

		add_action( 'wp_footer', [ $this, 'remove_woocommerce_schema' ], 0 );

		add_filter( 'slim_seo_schema_variables', [ $this, 'add_variables' ] );
		add_filter( 'slim_seo_schema_data', [ $this, 'add_data' ] );

		// Add product type location rules.
		add_filter( 'slim_seo_schema_locations', [ $this, 'add_locations' ] );
		add_filter( 'slim_seo_schema_location_terms', [ $this, 'add_location_terms' ], 10, 2 );
		add_filter( 'slim_seo_schema_location_validate_singular', [ $this, 'validate_location' ], 10, 2 );
	}

	public function remove_woocommerce_schema() {
		remove_action( 'wp_footer', [ WC()->structured_data, 'output_structured_data' ] );
	}

	public function add_variables( $variables ) {
		$variables[] = [
			'label'   => 'WooCommerce',
			'options' => [
				'product.price'          => __( 'Price', 'slim-seo-schema' ),
				'product.price_with_tax' => __( 'Price including tax', 'slim-seo-schema' ),
				'product.low_price'      => __( 'Low price (variable product)', 'slim-seo-schema' ),
				'product.high_price'     => __( 'High price (variable product)', 'slim-seo-schema' ),
				'product.offer_count'    => __( 'Offer count (variable product)', 'slim-seo-schema' ),
				'product.sale_from'      => __( 'Sale price date "From"', 'slim-seo-schema' ),
				'product.sale_to'        => __( 'Sale price date "To"', 'slim-seo-schema' ),
				'product.sku'            => __( 'SKU', 'slim-seo-schema' ),
				'product.stock'          => __( 'Stock status', 'slim-seo-schema' ),
				'product.currency'       => __( 'Currency', 'slim-seo-schema' ),
				'product.rating'         => __( 'Rating value', 'slim-seo-schema' ),
				'product.review_count'   => __( 'Review count', 'slim-seo-schema' ),
			],
		];

		return $variables;
	}

	public function add_data( $data ) {
		$post    = is_singular() ? get_queried_object() : get_post();
		$product = wc_get_product( $post );

		if ( empty( $product ) ) {
			return $data;
		}

		$price          = $product->get_price();
		$price_with_tax = wc_get_price_including_tax( $product, [ 'price' => $price ] );

		$sale_from = '';
		if ( $product->get_date_on_sale_from() ) {
			$sale_from = gmdate( 'Y-m-d', $product->get_date_on_sale_from()->getTimestamp() );
		}

		// By default, set the sale price is today + 1 month.
		$today   = gmdate( 'Y-m-d' );
		$sale_to = gmdate( 'Y-m-d', wc_string_to_timestamp( '+1 month' ) );

		// Sale already started.
		if ( $product->is_on_sale() ) {
			if ( $product->get_date_on_sale_to() ) {
				$sale_to = gmdate( 'Y-m-d', $product->get_date_on_sale_to()->getTimestamp() );
			}
		} else {
			// Sale hasn't started yet, so the regular price will be available until sale!
			if ( $sale_from > $today ) {
				$sale_to = gmdate( 'Y-m-d', wc_string_to_timestamp( $sale_from ) - DAY_IN_SECONDS );
			} elseif ( $sale_from === $today ) {
				$sale_to = $today;
			}
		}

		$low_price   = '';
		$high_price  = '';
		$offer_count = 0;
		if ( $product->is_type( 'variable' ) ) {
			$low_price   = $product->get_variation_price( 'min', false );
			$low_price   = wc_get_price_including_tax( $product, [ 'price' => $low_price ] );
			$high_price  = $product->get_variation_price( 'max', false );
			$high_price  = wc_get_price_including_tax( $product, [ 'price' => $high_price ] );
			$offer_count = count( $product->get_children() );
		}

		$sku          = $product->get_sku();
		$currency     = get_woocommerce_currency();
		$rating       = $product->get_average_rating();
		$review_count = $product->get_review_count();

		$statuses = [
			// WooCommerce built-in statuses.
			'instock'              => 'InStock',
			'outofstock'           => 'OutOfStock',
			'onbackorder'          => 'BackOrder',

			// Developers can register product custom stock statuses (supported by Google) with variations.
			'discontinued'         => 'Discontinued',

			'instoreonly'          => 'InStoreOnly',
			'in_store_only'        => 'InStoreOnly',
			'in-store-only'        => 'InStoreOnly',

			'limitedavailability'  => 'LimitedAvailability',
			'limited_availability' => 'LimitedAvailability',
			'limited-availability' => 'LimitedAvailability',

			'onlineonly'           => 'OnlineOnly',
			'online_only'          => 'OnlineOnly',
			'online-only'          => 'OnlineOnly',

			'preorder'             => 'PreOrder',
			'pre_order'            => 'PreOrder',
			'pre-order'            => 'PreOrder',

			'presale'              => 'PreSale',
			'pre_sale'             => 'PreSale',
			'pre-sale'             => 'PreSale',

			'soldout'              => 'SoldOut',
			'sold_out'             => 'SoldOut',
			'sold-out'             => 'SoldOut',
		];
		$status   = strtolower( $product->get_stock_status() );
		$status   = $statuses[ $status ] ?? 'InStock';
		$stock    = "https://schema.org/$status";

		$data['product'] = compact(
			'price',
			'price_with_tax',
			'low_price',
			'high_price',
			'offer_count',
			'sale_from',
			'sale_to',
			'sku',
			'stock',
			'currency',
			'rating',
			'review_count'
		);

		return $data;
	}

	public function add_locations( $locations ) {
		$singular = &$locations['singularLocations'];
		if ( empty( $singular['product'] ) ) {
			return;
		}

		$singular['product']['options'][] = [
			'value' => 'product:type',
			'label' => __( 'Type', 'slim-seo-schema' ),
		];

		return $locations;
	}

	public function add_location_terms( array $data, WP_REST_Request $request ) : array {
		return 'product:type' !== $request->get_param( 'name' ) ? $data : [
			[
				'value' => 'simple',
				'label' => __( 'Simple', 'slim-seo-schema' ),
			],
			[
				'value' => 'variable',
				'label' => __( 'Variable', 'slim-seo-schema' ),
			],
			[
				'value' => 'grouped',
				'label' => __( 'Grouped', 'slim-seo-schema' ),
			],
			[
				'value' => 'external',
				'label' => __( 'External/Affiliate', 'slim-seo-schema' ),
			],
		];
	}

	public function validate_location( bool $result, array $rule ) : bool {
		if ( ! is_singular( 'product' ) || $rule['name'] !== 'product:type' ) {
			return $result;
		}

		$product = wc_get_product( get_queried_object() );
		return $product->get_type() === $rule['value'];
	}
}
