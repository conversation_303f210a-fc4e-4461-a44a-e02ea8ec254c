<?php
namespace SlimSEOPro\Schema;

class Settings {
	public const OPTION_NAME = 'slim_seo_schemas';

	public function __construct() {
		add_filter( 'slim_seo_settings_tabs', [ $this, 'add_tab' ] );
		add_filter( 'slim_seo_settings_panes', [ $this, 'add_pane' ] );
		add_action( 'admin_print_styles-settings_page_slim-seo', [ $this, 'enqueue' ] );

		add_action( 'slim_seo_save', [ $this, 'save' ] );

		add_filter( 'slim_seo_upgradeable', '__return_false' );
	}

	public function add_tab( $tabs ) {
		$tabs['schema'] = __( 'Schema', 'slim-seo-schema' );
		return $tabs;
	}

	public function add_pane( $panes ) {
		$panes['schema'] = '<div id="schema" class="ss-tab-pane"></div>';
		return $panes;
	}

	public function enqueue() {
		wp_enqueue_style( 'slim-seo-settings', 'https://cdn.jsdelivr.net/gh/elightup/slim-seo@master/css/settings.min.css', [], SLIM_SEO_SCHEMA_VER );
		wp_enqueue_script( 'slim-seo-settings', 'https://cdn.jsdelivr.net/gh/elightup/slim-seo@master/js/settings.min.js', [], SLIM_SEO_SCHEMA_VER, true );

		wp_enqueue_style( 'slim-seo-react-tabs', 'https://cdn.jsdelivr.net/gh/elightup/slim-seo@master/css/react-tabs.min.css', [], SLIM_SEO_SCHEMA_VER );

		wp_enqueue_media();
		wp_enqueue_style( 'slim-seo-schema', SLIM_SEO_SCHEMA_URL . 'css/schema.css', [ 'wp-components' ], SLIM_SEO_SCHEMA_VER );

		wp_enqueue_script( 'slim-seo-schema', SLIM_SEO_SCHEMA_URL . 'js/schema.js', [ 'wp-element', 'wp-components', 'wp-i18n', 'wp-hooks' ], SLIM_SEO_SCHEMA_VER, true );

		$location_settings = new Location\Settings;
		$localized_data    = array_merge( $location_settings->get_localized_data(), [
			'rest'            => untrailingslashit( rest_url() ),
			'nonce'           => wp_create_nonce( 'wp_rest' ),
			'postTypes'       => $this->get_post_types(),
			'mediaPopupTitle' => __( 'Select An Image', 'slim-seo-schema' ),
		] );
		wp_localize_script( 'slim-seo-schema', 'SSSchema', $localized_data );

		do_action( 'slim_seo_schema_enqueue' );
		do_action( 'slim_seo_schema_enqueue_settings' );
	}

	public function get_post_types() {
		$post_types = get_post_types( [ 'public' => true ], 'objects' );
		unset( $post_types['attachment'] );
		$post_types = array_map( function( $post_type ) {
			return [
				'slug' => $post_type->name,
				'name' => $post_type->labels->singular_name,
			];
		}, $post_types );

		return array_values( $post_types );
	}

	public function save() {
		// @codingStandardsIgnoreLine.
		$schemas = isset( $_POST['schemas'] ) ? wp_unslash( $_POST['schemas'] ) : [];
		update_option( self::OPTION_NAME, $schemas );
	}

	public static function get_schemas(): array {
		return get_option( self::OPTION_NAME, Defaults::get() ) ?: [];
	}
}
