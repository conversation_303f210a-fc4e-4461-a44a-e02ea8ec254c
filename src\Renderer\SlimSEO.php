<?php
namespace SlimSEOPro\Schema\Renderer;

use SlimSEOPro\Schema\Support\Arr;

class SlimSEO extends Base {
	private $data = [];

	public function __construct() {
		add_filter( 'slim_seo_schema_graph', [ $this, 'merge_schemas' ] );
	}

	public function merge_schemas( array $data ): array {
		$graph = $this->get_graph();
		if ( empty( $graph ) ) {
			return $data;
		}

		$this->data = array_values( $data );

		foreach ( $this->data as &$schema ) {
			$index = $this->find_index( $graph, $schema['@type'] );

			if ( $index === -1 ) {
				continue;
			}

			$schema = Arr::merge_recursive( $schema, $graph[ $index ] );

			unset( $graph[ $index ] );
		}

		return array_values( array_merge( $this->data, $graph ) );
	}

	private function find_index( array $schemas, string $type ): int {
		$types = wp_list_pluck( $schemas, '@type' );
		$types = array_flip( $types );
		return $types[ $type ] ?? -1;
	}
}
